name: 'Lock Closed Issues'

on:
  schedule:
    - cron: '0 0 * * 0' # Every Sunday
  workflow_dispatch:

permissions:
  issues: write
  pull-requests: write

concurrency:
  group: lock

jobs:
  action:
    runs-on: ubuntu-latest
    steps:
      - name: Lock Issues
        if: github.repository == 'htr-tech/zphisher' && github.ref == 'refs/heads/master'
        uses: dessant/lock-threads@v3
        with:
          process-only: 'issues'
          issue-inactive-days: '30'
          exclude-any-issue-labels: 'enhancement'
          add-issue-labels: 'archived'
          remove-issue-labels: 'stale'
          issue-lock-reason: 'resolved'
          issue-comment: >
            This issue has been automatically locked since there
            has not been any recent activity after it was closed.
            Please open a new issue for related bugs/questions.
