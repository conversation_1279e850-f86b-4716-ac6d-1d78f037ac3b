name: Upload .DEB to Release

on:
  push:
    tags:
    - '*'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Build .DEB
        run: |
          sed -i 's/#PKG_NAME/PKG_NAME/' make-deb.sh
          bash make-deb.sh # Build for Debian
          bash make-deb.sh termux # Build for Termux
          find -type f -name "*.deb" -exec sha256sum "{}" > CHECKSUMS.txt \;

      - name: Upload to Release
        uses: softprops/action-gh-release@v1
        with:
          files: "*.deb"
      - uses: softprops/action-gh-release@v1
        with:
          files: CHECKSUMS.txt
