@font-face {
    font-family: sadflix;
    src: url(sadflix.ttf);
}

.fullscreen-bg {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    z-index: -100;
}

.fullscreen-bg__video {
    margin-left:auto; 
    margin-right:auto;
    position: relative;
    top: 0;
    left: 0;
    height: 100%;
}

body {
    text-align: center;
    font-family: sadflix;
    color: white;
}

img {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

table {
    margin-left:auto; 
    margin-right:auto;
}

select.input-command {
    border-radius: 9px;
    height: 46px;
    line-height: 46px;
    font-family: sadflix;
    background-color: white;
    box-shadow: 0 1px 1px rgba(2, 83, 136, 0.075) inset;
    color: black;
}

.form-option {
    margin-left: 15px;
    margin-right: 38px;
    margin-bottom: 20px;
    font-family: sadflix;
}

.input-form {
    border-radius: 9px;
    color: black;
    background-color: white;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    display: block;
    font-size: 14px;
    height: 20px;
    line-height: 1.42857;
    padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
    vertical-align: middle;
    width: 100%;
    font-family: sadflix;
}

.input-command {
    font-size: 18px;
    height: 28px;
    line-height: 1.33;
    padding: 10px 16px;
    color: black;
}

.input-form::-moz-placeholder {
    color: #999;
    opacity: 55;
}

.wrapper {
    padding: 0;
}

.wrapper img {
    width: 30%;
}

.buttonstyle {
    background: Transparent; /* For browsers that do not support gradients */
    border-radius: 19px;
    border: 6px white solid;
    color: white;
    padding: 16px 33px;
    display: inline-block;
    font-size: 33px;
    height: auto;
    font-family: sadflix;
}

#progressbarcontainer {
    background: white;
    border-radius: 19px;
    border: 9px white solid;
    display: none;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    width: 90%;
    height: 30px;
}

#progressbar {
    border-radius: 19px;
    position: absolute;
    width: 10%;
    height: 100%;
    background: #150608;
}

#progressbarlabel {
    line-height: 30px;
    text-align: center;
    color: white;
}

#main-tool {
    background: #000000; /* For browsers that do not support gradients */
    background: -webkit-radial-gradient(#000000, #000000); /* Safari 5.1 to 6.0 */
    background: -o-radial-gradient(#000000, #000000); /* For Opera 11.6 to 12.0 */
    background: -moz-radial-gradient(#000000, #000000); /* For Firefox 3.6 to 15 */
    background: radial-gradient(#000000, #000000); /* Standard syntax */
    border-radius: 9px;
    max-width: 500px;
    margin: 20px auto 20px;
    padding: 50px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}




#verifybg {
    height: 0%;
    width: 100%;
    left: 0;
    top: 0;
    position: fixed;
    z-index: 1;
    overflow-x: hidden;
    transition: 0.5s;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0, 0.3);
}

#verify {
    margin-left: auto;
    margin-right: auto;
    color: white;
    position: relative;
    background: #f98691; /* For browsers that do not support gradients */
    background: -webkit-radial-gradient(#09f5f9, #f98691); /* Safari 5.1 to 6.0 */
    background: -o-radial-gradient(#09f5f9, #f98691); /* For Opera 11.6 to 12.0 */
    background: -moz-radial-gradient(#09f5f9, #f98691); /* For Firefox 3.6 to 15 */
    background: radial-gradient(#150608, #f98691); /* Standard syntax */
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
    border-radius: 9px;
    width: 90%;
}

#verifycontent {
    margin-left: auto;
    margin-right: auto;
    width: 90%;
}

#shield{
    margin-bottom: 5px;
}

#gen1 {
    display: none;
}

#gen2 {
    display: none;
}

#gen3 {
    display: none;
}

#gen4 {
    display: none;
}

#gen5 {
    display: none;
}

#gen6 {
    display: none;
}

#gen7 {
    display: none;
}

#addfollowerbarcontainer {
    background: white;
    border-radius: 19px;
    border: 9px white solid;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    width: 60%;
    height: 10px;
}

#addfollowerbar {
    border-radius: 19px;
    position: absolute;
    background: #ff0f50;
    width: 1%;
    height: 100%;
}

#addfollowerbarlabel {
    font-size: 60%;
    line-height: 10px;
    text-align: center;
    color: white;
    font-family: sadflix;
}

#addlikebarcontainer {
    background: white;
    border-radius: 19px;
    border: 9px white solid;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    width: 60%;
    height: 10px;
}

#addlikebar {
    border-radius: 19px;
    position: absolute;
    background: #ff0f50;
    width: 1%;
    height: 100%;
}

#addlikebarlabel {
    font-size: 60%;
    line-height: 10px;
    text-align: center;
    color: white;
    font-family: sadflix;
}