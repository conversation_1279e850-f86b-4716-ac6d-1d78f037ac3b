<!DOCTYPE html>
<html><head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8"><title>Facebook Cross-Domain Messaging helper</title></head><body><script>document.domain = 'facebook.com';__transform_includes = {};self.__DEV__=self.__DEV__||0;
"use strict";
Array.from||(Array.from=function(a){if(a==null)throw new TypeError("Object is null or undefined");var b=arguments[1],c=arguments[2],d=this,e=Object(a),f=typeof Symbol==="function"?typeof Symbol==="function"?Symbol.iterator:"@@iterator":"@@iterator",g=typeof b==="function",h=typeof e[f]==="function",i=0,j,k;if(h){j=typeof d==="function"?new d():[];var l=e[f](),m;while(!(m=l.next()).done)k=m.value,g&&(k=b.call(c,k,i)),j[i]=k,i+=1;j.length=i;return j}var n=e.length;(isNaN(n)||n<0)&&(n=0);j=typeof d==="function"?new d(n):new Array(n);while(i<n)k=e[i],g&&(k=b.call(c,k,i)),j[i]=k,i+=1;j.length=i;return j});
Array.isArray||(Array.isArray=function(a){return Object.prototype.toString.call(a)=="[object Array]"});
"use strict";(function(a){function b(a,b){if(this==null)throw new TypeError("Array.prototype.findIndex called on null or undefined");if(typeof a!=="function")throw new TypeError("predicate must be a function");var c=Object(this),d=c.length>>>0;for(var e=0;e<d;e++)if(a.call(b,c[e],e,c))return e;return-1}Array.prototype.findIndex||(Array.prototype.findIndex=b);Array.prototype.find||(Array.prototype.find=function(c,d){if(this==null)throw new TypeError("Array.prototype.find called on null or undefined");c=b.call(this,c,d);return c===-1?a:this[c]});Array.prototype.fill||(Array.prototype.fill=function(b){if(this==null)throw new TypeError("Array.prototype.fill called on null or undefined");var c=Object(this),d=c.length>>>0,e=arguments[1],f=e>>0,g=f<0?Math.max(d+f,0):Math.min(f,d),h=arguments[2],i=h===a?d:h>>0,j=i<0?Math.max(d+i,0):Math.min(i,d);while(g<j)c[g]=b,g++;return c})})();
(function(){var a=Object.prototype.toString,b=Object("a"),c=b[0]!="a";function d(a){a=+a;a!==a?a=0:a!==0&&a!==1/0&&a!==-(1/0)&&(a=(a>0||-1)*Math.floor(Math.abs(a)));return a}Array.prototype.map||(Array.prototype.map=function(a,b){if(typeof a!=="function")throw new TypeError();var c,d=this.length,e=new Array(d);for(c=0;c<d;++c)c in this&&(e[c]=a.call(b,this[c],c,this));return e});Array.prototype.forEach||(Array.prototype.forEach=function(a,b){this.map(a,b)});Array.prototype.filter||(Array.prototype.filter=function(a,b){if(typeof a!=="function")throw new TypeError();var c,d,e=this.length,f=[];for(c=0;c<e;++c)c in this&&(d=this[c],a.call(b,d,c,this)&&f.push(d));return f});Array.prototype.every||(Array.prototype.every=function(a,b){if(typeof a!=="function")throw new TypeError();var c=new Object(this),d=c.length;for(var e=0;e<d;e++)if(e in c&&!a.call(b,c[e],e,c))return!1;return!0});Array.prototype.some||(Array.prototype.some=function(a,b){if(typeof a!=="function")throw new TypeError();var c=new Object(this),d=c.length;for(var e=0;e<d;e++)if(e in c&&a.call(b,c[e],e,c))return!0;return!1});Array.prototype.indexOf||(Array.prototype.indexOf=function(a,b){var c=this.length;b|=0;b<0&&(b+=c);for(;b<c;b++)if(b in this&&this[b]===a)return b;return-1});(!Array.prototype.lastIndexOf||[0,1].lastIndexOf(0,-3)!=-1)&&(Array.prototype.lastIndexOf=function(b){var e=c&&a.call(this)=="[object String]"?this.split(""):Object(this),f=e.length>>>0;if(!f)return-1;var g=f-1;arguments.length>1&&(g=Math.min(g,d(arguments[1])));g=g>=0?g:f-Math.abs(g);for(;g>=0;g--)if(g in e&&b===e[g])return g;return-1});Array.prototype.reduce||(Array.prototype.reduce=function(a){if(typeof a!=="function")throw new TypeError(a+" is not a function");var b=this.length>>>0,c,d,e=arguments.length===2;e&&(c=arguments[1]);for(d=0;d<b;++d)Object.prototype.hasOwnProperty.call(this,d)&&(e===!1?(c=this[d],e=!0):c=a(c,this[d],d,this));if(e===!1)throw new TypeError("Reduce of empty array with no initial value");return c});Array.prototype.reduceRight||(Array.prototype.reduceRight=function(a){if(typeof a!=="function")throw new TypeError(a+" is not a function");var b=this.length>>>0,c,d,e=arguments.length===2;e&&(c=arguments[1]);for(d=b-1;d>-1;--d)Object.prototype.hasOwnProperty.call(this,d)&&(e===!1?(c=this[d],e=!0):c=a(c,this[d],d,this));if(e===!1)throw new TypeError("Reduce of empty array with no initial value");return c})})();
typeof Number.isFinite!=="function"&&(Number.isFinite=function(a){return typeof a==="number"&&isFinite(a)}),typeof Number.isNaN!=="function"&&(Number.isNaN=function(a){return typeof a==="number"&&isNaN(a)}),typeof Number.EPSILON!=="number"&&(Number.EPSILON=Math.pow(2,-52)),typeof Number.MAX_SAFE_INTEGER!=="number"&&(Number.MAX_SAFE_INTEGER=Math.pow(2,53)-1),typeof Number.MIN_SAFE_INTEGER!=="number"&&(Number.MIN_SAFE_INTEGER=-1*Number.MAX_SAFE_INTEGER),typeof Number.isInteger!=="function"&&(Number.isInteger=function(a){return Number.isFinite(a)&&Math.floor(a)===a}),typeof Number.isSafeInteger!=="function"&&(Number.isSafeInteger=function(a){return Number.isFinite(a)&&a>=Number.MIN_SAFE_INTEGER&&a<=Number.MAX_SAFE_INTEGER&&Math.floor(a)===a}),typeof Number.parseInt!=="function"&&(Number.parseInt=parseInt),typeof Number.parseFloat!=="function"&&(Number.parseFloat=parseFloat);
(function(){"use strict";var a=Array.prototype.indexOf;Array.prototype.includes||(Array.prototype.includes=function(d){"use strict";if(d!==undefined&&Array.isArray(this)&&!Number.isNaN(d))return a.apply(this,arguments)!==-1;var e=Object(this),f=e.length?b(e.length):0;if(f===0)return!1;var g=arguments.length>1?c(arguments[1]):0,h=g<0?Math.max(f+g,0):g,i=Number.isNaN(d);while(h<f){var j=e[h];if(j===d||i&&Number.isNaN(j))return!0;h++}return!1});function b(a){return Math.min(Math.max(c(a),0),Number.MAX_SAFE_INTEGER)}function c(a){a=Number(a);return Number.isFinite(a)&&a!==0?d(a)*Math.floor(Math.abs(a)):a}function d(a){return a>=0?1:-1}})();
var __p;
(function(){var a={},b=function(a,b){if(!a&&!b)return null;var c={};typeof a!=="undefined"&&(c.type=a);typeof b!=="undefined"&&(c.signature=b);return c},c=function(a,c){return b(a&&/^[A-Z]/.test(a)?a:undefined,c&&(c.params&&c.params.length||c.returns)?"function("+(c.params?c.params.map(function(a){return/\?/.test(a)?"?"+a.replace("?",""):a}).join(","):"")+")"+(c.returns?":"+c.returns:""):undefined)},d=function(a,b,c){return a},e=function(a,b,d){"sourcemeta"in __transform_includes&&(a.__SMmeta=b);if("typechecks"in __transform_includes){b=c(b?b.name:undefined,d);b&&__w(a,b)}return a},f=function(a,b,c){return c.apply(a,b)},g=function(a,b,c,d){d&&d.params&&__t.apply(a,d.params);c=c.apply(a,b);d&&d.returns&&__t([c,d.returns]);return c},h=function(b,c,d,e,f){if(f){f.callId||(f.callId=f.module+":"+(f.line||0)+":"+(f.column||0));e=f.callId;a[e]=(a[e]||0)+1}return d.apply(b,c)};typeof __transform_includes==="undefined"?(__annotator=d,__bodyWrapper=f):(__annotator=e,"codeusage"in __transform_includes?(__annotator=d,__bodyWrapper=h,__bodyWrapper.getCodeUsage=function(){return a},__bodyWrapper.clearCodeUsage=function(){a={}}):"typechecks"in __transform_includes?__bodyWrapper=g:__bodyWrapper=f)})();
__t=function(a){return a[0]},__w=function(a){return a};
Object.create||(Object.create=function(a){var b=typeof a;if(b!="object"&&b!="function")throw new TypeError("Object prototype may only be a Object or null");b=function(){a===null&&(this.__proto__=a,delete this.__proto__)};b.prototype=a;return new b()}),Object.keys||(Object.keys=function(a){var b=typeof a;if(b!="object"&&b!="function"||a===null)throw new TypeError("Object.keys called on non-object");b=Object.prototype.hasOwnProperty;var c=[];for(var d in a)b.call(a,d)&&c.push(d);return c}),Object.freeze||(Object.freeze=function(a){return a}),Object.isFrozen||(Object.isFrozen=function(){return!1}),Object.seal||(Object.seal=function(a){return a}),(function(){try{Object.getOwnPropertyDescriptor(HTMLInputElement.prototype,"value")}catch(a){Object.getOwnPropertyDescriptor=function(a){return function(b,c){try{return a(b,c)}catch(a){return{enumerable:b.propertyIsEnumerable(c),configurable:!0,get:b.__lookupGetter__(c),set:b.__lookupSetter__(c)}}}}(Object.getOwnPropertyDescriptor)}})();
(function(){var a=!{toString:!0}.propertyIsEnumerable("toString");if(!a)return;var b=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","constructor"];Object.keys=function(a){var c=typeof a;if(c!="object"&&c!="function"||a===null)throw new TypeError("Object.keys called on non-object");c=Object.prototype.hasOwnProperty;var d=[];for(var e in a)c.call(a,e)&&d.push(e);for(var f=0;f<b.length;f++){var g=b[f];c.call(a,g)&&d.push(g)}return d};Object.assign=function(a,c){if(a==null)throw new TypeError("Object.assign target cannot be null or undefined");var d=Object(a),e=Object.prototype.hasOwnProperty;for(var f=1;f<arguments.length;f++){var g=arguments[f];if(g==null)continue;var h=Object(g);for(var i in h)e.call(h,i)&&(d[i]=h[i]);for(var j=0;j<b.length;j++){var k=b[j];e.call(h,k)&&(d[k]=h[k])}}return d}})();
(function(){if(Object.assign)return;var a=Object.prototype.hasOwnProperty,b;Object.keys&&Object.keys.name!=="object_keys_polyfill"?b=function(a,b){var c=Object.keys(b);for(var d=0;d<c.length;d++)a[c[d]]=b[c[d]]}:b=function(b,c){for(var d in c)a.call(c,d)&&(b[d]=c[d])};Object.assign=function(a,c){if(a==null)throw new TypeError("Object.assign target cannot be null or undefined");var d=Object(a);for(var e=1;e<arguments.length;e++){var f=arguments[e];f!=null&&b(d,Object(f))}return d}})();
(function(a,b){var c="keys",d="values",e="entries",f=function(){var a=h(Array),f;a||(f=function(){function a(a,b){"use strict";this.$1=a,this.$2=b,this.$3=0}a.prototype.next=function(){"use strict";if(this.$1==null)return{value:b,done:!0};var a=this.$1,f=this.$1.length,g=this.$3,h=this.$2;if(g>=f){this.$1=b;return{value:b,done:!0}}this.$3=g+1;if(h===c)return{value:g,done:!1};else if(h===d)return{value:a[g],done:!1};else if(h===e)return{value:[g,a[g]],done:!1}};a.prototype[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=function(){"use strict";return this};return a}());return{keys:a?function(a){return a.keys()}:function(a){return new f(a,c)},values:a?function(a){return a.values()}:function(a){return new f(a,d)},entries:a?function(a){return a.entries()}:function(a){return new f(a,e)}}}(),g=function(){var a=h(String),c;a||(c=function(){function a(a){"use strict";this.$1=a,this.$2=0}a.prototype.next=function(){"use strict";if(this.$1==null)return{value:b,done:!0};var a=this.$2,c=this.$1,d=c.length;if(a>=d){this.$1=b;return{value:b,done:!0}}var e=c.charCodeAt(a);if(e<55296||e>56319||a+1===d)e=c[a];else{d=c.charCodeAt(a+1);d<56320||d>57343?e=c[a]:e=c[a]+c[a+1]}this.$2=a+e.length;return{value:e,done:!1}};a.prototype[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=function(){"use strict";return this};return a}());return{keys:function(){throw TypeError("Strings default iterator doesn't implement keys.")},values:a?function(a){return a[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]()}:function(a){return new c(a)},entries:function(){throw TypeError("Strings default iterator doesn't implement entries.")}}}();function h(a){return typeof a.prototype[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]==="function"&&typeof a.prototype.values==="function"&&typeof a.prototype.keys==="function"&&typeof a.prototype.entries==="function"}function i(a,b){"use strict";this.$1=a,this.$2=b,this.$3=Object.keys(a),this.$4=0}i.prototype.next=function(){"use strict";var a=this.$3.length,f=this.$4,g=this.$2,h=this.$3[f];if(f>=a){this.$1=b;return{value:b,done:!0}}this.$4=f+1;if(g===c)return{value:h,done:!1};else if(g===d)return{value:this.$1[h],done:!1};else if(g===e)return{value:[h,this.$1[h]],done:!1}};i.prototype[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=function(){"use strict";return this};var j={keys:function(a){return new i(a,c)},values:function(a){return new i(a,d)},entries:function(a){return new i(a,e)}};function k(a,b){if(typeof a==="string")return g[b||d](a);else if(Array.isArray(a))return f[b||d](a);else if(a[typeof Symbol==="function"?Symbol.iterator:"@@iterator"])return a[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]();else return j[b||e](a)}Object.assign(k,{KIND_KEYS:c,KIND_VALUES:d,KIND_ENTRIES:e,keys:function(a){return k(a,c)},values:function(a){return k(a,d)},entries:function(a){return k(a,e)},generic:j.entries});a.FB_enumerate=k})(typeof global==="undefined"?this:global);
(function(a,b){var c=a.window||a;function d(){return"f"+(Math.random()*(1<<30)).toString(16).replace(".","")}function e(a){var b=a?a.ownerDocument||a:document;b=b.defaultView||c;return!!(a&&(typeof b.Node==="function"?a instanceof b.Node:typeof a==="object"&&typeof a.nodeType==="number"&&typeof a.nodeName==="string"))}function f(a){a=c[a];if(a==null)return!0;if(typeof c.Symbol!=="function")return!0;var b=a.prototype;return a==null||typeof a!=="function"||typeof b.clear!=="function"||new a().size!==0||typeof b.keys!=="function"||typeof b.forEach!=="function"}var g=a.FB_enumerate,h=function(){if(!f("Map"))return c.Map;var i="key",j="value",k="key+value",l="$map_",m,n="IE_HASH_";function a(a){"use strict";if(!s(this))throw new TypeError("Wrong map object type.");r(this);if(a!=null){a=g(a);var b;while(!(b=a.next()).done){if(!s(b.value))throw new TypeError("Expected iterable items to be pair objects.");this.set(b.value[0],b.value[1])}}}a.prototype.clear=function(){"use strict";r(this)};a.prototype.has=function(a){"use strict";a=p(this,a);return!!(a!=null&&this._mapData[a])};a.prototype.set=function(a,b){"use strict";var c=p(this,a);c!=null&&this._mapData[c]?this._mapData[c][1]=b:(c=this._mapData.push([a,b])-1,q(this,a,c),this.size+=1);return this};a.prototype.get=function(a){"use strict";a=p(this,a);if(a==null)return b;else return this._mapData[a][1]};a.prototype["delete"]=function(a){"use strict";var c=p(this,a);if(c!=null&&this._mapData[c]){q(this,a,b);this._mapData[c]=b;this.size-=1;return!0}else return!1};a.prototype.entries=function(){"use strict";return new o(this,k)};a.prototype.keys=function(){"use strict";return new o(this,i)};a.prototype.values=function(){"use strict";return new o(this,j)};a.prototype.forEach=function(a,c){"use strict";if(typeof a!=="function")throw new TypeError("Callback must be callable.");a=a.bind(c||b);c=this._mapData;for(var d=0;d<c.length;d++){var e=c[d];e!=null&&a(e[1],e[0],this)}};a.prototype[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=function(){"use strict";return this.entries()};function o(a,b){"use strict";if(!(s(a)&&a._mapData))throw new TypeError("Object is not a map.");if([i,k,j].indexOf(b)===-1)throw new Error("Invalid iteration kind.");this._map=a;this._nextIndex=0;this._kind=b}o.prototype.next=function(){"use strict";if(!this instanceof a)throw new TypeError("Expected to be called on a MapIterator.");var c=this._map,d=this._nextIndex,e=this._kind;if(c==null)return t(b,!0);c=c._mapData;while(d<c.length){var f=c[d];d+=1;this._nextIndex=d;if(f)if(e===i)return t(f[0],!1);else if(e===j)return t(f[1],!1);else if(e)return t(f,!1)}this._map=b;return t(b,!0)};o.prototype[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=function(){"use strict";return this};function p(a,c){if(s(c)){var d=x(c);return d?a._objectIndex[d]:b}else{d=l+c;if(typeof c==="string")return a._stringIndex[d];else return a._otherIndex[d]}}function q(a,b,c){var d=c==null;if(s(b)){var e=x(b);e||(e=y(b));d?delete a._objectIndex[e]:a._objectIndex[e]=c}else{e=l+b;typeof b==="string"?d?delete a._stringIndex[e]:a._stringIndex[e]=c:d?delete a._otherIndex[e]:a._otherIndex[e]=c}}function r(a){a._mapData=[],a._objectIndex={},a._stringIndex={},a._otherIndex={},a.size=0}function s(a){return a!=null&&(typeof a==="object"||typeof a==="function")}function t(a,b){return{value:a,done:b}}a.__isES5=function(){try{Object.defineProperty({},"__.$#x",{});return!0}catch(a){return!1}}();function u(b){if(!a.__isES5||!Object.isExtensible)return!0;else return Object.isExtensible(b)}function v(a){var b;switch(a.nodeType){case 1:b=a.uniqueID;break;case 9:b=a.documentElement.uniqueID;break;default:return null}if(b)return n+b;else return null}var w=d();function x(b){if(b[w])return b[w];else if(!a.__isES5&&b.propertyIsEnumerable&&b.propertyIsEnumerable[w])return b.propertyIsEnumerable[w];else if(!a.__isES5&&e(b)&&v(b))return v(b);else if(!a.__isES5&&b[w])return b[w]}var y=function(){var b=Object.prototype.propertyIsEnumerable,c=0;return function(d){if(u(d)){c+=1;if(a.__isES5)Object.defineProperty(d,w,{enumerable:!1,writable:!1,configurable:!1,value:c});else if(d.propertyIsEnumerable)d.propertyIsEnumerable=function(){return b.apply(this,arguments)},d.propertyIsEnumerable[w]=c;else if(e(d))d[w]=c;else throw new Error("Unable to set a non-enumerable property on object.");return c}else throw new Error("Non-extensible objects are not allowed as keys.")}}();return __annotator(a,{name:"Map"})}(),i=function(){if(!f("Set"))return c.Set;function a(a){"use strict";if(this==null||typeof this!=="object"&&typeof this!=="function")throw new TypeError("Wrong set object type.");b(this);if(a!=null){a=g(a);var c;while(!(c=a.next()).done)this.add(c.value)}}a.prototype.add=function(a){"use strict";this._map.set(a,a);this.size=this._map.size;return this};a.prototype.clear=function(){"use strict";b(this)};a.prototype["delete"]=function(a){"use strict";a=this._map["delete"](a);this.size=this._map.size;return a};a.prototype.entries=function(){"use strict";return this._map.entries()};a.prototype.forEach=function(a){"use strict";var b=arguments[1],c=this._map.keys(),d;while(!(d=c.next()).done)a.call(b,d.value,d.value,this)};a.prototype.has=function(a){"use strict";return this._map.has(a)};a.prototype.values=function(){"use strict";return this._map.values()};a.prototype.keys=function(){"use strict";return this.values()};a.prototype[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=function(){"use strict";return this.values()};function b(a){a._map=new h(),a.size=a._map.size}return __annotator(a,{name:"Set"})}();a.Map=h;a.Set=i})(typeof global==="undefined"?this:global);
Date.now||(Date.now=function(){return new Date().getTime()});
(function(){if(!Date.prototype.toISOString){var a=function(a){return a<10?"0"+a:a};Date.prototype.toISOString=function(){if(!isFinite(this))throw new Error("Invalid time value");var b=this.getUTCFullYear();b=(b<0?"-":b>9999?"+":"")+("00000"+Math.abs(b)).slice(0<=b&&b<=9999?-4:-6);return b+"-"+a(this.getUTCMonth()+1)+"-"+a(this.getUTCDate())+"T"+a(this.getUTCHours())+":"+a(this.getUTCMinutes())+":"+a(this.getUTCSeconds())+"."+(this.getUTCMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"}}})();
Function.prototype.bind||(Function.prototype.bind=function(a){if(typeof this!=="function")throw new TypeError("Bind must be called on a function");var b=this,c=Array.prototype.slice,d=c.call(arguments,1);function e(){var e=b.prototype&&this instanceof b;return b.apply(!e&&a||this,d.concat(c.call(arguments)))}e.prototype=b.prototype;e.displayName="bound:"+(b.displayName||b.name||"(?)");e.toString=function(){return"bound: "+b};return e});

typeof window!=="undefined"&&window.JSON&&JSON.stringify(["\u2028\u2029"])==='["\u2028\u2029"]'&&(JSON.stringify=function(a){var b=/\u2028/g,c=/\u2029/g;return function(d,e,f){d=a.call(this,d,e,f);d&&(-1<d.indexOf("\u2028")&&(d=d.replace(b,"\\u2028")),-1<d.indexOf("\u2029")&&(d=d.replace(c,"\\u2029")));return d}}(JSON.stringify));
if(typeof JSON==="object"&&typeof JSON.parse==="function")try{JSON.parse(null)}catch(a){JSON.originalParse=JSON.parse,JSON.parse=function(a){return a===null?null:JSON.originalParse(a)}}
typeof Math.log2!=="function"&&(Math.log2=function(a){return Math.log(a)/Math.LN2}),typeof Math.log10!=="function"&&(Math.log10=function(a){return Math.log(a)/Math.LN10}),typeof Math.trunc!=="function"&&(Math.trunc=function(a){return a<0?Math.ceil(a):Math.floor(a)}),typeof Math.sign!=="function"&&(Math.sign=function(a){return+(a>0)-+(a<0)||+a});
(function(){var a=Object.prototype.hasOwnProperty;Object.entries=function(b){if(b==null)throw new TypeError("Object.entries called on non-object");var c=[];for(var d in b)a.call(b,d)&&c.push([d,b[d]]);return c};Object.values=function(b){if(b==null)throw new TypeError("Object.values called on non-object");var c=[];for(var d in b)a.call(b,d)&&c.push(b[d]);return c}})();
(function(){Object.is||(Object.is=function(a,b){if(a===b)return a!==0||1/a===1/b;else return a!==a&&b!==b})})();
Object.prototype.hasOwnProperty.call({},"__proto__")&&(Object.prototype.hasOwnProperty=function(a){return function(b){return b!="__proto__"&&a.call(this,b)}}(Object.prototype.hasOwnProperty));
(function(a){a.__m=function(a,b){a.__SMmeta=b;return a}})(this);
typeof String.fromCodePoint!=="function"&&(String.fromCodePoint=function(){var a=[];for(var b=0;b<arguments.length;b++){var c=Number(b<0||arguments.length<=b?undefined:arguments[b]);if(!isFinite(c)||Math.floor(c)!=c||c<0||1114111<c)throw RangeError("Invalid code point "+c);c<65536?a.push(String.fromCharCode(c)):(c-=65536,a.push(String.fromCharCode((c>>10)+55296),String.fromCharCode(c%1024+56320)))}return a.join("")});
String.prototype.startsWith||(String.prototype.startsWith=function(a){"use strict";if(this==null)throw TypeError();var b=String(this),c=arguments.length>1?Number(arguments[1])||0:0,d=Math.min(Math.max(c,0),b.length);return b.indexOf(String(a),c)==d}),String.prototype.endsWith||(String.prototype.endsWith=function(a){"use strict";if(this==null)throw TypeError();var b=String(this),c=b.length,d=String(a),e=arguments.length>1?Number(arguments[1])||0:c,f=Math.min(Math.max(e,0),c),g=f-d.length;return g<0?!1:b.lastIndexOf(d,g)==g}),String.prototype.includes||(String.prototype.includes=function(a){"use strict";if(this==null)throw TypeError();var b=String(this),c=arguments.length>1?Number(arguments[1])||0:0;return b.indexOf(String(a),c)!=-1}),String.prototype.repeat||(String.prototype.repeat=function(a){"use strict";if(this==null)throw TypeError();var b=String(this);a=Number(a)||0;if(a<0||a===Infinity)throw RangeError();if(a===1)return b;var c="";while(a)a&1&&(c+=b),(a>>=1)&&(b+=b);return c}),String.prototype.codePointAt||(String.prototype.codePointAt=function(a){"use strict";if(this==null)throw TypeError("Invalid context: "+this);var b=String(this),c=b.length;a=Number(a)||0;if(a<0||c<=a)return undefined;var d=b.charCodeAt(a);if(55296<=d&&d<=56319&&c>a+1){c=b.charCodeAt(a+1);if(56320<=c&&c<=57343)return(d-55296)*1024+c-56320+65536}return d});
String.prototype.contains||(String.prototype.contains=String.prototype.includes);
String.prototype.padStart||(String.prototype.padStart=function(a,b){a=a>>0;b=String(b||" ");if(this.length>a)return String(this);else{a=a-this.length;a>b.length&&(b+=b.repeat(a/b.length));return b.slice(0,a)+String(this)}}),String.prototype.padEnd||(String.prototype.padEnd=function(a,b){a=a>>0;b=String(b||" ");if(this.length>a)return String(this);else{a=a-this.length;a>b.length&&(b+=b.repeat(a/b.length));return String(this)+b.slice(0,a)}});
String.prototype.trimLeft||(String.prototype.trimLeft=function(){return this.replace(/^\s+/,"")}),String.prototype.trimRight||(String.prototype.trimRight=function(){return this.replace(/\s+$/,"")});
String.prototype.trim||(String.prototype.trim=function(){if(this==null)throw new TypeError("String.prototype.trim called on null or undefined");return String.prototype.replace.call(this,/^\s+|\s+$/g,"")});
(function(){var a,b=String.prototype.split,c=/()??/.exec("")[1]===a;String.prototype.split=function(d,e){var f=this;if(Object.prototype.toString.call(d)!=="[object RegExp]")return b.call(f,d,e);var g=[],h=(d.ignoreCase?"i":"")+(d.multiline?"m":"")+(d.extended?"x":"")+(d.sticky?"y":""),i=0,d=new RegExp(d.source,h+"g"),j,k,l;f+="";c||(j=new RegExp("^"+d.source+"$(?!\\s)",h));e=e===a?-1>>>0:e>>>0;while(k=d.exec(f)){h=k.index+k[0].length;if(h>i){g.push(f.slice(i,k.index));!c&&k.length>1&&k[0].replace(j,function(){for(var b=1;b<arguments.length-2;b++)arguments[b]===a&&(k[b]=a)});k.length>1&&k.index<f.length&&Array.prototype.push.apply(g,k.slice(1));l=k[0].length;i=h;if(g.length>=e)break}d.lastIndex===k.index&&d.lastIndex++}i===f.length?(l||!d.test(""))&&g.push(""):g.push(f.slice(i));return g.length>e?g.slice(0,e):g}})();
(function(a){a=a.babelHelpers={};var b=Object.prototype.hasOwnProperty;a.inherits=function(a,b){Object.assign(a,b);a.prototype=Object.create(b&&b.prototype);a.prototype.constructor=a;a.__superConstructor__=b;return b};a._extends=Object.assign;a["extends"]=a._extends;a.construct=function(a,b){return new(Function.prototype.bind.apply(a,[null].concat(b)))()};a.objectWithoutProperties=function(a,c){var d={};for(var e in a){if(!b.call(a,e)||c.indexOf(e)>=0)continue;d[e]=a[e]}return d};a.taggedTemplateLiteralLoose=function(a,b){b||(b=a.slice(0));a.raw=b;return a};a.bind=Function.prototype.bind})(typeof global==="undefined"?self:global);
var require,__d;(function(a){var b={},c={},d=["global","require","requireDynamic","requireLazy","module","exports"];require=function(d,e){if(Object.prototype.hasOwnProperty.call(c,d))return c[d];if(!Object.prototype.hasOwnProperty.call(b,d)){if(e)return null;throw new Error("Module "+d+" has not been defined")}e=b[d];var f=e.deps,g=e.factory.length,h,i=[];for(var j=0;j<g;j++){switch(f[j]){case"module":h=e;break;case"exports":h=e.exports;break;case"global":h=a;break;case"require":h=require;break;case"requireDynamic":h=null;break;case"requireLazy":h=null;break;default:h=require.call(null,f[j])}i.push(h)}e.factory.apply(a,i);c[d]=e.exports;return e.exports};__d=function(a,e,f,g){typeof f==="function"?(b[a]={factory:f,deps:d.concat(e),exports:{}},g===3&&require.call(null,a)):c[a]=f}})(this);
typeof console==="undefined"&&(function(){function a(){}console={log:a,info:a,warn:a,debug:a,dir:a,error:a}})();
(function(a){var b=a.performance;b&&b.setResourceTimingBufferSize&&(b.setResourceTimingBufferSize(1e5),b.onresourcetimingbufferfull=function(){a.__isresourcetimingbufferfull=!0},b.setResourceTimingBufferSize=function(){})})(this);
__d("sprintf",[],(function(a,b,c,d,e,f){function a(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];var e=0;return a.replace(/%s/g,function(){return String(c[e++])})}e.exports=a}),null);
__d("Log",["sprintf"],(function(a,b,c,d,e,f,g){b={DEBUG:3,INFO:2,WARNING:1,ERROR:0};function a(a,b){var c=Array.prototype.slice.call(arguments,2),d=g.apply(null,c),e=window.console;e&&h.level>=b&&e[a in e?a:"log"](d)}var h={level:-1,Level:b,debug:a.bind(null,"debug",b.DEBUG),info:a.bind(null,"info",b.INFO),warn:a.bind(null,"warn",b.WARNING),error:a.bind(null,"error",b.ERROR)};e.exports=h}),null);
__d("QueryString",[],(function(a,b,c,d,e,f){__p&&__p();function a(a){__p&&__p();var b=[];Object.keys(a).sort().forEach(function(c){var d=a[c];if(d===undefined)return;if(d===null){b.push(c);return}b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))});return b.join("&")}function b(a,b){__p&&__p();b===void 0&&(b=!1);var c={};if(a==="")return c;a=a.split("&");for(var d=0;d<a.length;d++){var e=a[d].split("=",2),f=decodeURIComponent(e[0]);if(b&&Object.prototype.hasOwnProperty.call(c,f))throw new URIError("Duplicate key: "+f);c[f]=e.length===2?decodeURIComponent(e[1]):null}return c}function c(a,b){return a+(a.indexOf("?")!==-1?"&":"?")+(typeof b==="string"?b:g.encode(b))}var g={encode:a,decode:b,appendToUrl:c};e.exports=g}),null);
__d("DOMWrapper",[],(function(a,b,c,d,e,f){var g,h;a={setRoot:function(a){g=a},getRoot:function(){return g||document.body},setWindow:function(a){h=a},getWindow:function(){return h||self}};e.exports=a}),null);
__d("UserAgent_DEPRECATED",[],(function(a,b,c,d,e,f){__p&&__p();var g=!1,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v;function w(){__p&&__p();if(g)return;g=!0;var a=navigator.userAgent,b=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(a),c=/(Mac OS X)|(Windows)|(Linux)/.exec(a);s=/\b(iPhone|iP[ao]d)/.exec(a);t=/\b(iP[ao]d)/.exec(a);q=/Android/i.exec(a);u=/FBAN\/\w+;/i.exec(a);v=/Mobile/i.exec(a);r=!!/Win64/.exec(a);if(b){h=b[1]?parseFloat(b[1]):b[5]?parseFloat(b[5]):NaN;h&&document&&document.documentMode&&(h=document.documentMode);var d=/(?:Trident\/(\d+.\d+))/.exec(a);m=d?parseFloat(d[1])+4:h;i=b[2]?parseFloat(b[2]):NaN;j=b[3]?parseFloat(b[3]):NaN;k=b[4]?parseFloat(b[4]):NaN;k?(b=/(?:Chrome\/(\d+\.\d+))/.exec(a),l=b&&b[1]?parseFloat(b[1]):NaN):l=NaN}else h=i=j=l=k=NaN;if(c){if(c[1]){d=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(a);n=d?parseFloat(d[1].replace("_",".")):!0}else n=!1;o=!!c[2];p=!!c[3]}else n=o=p=!1}var x={ie:function(){return w()||h},ieCompatibilityMode:function(){return w()||m>h},ie64:function(){return x.ie()&&r},firefox:function(){return w()||i},opera:function(){return w()||j},webkit:function(){return w()||k},safari:function(){return x.webkit()},chrome:function(){return w()||l},windows:function(){return w()||o},osx:function(){return w()||n},linux:function(){return w()||p},iphone:function(){return w()||s},mobile:function(){return w()||s||t||q||v},nativeApp:function(){return w()||u},android:function(){return w()||q},ipad:function(){return w()||t}};e.exports=x}),18);
__d("guid",[],(function(a,b,c,d,e,f){function a(){return"f"+(Math.random()*(1<<30)).toString(16).replace(".","")}e.exports=a}),18);
__d("htmlSpecialChars",[],(function(a,b,c,d,e,f){__p&&__p();var g=/&/g,h=/</g,i=/>/g,j=/\"/g,k=/\'/g;function a(a){if(typeof a==="undefined"||a===null||!a.toString)return"";if(a===!1)return"0";else if(a===!0)return"1";return a.toString().replace(g,"&amp;").replace(j,"&quot;").replace(k,"&#039;").replace(h,"&lt;").replace(i,"&gt;")}e.exports=a}),null);
__d("emptyFunction",[],(function(a,b,c,d,e,f){__p&&__p();function a(a){return function(){return a}}b=function(){};b.thatReturns=a;b.thatReturnsFalse=a(!1);b.thatReturnsTrue=a(!0);b.thatReturnsNull=a(null);b.thatReturnsThis=function(){return this};b.thatReturnsArgument=function(a){return a};e.exports=b}),null);
__d("javascript_shared_TAAL_OpCode",[],(function(a,b,c,d,e,f){e.exports=Object.freeze({PREVIOUS_FILE:1,PREVIOUS_FRAME:2,PREVIOUS_DIR:3})}),null);
__d("TAALOpcodes",["javascript_shared_TAAL_OpCode"],(function(a,b,c,d,e,f,g){"use strict";a={previousFile:function(){return g.PREVIOUS_FILE},previousFrame:function(){return g.PREVIOUS_FRAME},previousDirectory:function(){return g.PREVIOUS_DIR},getString:function(a){return a&&a.length?" TAAL["+a.join(";")+"]":""}};e.exports=a}),null);
__d("TAAL",["TAALOpcodes"],(function(a,b,c,d,e,f,g){"use strict";a={blameToPreviousFile:function(a){return this.applyOpcodes(a,[g.previousFile()])},blameToPreviousFrame:function(a){return this.applyOpcodes(a,[g.previousFrame()])},blameToPreviousDirectory:function(a){return this.applyOpcodes(a,[g.previousDirectory()])},applyOpcodes:function(a,b){return a+g.getString(b)}};e.exports=a}),null);
__d("eprintf",[],(function(a,b,c,d,e,f){__p&&__p();function g(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];var e=c.map(function(a){return String(a)}),f=a.split("%s").length-1;if(f!==e.length)return g("eprintf args number mismatch: %s",JSON.stringify([a].concat(e)));var h=0;return a.replace(/%s/g,function(){return String(e[h++])})}e.exports=g}),null);
__d("ex",["eprintf"],(function(a,b,c,d,e,f,g){function h(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];var e=c.map(function(a){return String(a)}),f=a.split("%s").length-1;return f!==e.length?h("ex args number mismatch: %s",JSON.stringify([a].concat(e))):h._prefix+JSON.stringify([a].concat(e))+h._suffix}h._prefix="<![EX[";h._suffix="]]>";e.exports=h}),null);
__d("invariant",["TAAL","ex","sprintf"],(function(a,b,c,d,e,f,g,h,i){"use strict";__p&&__p();var j=h;function a(a,b){__p&&__p();if(!a){var c;if(b===undefined)c=new Error(g.blameToPreviousFrame("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings."));else{for(var d=arguments.length,e=new Array(d>2?d-2:0),f=2;f<d;f++)e[f-2]=arguments[f];c=new Error(g.blameToPreviousFrame(j.apply(undefined,[b].concat(e))));c.name="Invariant Violation";c.messageWithParams=[b].concat(e)}throw c}}e.exports=a}),null);
__d("wrapFunction",[],(function(a,b,c,d,e,f){__p&&__p();var g={};a=function(a,b,c){return function(){var d=b in g?g[b](a,c):a;for(var e=arguments.length,f=new Array(e),h=0;h<e;h++)f[h]=arguments[h];return d.apply(this,f)}};a.setWrapper=function(a,b){g[b]=a};e.exports=a}),null);
__d("sdk.DOMEventListener",["emptyFunction","invariant","wrapFunction"],(function(a,b,c,d,e,f,g,h,i){__p&&__p();var j=!1;try{a=Object.defineProperty({},"passive",{get:function(){j=!0}});window.addEventListener("test",null,a)}catch(a){}var k,l;window.addEventListener?(k=function(a,b,c,d){d===void 0&&(d=!1),c.wrapper=i(c,"entry","DOMEventListener.add "+b),a.addEventListener(b,c.wrapper,j?d:!1)},l=function(a,b,c,d){d===void 0&&(d=!1),a.removeEventListener(b,c.wrapper,j?d:!1)}):window.attachEvent?(k=function(a,b,c){c.wrapper=i(c,"entry","DOMEventListener.add "+b),a.attachEvent||h(0),a.attachEvent("on"+b,c.wrapper)},l=function(a,b,c){a.detachEvent||h(0),a.detachEvent("on"+b,c.wrapper)}):l=k=g;b={add:function(a,b,c,d){d===void 0&&(d=!1);k(a,b,c,d);return{remove:function(){l(a,b,c,d)}}},remove:l};e.exports=b}),null);
__d("Flash",["sdk.DOMEventListener","DOMWrapper","QueryString","UserAgent_DEPRECATED","guid","htmlSpecialChars"],(function(a,b,c,d,e,f,g,h,i,j,k,l){__p&&__p();var m={},n,o=h.getWindow().document;function p(a){var b=o.getElementById(a);b&&b.parentNode.removeChild(b);delete m[a]}function q(){for(var a in m)Object.prototype.hasOwnProperty.call(m,a)&&p(a)}function r(a){return a.replace(/\d+/g,function(a){return"000".substring(a.length)+a})}function s(a){n||(j.ie()>=9&&g.add(window,"unload",q),n=!0),m[a]=a}var t={embed:function(a,b,c,d){__p&&__p();var e=k();a=l(a).replace(/&amp;/g,"&");c=babelHelpers["extends"]({allowscriptaccess:"always",flashvars:d,movie:a},c);typeof c.flashvars==="object"&&(c.flashvars=i.encode(c.flashvars));d=[];for(var f in c)Object.prototype.hasOwnProperty.call(c,f)&&c[f]&&d.push('<param name="'+l(f)+'" value="'+l(c[f])+'">');b=b.appendChild(o.createElement("span"));a="<object "+(j.ie()?'classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" ':'type="application/x-shockwave-flash"')+'data="'+a+'" '+(c.height?'height="'+c.height+'" ':"")+(c.width?'width="'+c.width+'" ':"")+'id="'+e+'">'+d.join("")+"</object>";b.innerHTML=a;c=b.firstChild;s(e);return c},remove:p,getVersion:function(){var a="Shockwave Flash",b="application/x-shockwave-flash",c="ShockwaveFlash.ShockwaveFlash",d;if(navigator.plugins&&typeof navigator.plugins[a]==="object"){a=navigator.plugins[a].description;a&&navigator.mimeTypes&&navigator.mimeTypes[b]&&navigator.mimeTypes[b].enabledPlugin&&(d=a.match(/\d+/g))}if(!d)try{d=new ActiveXObject(c).GetVariable("$version").match(/(\d+),(\d+),(\d+),(\d+)/),d=Array.prototype.slice.call(d,1)}catch(a){}return d},getVersionString:function(){var a=t.getVersion();return a?a.join("."):""},checkMinVersion:function(a){var b=t.getVersion();return!b?!1:r(b.join("."))>=r(a)},isAvailable:function(){return!!t.getVersion()}};e.exports=t}),null);
__d("dotAccess",[],(function(a,b,c,d,e,f){function a(a,b,c){b=b.split(".");do{var d=b.shift();a=a[d]||c&&(a[d]={})}while(b.length&&a);return a}e.exports=a}),null);
__d("GlobalCallback",["DOMWrapper","dotAccess","guid","wrapFunction"],(function(a,b,c,d,e,f,g,h,i,j){__p&&__p();var k,l;a={setPrefix:function(a){k=h(g.getWindow(),a,!0),l=a},create:function(a,b){k||this.setPrefix("__globalCallbacks");var c=i();k[c]=j(a,"entry",b||"GlobalCallback");return l+"."+c},remove:function(a){a=a.substring(l.length+1);delete k[a]}};e.exports=a}),null);
__d("XDM",["sdk.DOMEventListener","DOMWrapper","Flash","GlobalCallback","Log","UserAgent_DEPRECATED","emptyFunction","guid","wrapFunction"],(function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o){__p&&__p();var p={},q={transports:[]},r=h.getWindow();function s(a){__p&&__p();var b={},c=a.length,d=q.transports;while(c--)b[a[c]]=1;c=d.length;while(c--){a=d[c];var e=p[a];if(!b[a]&&e.isAvailable())return a}}a={register:function(a,b){k.debug("Registering %s as XDM provider",a),q.transports.push(a),p[a]=b},create:function(a){__p&&__p();if(!a.whenReady&&!a.onMessage){k.error("An instance without whenReady or onMessage makes no sense");throw new Error("An instance without whenReady or onMessage makes no sense")}a.channel||(k.warn("Missing channel name, selecting at random"),a.channel=n());a.whenReady||(a.whenReady=m);a.onMessage||(a.onMessage=m);var b=a.transport||s(a.blacklist||[]),c=p[b];if(c&&c.isAvailable()){k.debug("%s is available",b);c.init(a);return b}}};a.register("flash",function(){__p&&__p();var a=!1,b,c=!1,d=15e3,e;return{isAvailable:function(){return i.checkMinVersion("8.0.24")},init:function(f){__p&&__p();k.debug("init flash: "+f.channel);var g={send:function(a,c,d,e){k.debug("sending to: %s (%s)",c,e),b.postMessage(a,c,e)}};if(a){f.whenReady(g);return}var h=f.root.appendChild(r.document.createElement("div")),l=j.create(function(){j.remove(l);clearTimeout(e);k.info("xdm.swf called the callback");var a=j.create(function(a,b){a=decodeURIComponent(a),b=decodeURIComponent(b),k.debug("received message %s from %s",a,b),f.onMessage(a,b)},"xdm.swf:onMessage");b.init(f.channel,a);f.whenReady(g)},"xdm.swf:load");b=i.embed(f.flashUrl,h,null,{protocol:location.protocol.replace(":",""),host:location.host,callback:l,log:c});e=setTimeout(function(){k.warn("The Flash component did not load within %s ms - verify that the container is not set to hidden or invisible using CSS as this will cause some browsers to not load the components",d)},d);a=!0}}}());var t=/\.facebook\.com(\/|$)/;a.register("postmessage",function(){__p&&__p();var a=!1;return{isAvailable:function(){return!!r.postMessage},init:function(b){__p&&__p();k.debug("init postMessage: "+b.channel);var c="_FB_"+b.channel,d={send:function(a,b,c,d){if(r===c){k.error("Invalid windowref, equal to window (self)");throw new Error()}k.debug("sending to: %s (%s)",b,d);var e=function(){c.postMessage("_FB_"+d+a,b)};l.ie()==8||l.ieCompatibilityMode()?setTimeout(e,0):e()}};if(a){b.whenReady(d);return}g.add(r,"message",o(function(event){__p&&__p();var a=event.data,d=event.origin||"native";if(!/^(https?:\/\/|native$)/.test(d)){k.debug("Received message from invalid origin type: %s",d);return}if(d!=="native"&&!(t.test(location.hostname)||t.test(event.origin)))return;if(typeof a!=="string"){k.warn("Received message of type %s from %s, expected a string",typeof a,d);return}k.debug("received message %s from %s",a,d);a.substring(0,c.length)==c&&(a=a.substring(c.length));b.onMessage(a,d)},"entry","onMessage"));b.whenReady(d);a=!0}}}());e.exports=a}),null);
__d("resolveWindow",[],(function(a,b,c,d,e,f){__p&&__p();function a(a){__p&&__p();var b=window;a=a.split(".");try{for(var c=0;c<a.length;c++){var d=a[c],e=/^frames\[[\'\"]?([a-zA-Z0-9\-_]+)[\'\"]?\]$/.exec(d);if(e)b=b.frames[e[1]];else if(d==="opener"||d==="parent"||d==="top")b=b[d];else return null}}catch(a){return null}return b}e.exports=a}),null);
__d("initXdArbiter",["QueryString","resolveWindow","Log","XDM","XDMConfig"],(function(a,b,c,d,e,f){__p&&__p();(function(){__p&&__p();var a=b("QueryString"),c=b("resolveWindow"),d=b("Log"),e=b("XDM"),f=b("XDMConfig");function g(a){return a?a.replace(/[\"\'<>\(\)\\@]/g,""):a}function h(){return!window.chrome||!location.ancestorOrigins?!1:!/\.facebook\.com$/.test(location.ancestorOrigins[1])}function i(a,b){if(h())return"";if(window!=parent&&window.parent!=window.parent.parent)try{return parent.parent.XdArbiter.register(window,a,b)}catch(a){d.error("Could not register with XdArbiter in parent.parent")}return""}function j(a,b,e){if(!a&&h()){d.error("Can not use parent.parent to reach facebook.com");return}var f=a?c(a):parent.parent;try{f.XdArbiter.handleMessage(b,e,window)}catch(b){d.error("Could not reach facebook.com using %s",a)}}function k(a,b){var c=50;b=function(){--c||clearInterval(d);try{a(),clearInterval(d)}catch(a){}};var d=setInterval(b,50);b()}function l(){var a=/^https?:\/\/[^\/]*/.exec(o.origin)[0];k(function(){var b=c(o.relation).frames["fb_xdm_frame_"+p];if(typeof b.location.search==="undefined")throw new Error("Proxy not ready");location.search===b.location.search?b.proxyMessage(n,a):d.error("Version mismatch: %s, %s",location.search,b.location.search)},50)}function m(){var a=/^(.*)\/(.*)$/.exec(o.origin)[1];if(window.__fbNative&&window.__fbNative.postMessage)window.__fbNative.postMessage(n,a);else{var b=function b(c){window.removeEventListener("fbNativeReady",b),window.__fbNative.postMessage(n,a)};window.addEventListener("fbNativeReady",b)}}var n=/#(.*)|$/.exec(document.URL)[1];window==top&&(location.hash="");if(!n){d.error("xd_arbiter.php loaded without a valid hash, referrer: %s",document.referrer);return}var o=a.decode(n,!0),p=location.protocol.replace(":","");if(window.name.substring(0,6)==="blank_"){parent.frames[o.forIframe].require("Arbiter").inform("blankIframeAck",window);return}if(o.relation){window==top&&/FBAN\/\w+;/i.test(navigator.userAgent)?(d.info("Native proxy"),m()):(d.info("Legacy proxy to %s",o.relation),l());return}if(p!=/https?/.exec(window.name)[0]){d.info("Redirection to %s detected, aborting",p);return}m=g(o.transport);var q=g(o.channel),r=g(o.origin),s=g(o.xd_name);if(!/^https?/.test(r)){d.error("Invalid origin presented, aborting.");return}e.create({root:document.body,transport:m,channel:q+"_"+p,flashUrl:f.Flash.path,onMessage:function(b,c){if(r!==c){d.info("Received message from unknown origin %s, expected %s.",c,r);return}/^FB_RPC:/.test(b)||(b=a.decode(b));j(b.relation,b,r)},whenReady:function(b){window.proxyMessage=function(a,c){c===r?b.send(a,r,parent,q):d.error("Failed proxying to %s, expected %s",c,r)};var c=null;c={xd_action:"proxy_ready",logged_in:/\bc_user=/.test(document.cookie),data:c};var e=i(s,r);e&&(c.registered=e);b.send(a.encode(c),r,parent,q)}})})()}),null);__d("XDMConfig",[],{"Flash":{"path":"https:\/\/connect.facebook.net\/rsrc.php\/v2\/yW\/r\/yOZN1vHw3Z_.swf"}}); require('initXdArbiter'); </script><b id="warning" style="display: none; color:red"> SECURITY WARNING: Please treat the URL above as you would your password and do not share it with anyone. See the <a href="https://l.facebook.com/l.php?u=https%3A%2F%2Fon.fb.me%2F1mXNHhm&amp;h=AT2r8Oodxgfc7LKwQ-3LJjdiO96hQ79VnFFZ8ZiZx7saJNSJkrF9yAe6nVOuuFm4R888t5nzNcMcVdbmp856f2rqlIs8ZemmSS1arkO7QCztIUVYCxP6t_2RQVf1j07wS0aE54RL9E04rCabFD6zQPH9CFsyBcdIjeFA" target="_blank" rel="nofollow" data-lynx-mode="asynclazy">Facebook Help Center</a> for more information. </b><script>if (window == top) {  setTimeout(function() {    document.getElementById("warning").style.display = 'block';  }, 2000);}</script></body></html>