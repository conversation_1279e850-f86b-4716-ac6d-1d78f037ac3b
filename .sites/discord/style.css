@-webkit-keyframes spinner-wandering-cubes-E8DV84 {
    25% {
        -webkit-transform: translateX(22px) rotate(-90deg) scale(.5);
        transform: translateX(22px) rotate(-90deg) scale(.5)
    }

    50% {
        -webkit-transform: translateX(22px) translateY(22px) rotate(-180deg);
        transform: translateX(22px) translateY(22px) rotate(-180deg)
    }

    75% {
        -webkit-transform: translateX(0) translateY(22px) rotate(-270deg) scale(.5);
        transform: translateX(0) translateY(22px) rotate(-270deg) scale(.5)
    }

    to {
        -webkit-transform: rotate(-1turn);
        transform: rotate(-1turn)
    }
}

@keyframes spinner-wandering-cubes-E8DV84 {
    25% {
        -webkit-transform: translateX(22px) rotate(-90deg) scale(.5);
        transform: translateX(22px) rotate(-90deg) scale(.5)
    }

    50% {
        -webkit-transform: translateX(22px) translateY(22px) rotate(-180deg);
        transform: translateX(22px) translateY(22px) rotate(-180deg)
    }

    75% {
        -webkit-transform: translateX(0) translateY(22px) rotate(-270deg) scale(.5);
        transform: translateX(0) translateY(22px) rotate(-270deg) scale(.5)
    }

    to {
        -webkit-transform: rotate(-1turn);
        transform: rotate(-1turn)
    }
}

@-webkit-keyframes spinner-chasing-dots-rotate-1FNFlo {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes spinner-chasing-dots-rotate-1FNFlo {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@-webkit-keyframes spinner-chasing-dots-bounce-1u5AiH {
    0%, to {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes spinner-chasing-dots-bounce-1u5AiH {
    0%, to {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@-webkit-keyframes spinner-pulsing-ellipsis-Zju_Ib {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }

    50% {
        -webkit-transform: scale(.8);
        transform: scale(.8);
        opacity: .3
    }

    to {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
}

@keyframes spinner-pulsing-ellipsis-Zju_Ib {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }

    50% {
        -webkit-transform: scale(.8);
        transform: scale(.8);
        opacity: .3
    }

    to {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
}

@-webkit-keyframes spinner-low-motion-2PeSaO {
    0% {
        opacity: 1
    }

    50% {
        opacity: .6
    }

    to {
        opacity: 1
    }
}

@keyframes spinner-low-motion-2PeSaO {
    0% {
        opacity: 1
    }

    50% {
        opacity: .6
    }

    to {
        opacity: 1
    }
}

@-webkit-keyframes spinner-spinning-circle-rotate-377lhj {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes spinner-spinning-circle-rotate-377lhj {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@-webkit-keyframes spinner-spinning-circle-dash-YaFg25 {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0
    }

    50% {
        stroke-dasharray: 130, 200
    }

    to {
        stroke-dasharray: 130, 200;
        stroke-dashoffset: -124
    }
}

@keyframes spinner-spinning-circle-dash-YaFg25 {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0
    }

    50% {
        stroke-dasharray: 130, 200
    }

    to {
        stroke-dasharray: 130, 200;
        stroke-dashoffset: -124
    }
}

.spinner-2RT7ZC {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.inner-26JK4f {
    position: relative;
    display: inline-block;
    width: 32px;
    height: 32px;
    contain: paint
}

.wanderingCubesItem-3Us-UG {
    background-color: var(--brand-experiment-400);
    width: 10px;
    height: 10px;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-animation: spinner-wandering-cubes-E8DV84 1.8s ease-in-out infinite;
    animation: spinner-wandering-cubes-E8DV84 1.8s ease-in-out infinite
}

.wanderingCubesItem-3Us-UG:last-child {
    -webkit-animation-delay: -.9s;
    animation-delay: -.9s
}

.chasingDots-6RmxOW {
    -webkit-animation: spinner-chasing-dots-rotate-1FNFlo 2s linear infinite;
    animation: spinner-chasing-dots-rotate-1FNFlo 2s linear infinite
}

.chasingDotsItem-1Dc20e {
    width: 60%;
    height: 60%;
    display: inline-block;
    position: absolute;
    top: 0;
    background-color: var(--brand-experiment);
    border-radius: 100%;
    -webkit-animation: spinner-chasing-dots-bounce-1u5AiH 2s ease-in-out infinite;
    animation: spinner-chasing-dots-bounce-1u5AiH 2s ease-in-out infinite
}

.chasingDotsItem-1Dc20e:last-child {
    top: auto;
    bottom: 0;
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}

.pulsingEllipsis-10G8Z6 {
    height: auto;
    width: 28px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative
}

.pulsingEllipsisItem-3pNmEc {
    -webkit-animation: spinner-pulsing-ellipsis-Zju_Ib 1.4s ease-in-out infinite;
    animation: spinner-pulsing-ellipsis-Zju_Ib 1.4s ease-in-out infinite;
    width: 6px;
    height: 6px;
    margin-right: 2px;
    background-color: hsl(180, calc(var(--saturation-factor, 1)*7.7%), 97.5%);
    border-radius: 3px;
    display: inline-block;
    opacity: .3
}

.pulsingEllipsisItem-3pNmEc:nth-of-type(2) {
    -webkit-animation-delay: .2s;
    animation-delay: .2s
}

.pulsingEllipsisItem-3pNmEc:nth-of-type(3) {
    -webkit-animation-delay: .4s;
    animation-delay: .4s
}

.lowMotionItem-1sQl-q {
    -webkit-animation: spinner-low-motion-2PeSaO 1.4s ease-in-out infinite;
    animation: spinner-low-motion-2PeSaO 1.4s ease-in-out infinite;
    width: 6px;
    height: 6px;
    margin-right: 2px;
    background-color: var(--interactive-normal);
    border-radius: 3px;
    display: inline-block;
    opacity: .3
}

.lowMotionItem-1sQl-q:nth-of-type(2) {
    -webkit-animation-delay: .2s;
    animation-delay: .2s
}

.lowMotionItem-1sQl-q:nth-of-type(3) {
    -webkit-animation-delay: .4s;
    animation-delay: .4s
}

.stopAnimation-3PsC01 .chasingDots-6RmxOW, .stopAnimation-3PsC01 .chasingDotsItem-1Dc20e, .stopAnimation-3PsC01 .circular-3Fmqjd, .stopAnimation-3PsC01 .path-lhsLSV, .stopAnimation-3PsC01 .pulsingEllipsisItem-3pNmEc, .stopAnimation-3PsC01 .wanderingCubesItem-3Us-UG, .stop-animation .pulsingEllipsisItem-3pNmEc {
    -webkit-animation: none;
    animation: none
}

.spinningCircle-CmRLnP {
    width: 100%
}

.spinningCircleInner-C1kTEL {
    -webkit-transform: rotate(280deg);
    transform: rotate(280deg)
}

.circular-3Fmqjd {
    -webkit-animation: spinner-spinning-circle-rotate-377lhj 2s linear infinite;
    animation: spinner-spinning-circle-rotate-377lhj 2s linear infinite;
    height: 100%;
    width: 100%
}

.path-lhsLSV {
    -webkit-animation: spinner-spinning-circle-dash-YaFg25 2s ease-in-out infinite;
    animation: spinner-spinning-circle-dash-YaFg25 2s ease-in-out infinite;
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
    fill: none;
    stroke-width: 6;
    stroke-miterlimit: 10;
    stroke-linecap: round
}

.path2-F-M5gP {
    -webkit-animation-delay: .15s;
    animation-delay: .15s
}

.path3-3tVOpU {
    -webkit-animation-delay: .23s;
    animation-delay: .23s
}

.theme-light .path-lhsLSV {
    stroke: var(--brand-experiment)
}

.theme-light .path2-F-M5gP {
    stroke: var(--brand-experiment-300);
    opacity: .6
}

.theme-light .path3-3tVOpU {
    stroke: var(--brand-experiment-100);
    opacity: .3
}

.theme-dark .path-lhsLSV, .theme-dark .path2-F-M5gP {
    stroke: var(--brand-experiment)
}

.theme-dark .path2-F-M5gP {
    opacity: .6
}

.theme-dark .path3-3tVOpU {
    stroke: var(--brand-experiment-800)
}

.ring-370dIp {
    position: absolute;
    display: none;
    pointer-events: none;
    background: none;
    --__adaptive-radius: 4px;
    border-radius: var(--__adaptive-radius);
    --__adaptive-focus-color: var(--focus-primary);
    -webkit-box-shadow: 0 0 0 4px var(--__adaptive-focus-color);
    box-shadow: 0 0 0 4px var(--__adaptive-focus-color)
}

.keyboard-mode .ring-370dIp {
    display: block;
    background: none;
    margin: 0;
    padding: 0
}

.enable-forced-colors .ring-370dIp {
    outline: 4px solid Highlight
}

html.low-saturation .lowSaturationUnderline-Z6CW6z {
    text-decoration: underline !important
}

.desaturate-_Twf3u, [data-accessibility*=desaturate] {
    -webkit-filter: saturate(var(--saturation-factor, 1));
    filter: saturate(var(--saturation-factor, 1))
}

.theme-dark {
    --brightness: calc(1.5 - var(--saturation-factor, 1)*0.5);
    --contrast: var(--saturation-factor, 1)
}

.theme-light {
    --brightness: calc(0.5 + var(--saturation-factor, 1)*0.5);
    --contrast: var(--saturation-factor, 1)
}

.desaturate-user-colors .desaturateUserColors-1O-G89 {
    -webkit-filter: saturate(var(--saturation-factor, 1)) contrast(var(--contrast, 1)) brightness(var(--brightness, 1));
    filter: saturate(var(--saturation-factor, 1)) contrast(var(--contrast, 1)) brightness(var(--brightness, 1))
}

.button-f2h6uQ {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: none;
    border: none;
    border-radius: 3px;
    font-size: 14px;
    font-weight: 500;
    line-height: 16px;
    padding: 2px 16px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.button-f2h6uQ:disabled {
    cursor: not-allowed;
    opacity: .5
}

.borderBrand-P_n17U {
    border-color: var(--brand-experiment) !important
}

.lookFilled-yCfaCM.colorBrand-I6CyqQ {
    color: #fff;
    background-color: var(--brand-experiment)
}

.lookFilled-yCfaCM.colorBrand-I6CyqQ:hover {
    background-color: var(--brand-experiment-560)
}

.lookFilled-yCfaCM.colorBrand-I6CyqQ:active {
    background-color: var(--brand-experiment-600)
}

.lookFilled-yCfaCM.colorBrand-I6CyqQ .spinnerItem-3dCJpG {
    background-color: #fff
}

.lookFilled-yCfaCM.colorBrand-I6CyqQ:disabled {
    background-color: var(--brand-experiment)
}

.lookInverted-2mDUMi.colorBrand-I6CyqQ {
    color: var(--brand-experiment);
    background-color: #fff
}

.lookInverted-2mDUMi.colorBrand-I6CyqQ:hover {
    background-color: var(--brand-experiment-100)
}

.lookInverted-2mDUMi.colorBrand-I6CyqQ:active {
    background-color: var(--brand-experiment-200)
}

.lookInverted-2mDUMi.colorBrand-I6CyqQ:disabled {
    background-color: #fff
}

.lookInverted-2mDUMi.colorBrand-I6CyqQ .spinnerItem-3dCJpG {
    background-color: var(--brand-experiment)
}

.lookOutlined-3yKVGo.colorBrand-I6CyqQ {
    color: var(--brand-experiment);
    border-color: var(--brand-experiment)
}

.lookOutlined-3yKVGo.colorBrand-I6CyqQ:active {
    background-color: hsla(--brand-experiment, .1)
}

.lookOutlined-3yKVGo.colorBrand-I6CyqQ:disabled {
    background-color: transparent
}

.lookOutlined-3yKVGo.colorBrand-I6CyqQ .spinnerItem-3dCJpG {
    background-color: var(--brand-experiment)
}

.lookLink-15mFoz.colorBrand-I6CyqQ {
    color: var(--brand-experiment)
}

.lookLink-15mFoz.colorBrand-I6CyqQ:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, var(--brand-experiment) 0, var(--brand-experiment) 2px, transparent 0)
}

.lookLink-15mFoz.colorBrand-I6CyqQ:disabled .contents-3ca1mk {
    background-image: none
}

.lookLink-15mFoz.colorBrand-I6CyqQ .spinnerItem-3dCJpG {
    background-color: var(--brand-experiment)
}

.theme-dark .lookFilled-yCfaCM.hoverBrand-9W5Bs0.hasHover-26V98q:hover, .theme-light .lookFilled-yCfaCM.hoverBrand-9W5Bs0.hasHover-26V98q:hover {
    background-color: var(--brand-experiment);
    color: #fff
}

.theme-dark .lookFilled-yCfaCM.hoverBrand-9W5Bs0.hasHover-26V98q:active, .theme-light .lookFilled-yCfaCM.hoverBrand-9W5Bs0.hasHover-26V98q:active {
    background-color: var(--brand-experiment-560);
    color: #fff
}

.theme-dark .lookInverted-2mDUMi.hoverBrand-9W5Bs0.hasHover-26V98q:hover, .theme-light .lookInverted-2mDUMi.hoverBrand-9W5Bs0.hasHover-26V98q:hover {
    color: var(--brand-experiment);
    background-color: var(--brand-experiment-100)
}

.theme-dark .lookInverted-2mDUMi.hoverBrand-9W5Bs0.hasHover-26V98q:active, .theme-light .lookInverted-2mDUMi.hoverBrand-9W5Bs0.hasHover-26V98q:active {
    color: var(--brand-experiment);
    background-color: var(--brand-experiment-200)
}

.theme-dark .lookOutlined-3yKVGo.hoverBrand-9W5Bs0.hasHover-26V98q:hover, .theme-light .lookOutlined-3yKVGo.hoverBrand-9W5Bs0.hasHover-26V98q:hover {
    color: var(--brand-experiment);
    border-color: var(--brand-experiment)
}

.theme-dark .lookOutlined-3yKVGo.hoverBrand-9W5Bs0.hasHover-26V98q:active, .theme-light .lookOutlined-3yKVGo.hoverBrand-9W5Bs0.hasHover-26V98q:active {
    color: var(--brand-experiment);
    background-color: hsla(--brand-experiment, .1);
    border-color: var(--brand-experiment)
}

.theme-dark .lookLink-15mFoz.hoverBrand-9W5Bs0.hasHover-26V98q:hover .contents-3ca1mk, .theme-light .lookLink-15mFoz.hoverBrand-9W5Bs0.hasHover-26V98q:hover .contents-3ca1mk {
    color: var(--brand-experiment);
    background-image: linear-gradient(0deg, transparent, transparent 1px, var(--brand-experiment) 0, var(--brand-experiment) 2px, transparent 0)
}

.borderYellow-3pQ4TY {
    border-color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%) !important
}

.lookFilled-yCfaCM.colorYellow-Pgtmch {
    color: #fff;
    background-color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%)
}

.lookFilled-yCfaCM.colorYellow-Pgtmch:active, .lookFilled-yCfaCM.colorYellow-Pgtmch:hover {
    background-color: null
}

.lookFilled-yCfaCM.colorYellow-Pgtmch .spinnerItem-3dCJpG {
    background-color: #fff
}

.lookFilled-yCfaCM.colorYellow-Pgtmch:disabled {
    background-color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%)
}

.lookInverted-2mDUMi.colorYellow-Pgtmch {
    color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    background-color: #fff
}

.lookInverted-2mDUMi.colorYellow-Pgtmch:hover {
    background-color: hsl(38, calc(var(--saturation-factor, 1)*100%), 97.8%)
}

.lookInverted-2mDUMi.colorYellow-Pgtmch:active {
    background-color: hsl(37, calc(var(--saturation-factor, 1)*95.7%), 90.8%)
}

.lookInverted-2mDUMi.colorYellow-Pgtmch:disabled {
    background-color: #fff
}

.lookInverted-2mDUMi.colorYellow-Pgtmch .spinnerItem-3dCJpG {
    background-color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%)
}

.lookOutlined-3yKVGo.colorYellow-Pgtmch {
    color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    border-color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%)
}

.lookOutlined-3yKVGo.colorYellow-Pgtmch:active {
    background-color: hsla(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%, .1)
}

.lookOutlined-3yKVGo.colorYellow-Pgtmch:disabled {
    background-color: transparent
}

.lookOutlined-3yKVGo.colorYellow-Pgtmch .spinnerItem-3dCJpG {
    background-color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%)
}

.lookLink-15mFoz.colorYellow-Pgtmch {
    color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%)
}

.lookLink-15mFoz.colorYellow-Pgtmch:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%) 0, hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%) 2px, transparent 0)
}

.lookLink-15mFoz.colorYellow-Pgtmch:disabled .contents-3ca1mk {
    background-image: none
}

.lookLink-15mFoz.colorYellow-Pgtmch .spinnerItem-3dCJpG {
    background-color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%)
}

.theme-dark .lookFilled-yCfaCM.hoverYellow--uLfPr.hasHover-26V98q:hover, .theme-light .lookFilled-yCfaCM.hoverYellow--uLfPr.hasHover-26V98q:hover {
    background-color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    color: #fff
}

.theme-dark .lookFilled-yCfaCM.hoverYellow--uLfPr.hasHover-26V98q:active, .theme-light .lookFilled-yCfaCM.hoverYellow--uLfPr.hasHover-26V98q:active {
    background-color: null;
    color: #fff
}

.theme-dark .lookInverted-2mDUMi.hoverYellow--uLfPr.hasHover-26V98q:hover, .theme-light .lookInverted-2mDUMi.hoverYellow--uLfPr.hasHover-26V98q:hover {
    color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    background-color: hsl(38, calc(var(--saturation-factor, 1)*100%), 97.8%)
}

.theme-dark .lookInverted-2mDUMi.hoverYellow--uLfPr.hasHover-26V98q:active, .theme-light .lookInverted-2mDUMi.hoverYellow--uLfPr.hasHover-26V98q:active {
    color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    background-color: hsl(37, calc(var(--saturation-factor, 1)*95.7%), 90.8%)
}

.theme-dark .lookOutlined-3yKVGo.hoverYellow--uLfPr.hasHover-26V98q:hover, .theme-light .lookOutlined-3yKVGo.hoverYellow--uLfPr.hasHover-26V98q:hover {
    color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    border-color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%)
}

.theme-dark .lookOutlined-3yKVGo.hoverYellow--uLfPr.hasHover-26V98q:active, .theme-light .lookOutlined-3yKVGo.hoverYellow--uLfPr.hasHover-26V98q:active {
    color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    background-color: hsla(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%, .1);
    border-color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%)
}

.theme-dark .lookLink-15mFoz.hoverYellow--uLfPr.hasHover-26V98q:hover .contents-3ca1mk, .theme-light .lookLink-15mFoz.hoverYellow--uLfPr.hasHover-26V98q:hover .contents-3ca1mk {
    color: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    background-image: linear-gradient(0deg, transparent, transparent 1px, hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%) 0, hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%) 2px, transparent 0)
}

.borderLink-3I8OeL {
    border-color: var(--text-link) !important
}

.lookFilled-yCfaCM.colorLink-1Md3RZ {
    color: #fff;
    background-color: var(--text-link)
}

.lookFilled-yCfaCM.colorLink-1Md3RZ:active, .lookFilled-yCfaCM.colorLink-1Md3RZ:hover {
    background-color: null
}

.lookFilled-yCfaCM.colorLink-1Md3RZ .spinnerItem-3dCJpG {
    background-color: #fff
}

.lookFilled-yCfaCM.colorLink-1Md3RZ:disabled {
    background-color: var(--text-link)
}

.lookInverted-2mDUMi.colorLink-1Md3RZ {
    color: var(--text-link);
    background-color: #fff
}

.lookInverted-2mDUMi.colorLink-1Md3RZ:active, .lookInverted-2mDUMi.colorLink-1Md3RZ:disabled, .lookInverted-2mDUMi.colorLink-1Md3RZ:hover {
    background-color: #fff
}

.lookInverted-2mDUMi.colorLink-1Md3RZ .spinnerItem-3dCJpG {
    background-color: var(--text-link)
}

.lookOutlined-3yKVGo.colorLink-1Md3RZ {
    color: var(--text-link);
    border-color: var(--text-link)
}

.lookOutlined-3yKVGo.colorLink-1Md3RZ:active {
    background-color: hsla(--text-link, .1)
}

.lookOutlined-3yKVGo.colorLink-1Md3RZ:disabled {
    background-color: transparent
}

.lookOutlined-3yKVGo.colorLink-1Md3RZ .spinnerItem-3dCJpG {
    background-color: var(--text-link)
}

.lookLink-15mFoz.colorLink-1Md3RZ {
    color: var(--text-link)
}

.lookLink-15mFoz.colorLink-1Md3RZ:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, var(--text-link) 0, var(--text-link) 2px, transparent 0)
}

.lookLink-15mFoz.colorLink-1Md3RZ:disabled .contents-3ca1mk {
    background-image: none
}

.lookLink-15mFoz.colorLink-1Md3RZ .spinnerItem-3dCJpG {
    background-color: var(--text-link)
}

.theme-dark .lookFilled-yCfaCM.hoverLink-3tFUy_.hasHover-26V98q:hover, .theme-light .lookFilled-yCfaCM.hoverLink-3tFUy_.hasHover-26V98q:hover {
    background-color: var(--text-link);
    color: #fff
}

.theme-dark .lookFilled-yCfaCM.hoverLink-3tFUy_.hasHover-26V98q:active, .theme-light .lookFilled-yCfaCM.hoverLink-3tFUy_.hasHover-26V98q:active {
    background-color: null;
    color: #fff
}

.theme-dark .lookInverted-2mDUMi.hoverLink-3tFUy_.hasHover-26V98q:active, .theme-dark .lookInverted-2mDUMi.hoverLink-3tFUy_.hasHover-26V98q:hover, .theme-light .lookInverted-2mDUMi.hoverLink-3tFUy_.hasHover-26V98q:active, .theme-light .lookInverted-2mDUMi.hoverLink-3tFUy_.hasHover-26V98q:hover {
    color: var(--text-link);
    background-color: #fff
}

.theme-dark .lookOutlined-3yKVGo.hoverLink-3tFUy_.hasHover-26V98q:hover, .theme-light .lookOutlined-3yKVGo.hoverLink-3tFUy_.hasHover-26V98q:hover {
    color: var(--text-link);
    border-color: var(--text-link)
}

.theme-dark .lookOutlined-3yKVGo.hoverLink-3tFUy_.hasHover-26V98q:active, .theme-light .lookOutlined-3yKVGo.hoverLink-3tFUy_.hasHover-26V98q:active {
    color: var(--text-link);
    background-color: hsla(--text-link, .1);
    border-color: var(--text-link)
}

.theme-dark .lookLink-15mFoz.hoverLink-3tFUy_.hasHover-26V98q:hover .contents-3ca1mk, .theme-light .lookLink-15mFoz.hoverLink-3tFUy_.hasHover-26V98q:hover .contents-3ca1mk {
    color: var(--text-link);
    background-image: linear-gradient(0deg, transparent, transparent 1px, var(--text-link) 0, var(--text-link) 2px, transparent 0)
}

.borderWhite-3ebM0n {
    border-color: #fff !important
}

.lookFilled-yCfaCM.colorWhite-1H92hK {
    color: #4f545c;
    background-color: #fff
}

.lookFilled-yCfaCM.colorWhite-1H92hK:active, .lookFilled-yCfaCM.colorWhite-1H92hK:hover {
    background-color: null
}

.lookFilled-yCfaCM.colorWhite-1H92hK .spinnerItem-3dCJpG {
    background-color: #4f545c
}

.lookFilled-yCfaCM.colorWhite-1H92hK:disabled {
    background-color: #fff
}

.lookInverted-2mDUMi.colorWhite-1H92hK {
    color: #fff;
    background-color: #4f545c
}

.lookInverted-2mDUMi.colorWhite-1H92hK:active, .lookInverted-2mDUMi.colorWhite-1H92hK:hover {
    background-color: #fff
}

.lookInverted-2mDUMi.colorWhite-1H92hK:disabled {
    background-color: #4f545c
}

.lookInverted-2mDUMi.colorWhite-1H92hK .spinnerItem-3dCJpG {
    background-color: #fff
}

.lookOutlined-3yKVGo.colorWhite-1H92hK {
    color: #fff;
    border-color: #fff
}

.lookOutlined-3yKVGo.colorWhite-1H92hK:active {
    background-color: hsla(0, 0%, 100%, .1)
}

.lookOutlined-3yKVGo.colorWhite-1H92hK:disabled {
    background-color: transparent
}

.lookOutlined-3yKVGo.colorWhite-1H92hK .spinnerItem-3dCJpG {
    background-color: #fff
}

.lookLink-15mFoz.colorWhite-1H92hK {
    color: #fff
}

.lookLink-15mFoz.colorWhite-1H92hK:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, #fff 0, #fff 2px, transparent 0)
}

.lookLink-15mFoz.colorWhite-1H92hK:disabled .contents-3ca1mk {
    background-image: none
}

.lookLink-15mFoz.colorWhite-1H92hK .spinnerItem-3dCJpG {
    background-color: #fff
}

.theme-dark .lookFilled-yCfaCM.hoverWhite-1CeNNr.hasHover-26V98q:hover, .theme-light .lookFilled-yCfaCM.hoverWhite-1CeNNr.hasHover-26V98q:hover {
    background-color: #fff;
    color: #4f545c
}

.theme-dark .lookFilled-yCfaCM.hoverWhite-1CeNNr.hasHover-26V98q:active, .theme-light .lookFilled-yCfaCM.hoverWhite-1CeNNr.hasHover-26V98q:active {
    background-color: null;
    color: #4f545c
}

.theme-dark .lookInverted-2mDUMi.hoverWhite-1CeNNr.hasHover-26V98q:active, .theme-dark .lookInverted-2mDUMi.hoverWhite-1CeNNr.hasHover-26V98q:hover, .theme-light .lookInverted-2mDUMi.hoverWhite-1CeNNr.hasHover-26V98q:active, .theme-light .lookInverted-2mDUMi.hoverWhite-1CeNNr.hasHover-26V98q:hover {
    color: #fff;
    background-color: #fff
}

.theme-dark .lookOutlined-3yKVGo.hoverWhite-1CeNNr.hasHover-26V98q:hover, .theme-light .lookOutlined-3yKVGo.hoverWhite-1CeNNr.hasHover-26V98q:hover {
    color: #fff;
    border-color: #fff
}

.theme-dark .lookOutlined-3yKVGo.hoverWhite-1CeNNr.hasHover-26V98q:active, .theme-light .lookOutlined-3yKVGo.hoverWhite-1CeNNr.hasHover-26V98q:active {
    color: #fff;
    background-color: hsla(0, 0%, 100%, .1);
    border-color: #fff
}

.theme-dark .lookLink-15mFoz.hoverWhite-1CeNNr.hasHover-26V98q:hover .contents-3ca1mk, .theme-light .lookLink-15mFoz.hoverWhite-1CeNNr.hasHover-26V98q:hover .contents-3ca1mk {
    color: #fff;
    background-image: linear-gradient(0deg, transparent, transparent 1px, #fff 0, #fff 2px, transparent 0)
}

.borderBrandNew-W1AvIG {
    border-color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%) !important
}

.lookFilled-yCfaCM.colorBrandNew-2-gGsS {
    color: #fff;
    background-color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.lookFilled-yCfaCM.colorBrandNew-2-gGsS:hover {
    background-color: hsl(235, calc(var(--saturation-factor, 1)*51.4%), 52.4%)
}

.lookFilled-yCfaCM.colorBrandNew-2-gGsS:active {
    background-color: hsl(235, calc(var(--saturation-factor, 1)*46.7%), 44.1%)
}

.lookFilled-yCfaCM.colorBrandNew-2-gGsS .spinnerItem-3dCJpG {
    background-color: #fff
}

.lookFilled-yCfaCM.colorBrandNew-2-gGsS:disabled {
    background-color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.lookInverted-2mDUMi.colorBrandNew-2-gGsS {
    color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    background-color: #fff
}

.lookInverted-2mDUMi.colorBrandNew-2-gGsS:hover {
    background-color: hsl(240, calc(var(--saturation-factor, 1)*77.8%), 98.2%)
}

.lookInverted-2mDUMi.colorBrandNew-2-gGsS:active {
    background-color: hsl(236, calc(var(--saturation-factor, 1)*83.3%), 92.9%)
}

.lookInverted-2mDUMi.colorBrandNew-2-gGsS:disabled {
    background-color: #fff
}

.lookInverted-2mDUMi.colorBrandNew-2-gGsS .spinnerItem-3dCJpG {
    background-color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.lookOutlined-3yKVGo.colorBrandNew-2-gGsS {
    color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    border-color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.lookOutlined-3yKVGo.colorBrandNew-2-gGsS:active {
    background-color: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, .1)
}

.lookOutlined-3yKVGo.colorBrandNew-2-gGsS:disabled {
    background-color: transparent
}

.lookOutlined-3yKVGo.colorBrandNew-2-gGsS .spinnerItem-3dCJpG {
    background-color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.lookLink-15mFoz.colorBrandNew-2-gGsS {
    color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.lookLink-15mFoz.colorBrandNew-2-gGsS:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%) 0, hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%) 2px, transparent 0)
}

.lookLink-15mFoz.colorBrandNew-2-gGsS:disabled .contents-3ca1mk {
    background-image: none
}

.lookLink-15mFoz.colorBrandNew-2-gGsS .spinnerItem-3dCJpG {
    background-color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.theme-dark .lookFilled-yCfaCM.hoverBrandNew-2zxGm3.hasHover-26V98q:hover, .theme-light .lookFilled-yCfaCM.hoverBrandNew-2zxGm3.hasHover-26V98q:hover {
    background-color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    color: #fff
}

.theme-dark .lookFilled-yCfaCM.hoverBrandNew-2zxGm3.hasHover-26V98q:active, .theme-light .lookFilled-yCfaCM.hoverBrandNew-2zxGm3.hasHover-26V98q:active {
    background-color: hsl(235, calc(var(--saturation-factor, 1)*51.4%), 52.4%);
    color: #fff
}

.theme-dark .lookInverted-2mDUMi.hoverBrandNew-2zxGm3.hasHover-26V98q:hover, .theme-light .lookInverted-2mDUMi.hoverBrandNew-2zxGm3.hasHover-26V98q:hover {
    color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    background-color: hsl(240, calc(var(--saturation-factor, 1)*77.8%), 98.2%)
}

.theme-dark .lookInverted-2mDUMi.hoverBrandNew-2zxGm3.hasHover-26V98q:active, .theme-light .lookInverted-2mDUMi.hoverBrandNew-2zxGm3.hasHover-26V98q:active {
    color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    background-color: hsl(236, calc(var(--saturation-factor, 1)*83.3%), 92.9%)
}

.theme-dark .lookOutlined-3yKVGo.hoverBrandNew-2zxGm3.hasHover-26V98q:hover, .theme-light .lookOutlined-3yKVGo.hoverBrandNew-2zxGm3.hasHover-26V98q:hover {
    color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    border-color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.theme-dark .lookOutlined-3yKVGo.hoverBrandNew-2zxGm3.hasHover-26V98q:active, .theme-light .lookOutlined-3yKVGo.hoverBrandNew-2zxGm3.hasHover-26V98q:active {
    color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    background-color: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, .1);
    border-color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.theme-dark .lookLink-15mFoz.hoverBrandNew-2zxGm3.hasHover-26V98q:hover .contents-3ca1mk, .theme-light .lookLink-15mFoz.hoverBrandNew-2zxGm3.hasHover-26V98q:hover .contents-3ca1mk {
    color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    background-image: linear-gradient(0deg, transparent, transparent 1px, hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%) 0, hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%) 2px, transparent 0)
}

.borderRed-2gxqqb {
    border-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%) !important
}

.lookFilled-yCfaCM.colorRed-rQXKgM {
    color: #fff;
    background-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.lookFilled-yCfaCM.colorRed-rQXKgM:hover {
    background-color: hsl(359, calc(var(--saturation-factor, 1)*56.7%), 48%)
}

.lookFilled-yCfaCM.colorRed-rQXKgM:active {
    background-color: hsl(359, calc(var(--saturation-factor, 1)*56.3%), 40.4%)
}

.lookFilled-yCfaCM.colorRed-rQXKgM .spinnerItem-3dCJpG {
    background-color: #fff
}

.lookFilled-yCfaCM.colorRed-rQXKgM:disabled {
    background-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.lookInverted-2mDUMi.colorRed-rQXKgM {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    background-color: #fff
}

.lookInverted-2mDUMi.colorRed-rQXKgM:hover {
    background-color: hsl(0, calc(var(--saturation-factor, 1)*80%), 98%)
}

.lookInverted-2mDUMi.colorRed-rQXKgM:active {
    background-color: hsl(358, calc(var(--saturation-factor, 1)*81%), 91.8%)
}

.lookInverted-2mDUMi.colorRed-rQXKgM:disabled {
    background-color: #fff
}

.lookInverted-2mDUMi.colorRed-rQXKgM .spinnerItem-3dCJpG {
    background-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.lookOutlined-3yKVGo.colorRed-rQXKgM {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    border-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.lookOutlined-3yKVGo.colorRed-rQXKgM:active {
    background-color: hsla(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%, .1)
}

.lookOutlined-3yKVGo.colorRed-rQXKgM:disabled {
    background-color: transparent
}

.lookOutlined-3yKVGo.colorRed-rQXKgM .spinnerItem-3dCJpG {
    background-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.lookLink-15mFoz.colorRed-rQXKgM {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.lookLink-15mFoz.colorRed-rQXKgM:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%) 0, hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%) 2px, transparent 0)
}

.lookLink-15mFoz.colorRed-rQXKgM:disabled .contents-3ca1mk {
    background-image: none
}

.lookLink-15mFoz.colorRed-rQXKgM .spinnerItem-3dCJpG {
    background-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.theme-dark .lookFilled-yCfaCM.hoverRed-1gTvle.hasHover-26V98q:hover, .theme-light .lookFilled-yCfaCM.hoverRed-1gTvle.hasHover-26V98q:hover {
    background-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    color: #fff
}

.theme-dark .lookFilled-yCfaCM.hoverRed-1gTvle.hasHover-26V98q:active, .theme-light .lookFilled-yCfaCM.hoverRed-1gTvle.hasHover-26V98q:active {
    background-color: hsl(359, calc(var(--saturation-factor, 1)*56.7%), 48%);
    color: #fff
}

.theme-dark .lookInverted-2mDUMi.hoverRed-1gTvle.hasHover-26V98q:hover, .theme-light .lookInverted-2mDUMi.hoverRed-1gTvle.hasHover-26V98q:hover {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    background-color: hsl(0, calc(var(--saturation-factor, 1)*80%), 98%)
}

.theme-dark .lookInverted-2mDUMi.hoverRed-1gTvle.hasHover-26V98q:active, .theme-light .lookInverted-2mDUMi.hoverRed-1gTvle.hasHover-26V98q:active {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    background-color: hsl(358, calc(var(--saturation-factor, 1)*81%), 91.8%)
}

.theme-dark .lookOutlined-3yKVGo.hoverRed-1gTvle.hasHover-26V98q:active, .theme-dark .lookOutlined-3yKVGo.hoverRed-1gTvle.hasHover-26V98q:hover, .theme-light .lookOutlined-3yKVGo.hoverRed-1gTvle.hasHover-26V98q:active, .theme-light .lookOutlined-3yKVGo.hoverRed-1gTvle.hasHover-26V98q:hover {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    border-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.theme-dark .lookOutlined-3yKVGo.hoverRed-1gTvle.hasHover-26V98q:active, .theme-light .lookOutlined-3yKVGo.hoverRed-1gTvle.hasHover-26V98q:active {
    background-color: hsla(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%, .1)
}

.theme-dark .lookLink-15mFoz.hoverRed-1gTvle.hasHover-26V98q:hover .contents-3ca1mk, .theme-light .lookLink-15mFoz.hoverRed-1gTvle.hasHover-26V98q:hover .contents-3ca1mk {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    background-image: linear-gradient(0deg, transparent, transparent 1px, hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%) 0, hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%) 2px, transparent 0)
}

.borderGreen-3MJerR {
    border-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%) !important
}

.lookFilled-yCfaCM.colorGreen-3y-Z79 {
    color: #fff;
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.lookFilled-yCfaCM.colorGreen-3y-Z79:hover {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.1%), 33.3%)
}

.lookFilled-yCfaCM.colorGreen-3y-Z79:active {
    background-color: hsl(138, calc(var(--saturation-factor, 1)*47%), 25.9%)
}

.lookFilled-yCfaCM.colorGreen-3y-Z79 .spinnerItem-3dCJpG {
    background-color: #fff
}

.lookFilled-yCfaCM.colorGreen-3y-Z79:disabled {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.lookInverted-2mDUMi.colorGreen-3y-Z79 {
    color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    background-color: #fff
}

.lookInverted-2mDUMi.colorGreen-3y-Z79:hover {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*83.8%), 92.7%)
}

.lookInverted-2mDUMi.colorGreen-3y-Z79:active {
    background-color: hsl(138, calc(var(--saturation-factor, 1)*86.1%), 85.9%)
}

.lookInverted-2mDUMi.colorGreen-3y-Z79:disabled {
    background-color: #fff
}

.lookInverted-2mDUMi.colorGreen-3y-Z79 .spinnerItem-3dCJpG {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.lookOutlined-3yKVGo.colorGreen-3y-Z79 {
    color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    border-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.lookOutlined-3yKVGo.colorGreen-3y-Z79:active {
    background-color: hsla(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%, .1)
}

.lookOutlined-3yKVGo.colorGreen-3y-Z79:disabled {
    background-color: transparent
}

.lookOutlined-3yKVGo.colorGreen-3y-Z79 .spinnerItem-3dCJpG {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.lookLink-15mFoz.colorGreen-3y-Z79 {
    color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.lookLink-15mFoz.colorGreen-3y-Z79:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%) 0, hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%) 2px, transparent 0)
}

.lookLink-15mFoz.colorGreen-3y-Z79:disabled .contents-3ca1mk {
    background-image: none
}

.lookLink-15mFoz.colorGreen-3y-Z79 .spinnerItem-3dCJpG {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.theme-dark .lookFilled-yCfaCM.hoverGreen-1KfT6K.hasHover-26V98q:hover, .theme-light .lookFilled-yCfaCM.hoverGreen-1KfT6K.hasHover-26V98q:hover {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    color: #fff
}

.theme-dark .lookFilled-yCfaCM.hoverGreen-1KfT6K.hasHover-26V98q:active, .theme-light .lookFilled-yCfaCM.hoverGreen-1KfT6K.hasHover-26V98q:active {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.1%), 33.3%);
    color: #fff
}

.theme-dark .lookInverted-2mDUMi.hoverGreen-1KfT6K.hasHover-26V98q:hover, .theme-light .lookInverted-2mDUMi.hoverGreen-1KfT6K.hasHover-26V98q:hover {
    color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    background-color: hsl(139, calc(var(--saturation-factor, 1)*83.8%), 92.7%)
}

.theme-dark .lookInverted-2mDUMi.hoverGreen-1KfT6K.hasHover-26V98q:active, .theme-light .lookInverted-2mDUMi.hoverGreen-1KfT6K.hasHover-26V98q:active {
    color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    background-color: hsl(138, calc(var(--saturation-factor, 1)*86.1%), 85.9%)
}

.theme-dark .lookOutlined-3yKVGo.hoverGreen-1KfT6K.hasHover-26V98q:active, .theme-dark .lookOutlined-3yKVGo.hoverGreen-1KfT6K.hasHover-26V98q:hover, .theme-light .lookOutlined-3yKVGo.hoverGreen-1KfT6K.hasHover-26V98q:active, .theme-light .lookOutlined-3yKVGo.hoverGreen-1KfT6K.hasHover-26V98q:hover {
    color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    border-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.theme-dark .lookOutlined-3yKVGo.hoverGreen-1KfT6K.hasHover-26V98q:active, .theme-light .lookOutlined-3yKVGo.hoverGreen-1KfT6K.hasHover-26V98q:active {
    background-color: hsla(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%, .1)
}

.theme-dark .lookLink-15mFoz.hoverGreen-1KfT6K.hasHover-26V98q:hover .contents-3ca1mk, .theme-light .lookLink-15mFoz.hoverGreen-1KfT6K.hasHover-26V98q:hover .contents-3ca1mk {
    color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    background-image: linear-gradient(0deg, transparent, transparent 1px, hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%) 0, hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%) 2px, transparent 0)
}

.theme-dark .borderGrey-gmqGL5 {
    border-color: #4f545c !important
}

.theme-dark .lookFilled-yCfaCM.colorGrey-2iAG-B {
    color: #fff;
    background-color: #4f545c
}

.theme-dark .lookFilled-yCfaCM.colorGrey-2iAG-B:hover {
    background-color: #5d6269
}

.theme-dark .lookFilled-yCfaCM.colorGrey-2iAG-B:active {
    background-color: #72767d
}

.theme-dark .lookFilled-yCfaCM.colorGrey-2iAG-B .spinnerItem-3dCJpG {
    background-color: #fff
}

.theme-dark .lookFilled-yCfaCM.colorGrey-2iAG-B:disabled {
    background-color: #4f545c
}

.theme-dark .lookInverted-2mDUMi.colorGrey-2iAG-B {
    color: #4f545c;
    background-color: #fff
}

.theme-dark .lookInverted-2mDUMi.colorGrey-2iAG-B:hover {
    background-color: #f6f6f7
}

.theme-dark .lookInverted-2mDUMi.colorGrey-2iAG-B:active {
    background-color: #dcddde
}

.theme-dark .lookInverted-2mDUMi.colorGrey-2iAG-B:disabled {
    background-color: #fff
}

.theme-dark .lookInverted-2mDUMi.colorGrey-2iAG-B .spinnerItem-3dCJpG {
    background-color: #4f545c
}

.theme-dark .lookOutlined-3yKVGo.colorGrey-2iAG-B {
    color: #4f545c;
    border-color: #4f545c
}

.theme-dark .lookOutlined-3yKVGo.colorGrey-2iAG-B:active {
    background-color: rgba(79, 84, 92, .1)
}

.theme-dark .lookOutlined-3yKVGo.colorGrey-2iAG-B:disabled {
    background-color: transparent
}

.theme-dark .lookOutlined-3yKVGo.colorGrey-2iAG-B .spinnerItem-3dCJpG {
    background-color: #4f545c
}

.theme-dark .lookLink-15mFoz.colorGrey-2iAG-B {
    color: #4f545c
}

.theme-dark .lookLink-15mFoz.colorGrey-2iAG-B:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, #4f545c 0, #4f545c 2px, transparent 0)
}

.theme-dark .lookLink-15mFoz.colorGrey-2iAG-B:disabled .contents-3ca1mk {
    background-image: none
}

.theme-dark .lookLink-15mFoz.colorGrey-2iAG-B .spinnerItem-3dCJpG {
    background-color: #4f545c
}

.theme-dark .theme-dark .lookFilled-yCfaCM.hoverGrey-JnW3T-.hasHover-26V98q:hover, .theme-dark .theme-light .lookFilled-yCfaCM.hoverGrey-JnW3T-.hasHover-26V98q:hover {
    background-color: #4f545c;
    color: #fff
}

.theme-dark .theme-dark .lookFilled-yCfaCM.hoverGrey-JnW3T-.hasHover-26V98q:active, .theme-dark .theme-light .lookFilled-yCfaCM.hoverGrey-JnW3T-.hasHover-26V98q:active {
    background-color: #5d6269;
    color: #fff
}

.theme-dark .theme-dark .lookInverted-2mDUMi.hoverGrey-JnW3T-.hasHover-26V98q:hover, .theme-dark .theme-light .lookInverted-2mDUMi.hoverGrey-JnW3T-.hasHover-26V98q:hover {
    color: #4f545c;
    background-color: #f6f6f7
}

.theme-dark .theme-dark .lookInverted-2mDUMi.hoverGrey-JnW3T-.hasHover-26V98q:active, .theme-dark .theme-light .lookInverted-2mDUMi.hoverGrey-JnW3T-.hasHover-26V98q:active {
    color: #4f545c;
    background-color: #dcddde
}

.theme-dark .theme-dark .lookOutlined-3yKVGo.hoverGrey-JnW3T-.hasHover-26V98q:hover, .theme-dark .theme-light .lookOutlined-3yKVGo.hoverGrey-JnW3T-.hasHover-26V98q:hover {
    color: #4f545c;
    border-color: #4f545c
}

.theme-dark .theme-dark .lookOutlined-3yKVGo.hoverGrey-JnW3T-.hasHover-26V98q:active, .theme-dark .theme-light .lookOutlined-3yKVGo.hoverGrey-JnW3T-.hasHover-26V98q:active {
    color: #4f545c;
    background-color: rgba(79, 84, 92, .1);
    border-color: #4f545c
}

.theme-dark .theme-dark .lookLink-15mFoz.hoverGrey-JnW3T-.hasHover-26V98q:hover .contents-3ca1mk, .theme-dark .theme-light .lookLink-15mFoz.hoverGrey-JnW3T-.hasHover-26V98q:hover .contents-3ca1mk {
    color: #4f545c;
    background-image: linear-gradient(0deg, transparent, transparent 1px, #4f545c 0, #4f545c 2px, transparent 0)
}

.theme-dark .borderPrimary-1ygM7_ {
    border-color: #fff !important
}

.theme-dark .lookFilled-yCfaCM.colorPrimary-2AuQVo {
    color: #f6f6f7;
    background-color: #4f545c
}

.theme-dark .lookFilled-yCfaCM.colorPrimary-2AuQVo:active, .theme-dark .lookFilled-yCfaCM.colorPrimary-2AuQVo:hover {
    background-color: #72767d
}

.theme-dark .lookFilled-yCfaCM.colorPrimary-2AuQVo .spinnerItem-3dCJpG {
    background-color: #f6f6f7
}

.theme-dark .lookFilled-yCfaCM.colorPrimary-2AuQVo:disabled {
    background-color: #4f545c
}

.theme-dark .lookInverted-2mDUMi.colorPrimary-2AuQVo {
    color: #4f545c;
    background-color: #f6f6f7
}

.theme-dark .lookInverted-2mDUMi.colorPrimary-2AuQVo:hover {
    background-color: #f6f6f7
}

.theme-dark .lookInverted-2mDUMi.colorPrimary-2AuQVo:active {
    background-color: #dcddde
}

.theme-dark .lookInverted-2mDUMi.colorPrimary-2AuQVo:disabled {
    background-color: #f6f6f7
}

.theme-dark .lookInverted-2mDUMi.colorPrimary-2AuQVo .spinnerItem-3dCJpG {
    background-color: #4f545c
}

.theme-dark .lookOutlined-3yKVGo.colorPrimary-2AuQVo {
    color: #fff;
    border-color: #fff
}

.theme-dark .lookOutlined-3yKVGo.colorPrimary-2AuQVo:active {
    background-color: hsla(0, 0%, 100%, .1)
}

.theme-dark .lookOutlined-3yKVGo.colorPrimary-2AuQVo:disabled {
    background-color: transparent
}

.theme-dark .lookOutlined-3yKVGo.colorPrimary-2AuQVo .spinnerItem-3dCJpG {
    background-color: #fff
}

.theme-dark .lookLink-15mFoz.colorPrimary-2AuQVo {
    color: #fff
}

.theme-dark .lookLink-15mFoz.colorPrimary-2AuQVo:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, #fff 0, #fff 2px, transparent 0)
}

.theme-dark .lookLink-15mFoz.colorPrimary-2AuQVo:disabled .contents-3ca1mk {
    background-image: none
}

.theme-dark .lookLink-15mFoz.colorPrimary-2AuQVo .spinnerItem-3dCJpG {
    background-color: #fff
}

.theme-dark .theme-dark .lookFilled-yCfaCM.hoverPrimary-2hqNIm.hasHover-26V98q:hover, .theme-dark .theme-light .lookFilled-yCfaCM.hoverPrimary-2hqNIm.hasHover-26V98q:hover {
    background-color: #4f545c;
    color: #f6f6f7
}

.theme-dark .theme-dark .lookFilled-yCfaCM.hoverPrimary-2hqNIm.hasHover-26V98q:active, .theme-dark .theme-light .lookFilled-yCfaCM.hoverPrimary-2hqNIm.hasHover-26V98q:active {
    background-color: #72767d;
    color: #f6f6f7
}

.theme-dark .theme-dark .lookInverted-2mDUMi.hoverPrimary-2hqNIm.hasHover-26V98q:hover, .theme-dark .theme-light .lookInverted-2mDUMi.hoverPrimary-2hqNIm.hasHover-26V98q:hover {
    color: #4f545c;
    background-color: #f6f6f7
}

.theme-dark .theme-dark .lookInverted-2mDUMi.hoverPrimary-2hqNIm.hasHover-26V98q:active, .theme-dark .theme-light .lookInverted-2mDUMi.hoverPrimary-2hqNIm.hasHover-26V98q:active {
    color: #4f545c;
    background-color: #dcddde
}

.theme-dark .theme-dark .lookOutlined-3yKVGo.hoverPrimary-2hqNIm.hasHover-26V98q:hover, .theme-dark .theme-light .lookOutlined-3yKVGo.hoverPrimary-2hqNIm.hasHover-26V98q:hover {
    color: #fff;
    border-color: #fff
}

.theme-dark .theme-dark .lookOutlined-3yKVGo.hoverPrimary-2hqNIm.hasHover-26V98q:active, .theme-dark .theme-light .lookOutlined-3yKVGo.hoverPrimary-2hqNIm.hasHover-26V98q:active {
    color: #fff;
    background-color: hsla(0, 0%, 100%, .1);
    border-color: #4f545c
}

.theme-dark .theme-dark .lookLink-15mFoz.hoverPrimary-2hqNIm.hasHover-26V98q:hover .contents-3ca1mk, .theme-dark .theme-light .lookLink-15mFoz.hoverPrimary-2hqNIm.hasHover-26V98q:hover .contents-3ca1mk {
    color: #fff;
    background-image: linear-gradient(0deg, transparent, transparent 1px, #fff 0, #fff 2px, transparent 0)
}

.theme-dark .borderTransparent-2P3AAk {
    border-color: #dcddde !important
}

.theme-dark .lookFilled-yCfaCM.colorTransparent-13Bvvi {
    color: #f6f6f7;
    background-color: hsla(0, 0%, 100%, .1)
}

.theme-dark .lookFilled-yCfaCM.colorTransparent-13Bvvi:hover {
    background-color: hsla(0, 0%, 100%, .05)
}

.theme-dark .lookFilled-yCfaCM.colorTransparent-13Bvvi:active {
    background-color: hsla(0, 0%, 100%, .01)
}

.theme-dark .lookFilled-yCfaCM.colorTransparent-13Bvvi .spinnerItem-3dCJpG {
    background-color: #f6f6f7
}

.theme-dark .lookFilled-yCfaCM.colorTransparent-13Bvvi:disabled {
    background-color: hsla(0, 0%, 100%, .1)
}

.theme-dark .lookInverted-2mDUMi.colorTransparent-13Bvvi {
    color: hsla(0, 0%, 100%, .1);
    background-color: #f6f6f7
}

.theme-dark .lookInverted-2mDUMi.colorTransparent-13Bvvi:hover {
    background-color: hsla(0, 0%, 100%, .05)
}

.theme-dark .lookInverted-2mDUMi.colorTransparent-13Bvvi:active {
    background-color: hsla(0, 0%, 100%, .1)
}

.theme-dark .lookInverted-2mDUMi.colorTransparent-13Bvvi:disabled {
    background-color: #f6f6f7
}

.theme-dark .lookInverted-2mDUMi.colorTransparent-13Bvvi .spinnerItem-3dCJpG {
    background-color: hsla(0, 0%, 100%, .1)
}

.theme-dark .lookOutlined-3yKVGo.colorTransparent-13Bvvi {
    color: #dcddde;
    border-color: #dcddde
}

.theme-dark .lookOutlined-3yKVGo.colorTransparent-13Bvvi:active {
    background-color: rgba(220, 221, 222, .1)
}

.theme-dark .lookOutlined-3yKVGo.colorTransparent-13Bvvi:disabled {
    background-color: transparent
}

.theme-dark .lookOutlined-3yKVGo.colorTransparent-13Bvvi .spinnerItem-3dCJpG {
    background-color: #dcddde
}

.theme-dark .lookLink-15mFoz.colorTransparent-13Bvvi {
    color: #dcddde
}

.theme-dark .lookLink-15mFoz.colorTransparent-13Bvvi:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, #dcddde 0, #dcddde 2px, transparent 0)
}

.theme-dark .lookLink-15mFoz.colorTransparent-13Bvvi:disabled .contents-3ca1mk {
    background-image: none
}

.theme-dark .lookLink-15mFoz.colorTransparent-13Bvvi .spinnerItem-3dCJpG {
    background-color: #dcddde
}

.theme-dark .theme-dark .lookFilled-yCfaCM.hoverTransparent-1F6BzX.hasHover-26V98q:hover, .theme-dark .theme-light .lookFilled-yCfaCM.hoverTransparent-1F6BzX.hasHover-26V98q:hover {
    background-color: hsla(0, 0%, 100%, .1);
    color: #f6f6f7
}

.theme-dark .theme-dark .lookFilled-yCfaCM.hoverTransparent-1F6BzX.hasHover-26V98q:active, .theme-dark .theme-light .lookFilled-yCfaCM.hoverTransparent-1F6BzX.hasHover-26V98q:active {
    background-color: hsla(0, 0%, 100%, .05);
    color: #f6f6f7
}

.theme-dark .theme-dark .lookInverted-2mDUMi.hoverTransparent-1F6BzX.hasHover-26V98q:hover, .theme-dark .theme-light .lookInverted-2mDUMi.hoverTransparent-1F6BzX.hasHover-26V98q:hover {
    color: hsla(0, 0%, 100%, .1);
    background-color: hsla(0, 0%, 100%, .05)
}

.theme-dark .theme-dark .lookInverted-2mDUMi.hoverTransparent-1F6BzX.hasHover-26V98q:active, .theme-dark .theme-light .lookInverted-2mDUMi.hoverTransparent-1F6BzX.hasHover-26V98q:active {
    color: hsla(0, 0%, 100%, .1);
    background-color: hsla(0, 0%, 100%, .1)
}

.theme-dark .theme-dark .lookOutlined-3yKVGo.hoverTransparent-1F6BzX.hasHover-26V98q:hover, .theme-dark .theme-light .lookOutlined-3yKVGo.hoverTransparent-1F6BzX.hasHover-26V98q:hover {
    color: #dcddde;
    border-color: #dcddde
}

.theme-dark .theme-dark .lookOutlined-3yKVGo.hoverTransparent-1F6BzX.hasHover-26V98q:active, .theme-dark .theme-light .lookOutlined-3yKVGo.hoverTransparent-1F6BzX.hasHover-26V98q:active {
    color: #dcddde;
    background-color: rgba(220, 221, 222, .1);
    border-color: hsla(0, 0%, 100%, .1)
}

.theme-dark .theme-dark .lookLink-15mFoz.hoverTransparent-1F6BzX.hasHover-26V98q:hover .contents-3ca1mk, .theme-dark .theme-light .lookLink-15mFoz.hoverTransparent-1F6BzX.hasHover-26V98q:hover .contents-3ca1mk {
    color: #dcddde;
    background-image: linear-gradient(0deg, transparent, transparent 1px, #dcddde 0, #dcddde 2px, transparent 0)
}

.theme-light .borderGrey-gmqGL5 {
    border-color: #747f8d !important
}

.theme-light .lookFilled-yCfaCM.colorGrey-2iAG-B {
    color: #fff;
    background-color: #747f8d
}

.theme-light .lookFilled-yCfaCM.colorGrey-2iAG-B:hover {
    background-color: #5e6772
}

.theme-light .lookFilled-yCfaCM.colorGrey-2iAG-B:active {
    background-color: #4f5660
}

.theme-light .lookFilled-yCfaCM.colorGrey-2iAG-B .spinnerItem-3dCJpG {
    background-color: #fff
}

.theme-light .lookFilled-yCfaCM.colorGrey-2iAG-B:disabled {
    background-color: #747f8d
}

.theme-light .lookInverted-2mDUMi.colorGrey-2iAG-B {
    color: #747f8d;
    background-color: #fff
}

.theme-light .lookInverted-2mDUMi.colorGrey-2iAG-B:hover {
    background-color: #f8f9f9
}

.theme-light .lookInverted-2mDUMi.colorGrey-2iAG-B:active {
    background-color: #e3e5e8
}

.theme-light .lookInverted-2mDUMi.colorGrey-2iAG-B:disabled {
    background-color: #fff
}

.theme-light .lookInverted-2mDUMi.colorGrey-2iAG-B .spinnerItem-3dCJpG {
    background-color: #747f8d
}

.theme-light .lookOutlined-3yKVGo.colorGrey-2iAG-B {
    color: #747f8d;
    border-color: #747f8d
}

.theme-light .lookOutlined-3yKVGo.colorGrey-2iAG-B:active {
    background-color: rgba(116, 127, 141, .1)
}

.theme-light .lookOutlined-3yKVGo.colorGrey-2iAG-B:disabled {
    background-color: transparent
}

.theme-light .lookOutlined-3yKVGo.colorGrey-2iAG-B .spinnerItem-3dCJpG {
    background-color: #747f8d
}

.theme-light .lookLink-15mFoz.colorGrey-2iAG-B {
    color: #747f8d
}

.theme-light .lookLink-15mFoz.colorGrey-2iAG-B:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, #747f8d 0, #747f8d 2px, transparent 0)
}

.theme-light .lookLink-15mFoz.colorGrey-2iAG-B:disabled .contents-3ca1mk {
    background-image: none
}

.theme-light .lookLink-15mFoz.colorGrey-2iAG-B .spinnerItem-3dCJpG {
    background-color: #747f8d
}

.theme-light .theme-dark .lookFilled-yCfaCM.hoverGrey-JnW3T-.hasHover-26V98q:hover, .theme-light .theme-light .lookFilled-yCfaCM.hoverGrey-JnW3T-.hasHover-26V98q:hover {
    background-color: #747f8d;
    color: #fff
}

.theme-light .theme-dark .lookFilled-yCfaCM.hoverGrey-JnW3T-.hasHover-26V98q:active, .theme-light .theme-light .lookFilled-yCfaCM.hoverGrey-JnW3T-.hasHover-26V98q:active {
    background-color: #5e6772;
    color: #fff
}

.theme-light .theme-dark .lookInverted-2mDUMi.hoverGrey-JnW3T-.hasHover-26V98q:hover, .theme-light .theme-light .lookInverted-2mDUMi.hoverGrey-JnW3T-.hasHover-26V98q:hover {
    color: #747f8d;
    background-color: #f8f9f9
}

.theme-light .theme-dark .lookInverted-2mDUMi.hoverGrey-JnW3T-.hasHover-26V98q:active, .theme-light .theme-light .lookInverted-2mDUMi.hoverGrey-JnW3T-.hasHover-26V98q:active {
    color: #747f8d;
    background-color: #e3e5e8
}

.theme-light .theme-dark .lookOutlined-3yKVGo.hoverGrey-JnW3T-.hasHover-26V98q:hover, .theme-light .theme-light .lookOutlined-3yKVGo.hoverGrey-JnW3T-.hasHover-26V98q:hover {
    color: #747f8d;
    border-color: #747f8d
}

.theme-light .theme-dark .lookOutlined-3yKVGo.hoverGrey-JnW3T-.hasHover-26V98q:active, .theme-light .theme-light .lookOutlined-3yKVGo.hoverGrey-JnW3T-.hasHover-26V98q:active {
    color: #747f8d;
    background-color: rgba(116, 127, 141, .1);
    border-color: #747f8d
}

.theme-light .theme-dark .lookLink-15mFoz.hoverGrey-JnW3T-.hasHover-26V98q:hover .contents-3ca1mk, .theme-light .theme-light .lookLink-15mFoz.hoverGrey-JnW3T-.hasHover-26V98q:hover .contents-3ca1mk {
    color: #747f8d;
    background-image: linear-gradient(0deg, transparent, transparent 1px, #747f8d 0, #747f8d 2px, transparent 0)
}

.theme-light .borderPrimary-1ygM7_ {
    border-color: #747f8d !important
}

.theme-light .lookFilled-yCfaCM.colorPrimary-2AuQVo {
    color: #fff;
    background-color: #747f8d
}

.theme-light .lookFilled-yCfaCM.colorPrimary-2AuQVo:hover {
    background-color: #9099a4
}

.theme-light .lookFilled-yCfaCM.colorPrimary-2AuQVo:active {
    background-color: #e3e5e8
}

.theme-light .lookFilled-yCfaCM.colorPrimary-2AuQVo .spinnerItem-3dCJpG {
    background-color: #fff
}

.theme-light .lookFilled-yCfaCM.colorPrimary-2AuQVo:disabled {
    background-color: #747f8d
}

.theme-light .lookInverted-2mDUMi.colorPrimary-2AuQVo {
    color: #747f8d;
    background-color: #fff
}

.theme-light .lookInverted-2mDUMi.colorPrimary-2AuQVo:hover {
    background-color: #4f5660
}

.theme-light .lookInverted-2mDUMi.colorPrimary-2AuQVo:active {
    background-color: #2e3338
}

.theme-light .lookInverted-2mDUMi.colorPrimary-2AuQVo:disabled {
    background-color: #fff
}

.theme-light .lookInverted-2mDUMi.colorPrimary-2AuQVo .spinnerItem-3dCJpG {
    background-color: #747f8d
}

.theme-light .lookOutlined-3yKVGo.colorPrimary-2AuQVo {
    color: #747f8d;
    border-color: #747f8d
}

.theme-light .lookOutlined-3yKVGo.colorPrimary-2AuQVo:active {
    background-color: rgba(116, 127, 141, .1)
}

.theme-light .lookOutlined-3yKVGo.colorPrimary-2AuQVo:disabled {
    background-color: transparent
}

.theme-light .lookOutlined-3yKVGo.colorPrimary-2AuQVo .spinnerItem-3dCJpG {
    background-color: #747f8d
}

.theme-light .lookLink-15mFoz.colorPrimary-2AuQVo {
    color: #747f8d
}

.theme-light .lookLink-15mFoz.colorPrimary-2AuQVo:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, #747f8d 0, #747f8d 2px, transparent 0)
}

.theme-light .lookLink-15mFoz.colorPrimary-2AuQVo:disabled .contents-3ca1mk {
    background-image: none
}

.theme-light .lookLink-15mFoz.colorPrimary-2AuQVo .spinnerItem-3dCJpG {
    background-color: #747f8d
}

.theme-light .theme-dark .lookFilled-yCfaCM.hoverPrimary-2hqNIm.hasHover-26V98q:hover, .theme-light .theme-light .lookFilled-yCfaCM.hoverPrimary-2hqNIm.hasHover-26V98q:hover {
    background-color: #747f8d;
    color: #fff
}

.theme-light .theme-dark .lookFilled-yCfaCM.hoverPrimary-2hqNIm.hasHover-26V98q:active, .theme-light .theme-light .lookFilled-yCfaCM.hoverPrimary-2hqNIm.hasHover-26V98q:active {
    background-color: #9099a4;
    color: #fff
}

.theme-light .theme-dark .lookInverted-2mDUMi.hoverPrimary-2hqNIm.hasHover-26V98q:hover, .theme-light .theme-light .lookInverted-2mDUMi.hoverPrimary-2hqNIm.hasHover-26V98q:hover {
    color: #747f8d;
    background-color: #4f5660
}

.theme-light .theme-dark .lookInverted-2mDUMi.hoverPrimary-2hqNIm.hasHover-26V98q:active, .theme-light .theme-light .lookInverted-2mDUMi.hoverPrimary-2hqNIm.hasHover-26V98q:active {
    color: #747f8d;
    background-color: #2e3338
}

.theme-light .theme-dark .lookOutlined-3yKVGo.hoverPrimary-2hqNIm.hasHover-26V98q:hover, .theme-light .theme-light .lookOutlined-3yKVGo.hoverPrimary-2hqNIm.hasHover-26V98q:hover {
    color: #747f8d;
    border-color: #747f8d
}

.theme-light .theme-dark .lookOutlined-3yKVGo.hoverPrimary-2hqNIm.hasHover-26V98q:active, .theme-light .theme-light .lookOutlined-3yKVGo.hoverPrimary-2hqNIm.hasHover-26V98q:active {
    color: #747f8d;
    background-color: rgba(116, 127, 141, .1);
    border-color: #747f8d
}

.theme-light .theme-dark .lookLink-15mFoz.hoverPrimary-2hqNIm.hasHover-26V98q:hover .contents-3ca1mk, .theme-light .theme-light .lookLink-15mFoz.hoverPrimary-2hqNIm.hasHover-26V98q:hover .contents-3ca1mk {
    color: #747f8d;
    background-image: linear-gradient(0deg, transparent, transparent 1px, #747f8d 0, #747f8d 2px, transparent 0)
}

.theme-light .borderTransparent-2P3AAk {
    border-color: #747f8d !important
}

.theme-light .lookFilled-yCfaCM.colorTransparent-13Bvvi {
    color: #747f8d;
    background-color: rgba(116, 127, 141, .01)
}

.theme-light .lookFilled-yCfaCM.colorTransparent-13Bvvi:hover {
    background-color: rgba(116, 127, 141, .2)
}

.theme-light .lookFilled-yCfaCM.colorTransparent-13Bvvi:active {
    background-color: rgba(116, 127, 141, .25)
}

.theme-light .lookFilled-yCfaCM.colorTransparent-13Bvvi .spinnerItem-3dCJpG {
    background-color: #747f8d
}

.theme-light .lookFilled-yCfaCM.colorTransparent-13Bvvi:disabled {
    background-color: rgba(116, 127, 141, .01)
}

.theme-light .lookInverted-2mDUMi.colorTransparent-13Bvvi {
    color: rgba(116, 127, 141, .01);
    background-color: #747f8d
}

.theme-light .lookInverted-2mDUMi.colorTransparent-13Bvvi:hover {
    background-color: rgba(116, 127, 141, .2)
}

.theme-light .lookInverted-2mDUMi.colorTransparent-13Bvvi:active {
    background-color: rgba(116, 127, 141, .25)
}

.theme-light .lookInverted-2mDUMi.colorTransparent-13Bvvi:disabled {
    background-color: #747f8d
}

.theme-light .lookInverted-2mDUMi.colorTransparent-13Bvvi .spinnerItem-3dCJpG {
    background-color: rgba(116, 127, 141, .01)
}

.theme-light .lookOutlined-3yKVGo.colorTransparent-13Bvvi {
    color: #747f8d;
    border-color: #747f8d
}

.theme-light .lookOutlined-3yKVGo.colorTransparent-13Bvvi:active {
    background-color: rgba(116, 127, 141, .1)
}

.theme-light .lookOutlined-3yKVGo.colorTransparent-13Bvvi:disabled {
    background-color: transparent
}

.theme-light .lookOutlined-3yKVGo.colorTransparent-13Bvvi .spinnerItem-3dCJpG {
    background-color: #747f8d
}

.theme-light .lookLink-15mFoz.colorTransparent-13Bvvi {
    color: #747f8d
}

.theme-light .lookLink-15mFoz.colorTransparent-13Bvvi:hover .contents-3ca1mk {
    background-image: linear-gradient(0deg, transparent, transparent 1px, #747f8d 0, #747f8d 2px, transparent 0)
}

.theme-light .lookLink-15mFoz.colorTransparent-13Bvvi:disabled .contents-3ca1mk {
    background-image: none
}

.theme-light .lookLink-15mFoz.colorTransparent-13Bvvi .spinnerItem-3dCJpG {
    background-color: #747f8d
}

.theme-light .theme-dark .lookFilled-yCfaCM.hoverTransparent-1F6BzX.hasHover-26V98q:hover, .theme-light .theme-light .lookFilled-yCfaCM.hoverTransparent-1F6BzX.hasHover-26V98q:hover {
    background-color: rgba(116, 127, 141, .01);
    color: #747f8d
}

.theme-light .theme-dark .lookFilled-yCfaCM.hoverTransparent-1F6BzX.hasHover-26V98q:active, .theme-light .theme-light .lookFilled-yCfaCM.hoverTransparent-1F6BzX.hasHover-26V98q:active {
    background-color: rgba(116, 127, 141, .2);
    color: #747f8d
}

.theme-light .theme-dark .lookInverted-2mDUMi.hoverTransparent-1F6BzX.hasHover-26V98q:hover, .theme-light .theme-light .lookInverted-2mDUMi.hoverTransparent-1F6BzX.hasHover-26V98q:hover {
    color: rgba(116, 127, 141, .01);
    background-color: rgba(116, 127, 141, .2)
}

.theme-light .theme-dark .lookInverted-2mDUMi.hoverTransparent-1F6BzX.hasHover-26V98q:active, .theme-light .theme-light .lookInverted-2mDUMi.hoverTransparent-1F6BzX.hasHover-26V98q:active {
    color: rgba(116, 127, 141, .01);
    background-color: rgba(116, 127, 141, .25)
}

.theme-light .theme-dark .lookOutlined-3yKVGo.hoverTransparent-1F6BzX.hasHover-26V98q:hover, .theme-light .theme-light .lookOutlined-3yKVGo.hoverTransparent-1F6BzX.hasHover-26V98q:hover {
    color: #747f8d;
    border-color: #747f8d
}

.theme-light .theme-dark .lookOutlined-3yKVGo.hoverTransparent-1F6BzX.hasHover-26V98q:active, .theme-light .theme-light .lookOutlined-3yKVGo.hoverTransparent-1F6BzX.hasHover-26V98q:active {
    color: #747f8d;
    background-color: rgba(116, 127, 141, .1);
    border-color: rgba(116, 127, 141, .01)
}

.theme-light .theme-dark .lookLink-15mFoz.hoverTransparent-1F6BzX.hasHover-26V98q:hover .contents-3ca1mk, .theme-light .theme-light .lookLink-15mFoz.hoverTransparent-1F6BzX.hasHover-26V98q:hover .contents-3ca1mk {
    color: #747f8d;
    background-image: linear-gradient(0deg, transparent, transparent 1px, #747f8d 0, #747f8d 2px, transparent 0)
}

.lookFilled-yCfaCM, .lookInverted-2mDUMi {
    -webkit-transition: background-color .17s ease, color .17s ease;
    transition: background-color .17s ease, color .17s ease
}

.lookOutlined-3yKVGo {
    -webkit-transition: color .17s ease, background-color .17s ease, border-color .17s ease;
    transition: color .17s ease, background-color .17s ease, border-color .17s ease;
    border-width: 1px;
    border-style: solid
}

.lookBlank-21BCro {
    background: transparent;
    color: currentColor;
    border: 0;
    padding: 0;
    margin: 0
}

.sizeTiny-3y2SSK {
    width: 52px;
    height: 24px;
    min-width: 52px;
    min-height: 24px
}

.sizeSmall-wU2dO- {
    width: 60px;
    height: 32px;
    min-width: 60px;
    min-height: 32px
}

.sizeMedium-2bFIHr {
    width: 96px;
    height: 38px;
    min-width: 96px;
    min-height: 38px
}

.sizeLarge-3mScP9 {
    width: 130px;
    height: 44px;
    min-width: 130px;
    min-height: 44px
}

.sizeXlarge-2TyITf {
    width: 148px;
    height: 50px;
    min-width: 148px;
    min-height: 50px;
    font-size: 16px;
    line-height: normal;
    padding: 2px 20px
}

.sizeMin-DfpWCE {
    display: inline;
    width: auto;
    height: auto;
    padding: 2px 4px
}

.sizeMax-1iNmdF {
    width: 100%;
    height: 100%;
    min-width: 100%;
    min-height: 100%;
    font-size: 16px
}

.sizeIcon-1kBo12 {
    height: auto;
    padding: 4px
}

.grow-2sR_-F, .sizeIcon-1kBo12 {
    width: auto
}

.fullWidth-fJIsjq {
    width: 100%
}

.submitting-3uDn4C {
    pointer-events: none
}

.lookFilled-yCfaCM .contents-3ca1mk, .lookInverted-2mDUMi .contents-3ca1mk, .lookLink-15mFoz .contents-3ca1mk, .lookOutlined-3yKVGo .contents-3ca1mk {
    margin: 0 auto;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden
}

.lookLink-15mFoz {}

@media (-ms-high-contrast:active), (-ms-high-contrast:none) {
    .lookFilled-yCfaCM .contents-3ca1mk, .lookInverted-2mDUMi .contents-3ca1mk, .lookLink-15mFoz .contents-3ca1mk, .lookOutlined-3yKVGo .contents-3ca1mk {
        margin: 0
    }
}

.submitting-3uDn4C .contents-3ca1mk {
    visibility: hidden
}

.spinner-3lTjTx {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0
}

.disabledButtonWrapper-2Oz3aY {
    display: inline-block;
    position: relative
}

.disabledButtonOverlay-1_LsqE {
    cursor: not-allowed;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9
}

.flex-3BkGQD {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.alignStart-2nShwY {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.alignEnd-2awoY_ {
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end
}

.alignCenter-14kD11 {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.alignStretch-Uwowzr {
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch
}

.alignBaseline-2kE5ql {
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline
}

.justifyStart-2Mwniq {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.justifyEnd-2G0m6w {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

.justifyCenter-rrurWZ {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.justifyAround-1MJ202 {
    -ms-flex-pack: distribute;
    justify-content: space-around
}

.justifyBetween-wAERV6 {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.noWrap-hBpHBz {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap
}

.wrap-7NZuTn {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.wrapReverse-jL3MY8 {
    -ms-flex-wrap: wrap-reverse;
    flex-wrap: wrap-reverse
}

.directionRow-2Iu2A9 {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
}

.directionRowReverse-HZatnx {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse
}

.directionColumn-3pi1nm {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.spacer-2upayl {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: hidden
}

.vertical-3aLnqW {}

.horizontal-1Piu5- {}

.horizontalReverse-2QssvL {}

.horizontal-1Piu5->.spacer-2upayl, .horizontalReverse-2QssvL>.spacer-2upayl, .vertical-3aLnqW>.spacer-2upayl {
    min-height: 1px
}

.flexCenter-1Mwsxg {}

.flex-2S1XBF {}

.horizontal-112GEH {}

.horizontalReverse-60Katr {}

.horizontal-112GEH>.flex-2S1XBF, .horizontal-112GEH>.flexChild-3PzYmX {
    margin-left: 10px;
    margin-right: 10px
}

.horizontal-112GEH>.flex-2S1XBF:first-child, .horizontal-112GEH>.flexChild-3PzYmX:first-child {
    margin-left: 0
}

.horizontal-112GEH>.flex-2S1XBF:last-child, .horizontal-112GEH>.flexChild-3PzYmX:last-child {
    margin-right: 0
}

.horizontalReverse-60Katr>.flex-2S1XBF, .horizontalReverse-60Katr>.flexChild-3PzYmX {
    margin-left: 10px;
    margin-right: 10px
}

.horizontalReverse-60Katr>.flex-2S1XBF:first-child, .horizontalReverse-60Katr>.flexChild-3PzYmX:first-child {
    margin-right: 0
}

.horizontalReverse-60Katr>.flex-2S1XBF:last-child, .horizontalReverse-60Katr>.flexChild-3PzYmX:last-child {
    margin-left: 0
}

.horizontal-112GEH>.flexMarginReset-3iIFcE {
    margin: 0
}

.scrollerBase-_bVAAt {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    min-height: 0;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto
}

.thin-31rlnD {
    scrollbar-width: thin
}

.thin-31rlnD, .thin-31rlnD.fade-1R6FHN:hover, .thin-31rlnD.scrolling-20Of9p {
    scrollbar-color: var(--scrollbar-thin-thumb) var(--scrollbar-thin-track)
}

.thin-31rlnD::-webkit-scrollbar {
    width: 8px;
    height: 8px
}

.thin-31rlnD::-webkit-scrollbar-track {
    border-color: var(--scrollbar-thin-track);
    background-color: var(--scrollbar-thin-track);
    border: 2px solid var(--scrollbar-thin-track)
}

.thin-31rlnD::-webkit-scrollbar-thumb {
    background-clip: padding-box;
    border: 2px solid transparent;
    border-radius: 4px;
    background-color: var(--scrollbar-thin-thumb);
    min-height: 40px
}

.thin-31rlnD::-webkit-scrollbar-corner {
    background-color: transparent
}

.auto-2K3UW5 {
    scrollbar-width: auto
}

.auto-2K3UW5, .auto-2K3UW5.fade-1R6FHN:hover, .auto-2K3UW5.scrolling-20Of9p {
    scrollbar-color: var(--scrollbar-auto-scrollbar-color-thumb) var(--scrollbar-auto-scrollbar-color-track)
}

.auto-2K3UW5::-webkit-scrollbar {
    width: 16px;
    height: 16px
}

.auto-2K3UW5::-webkit-scrollbar-track {
    background-color: var(--scrollbar-auto-track)
}

.auto-2K3UW5::-webkit-scrollbar-thumb, .auto-2K3UW5::-webkit-scrollbar-track {
    border: 4px solid transparent;
    background-clip: padding-box;
    border-radius: 8px
}

.auto-2K3UW5::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-auto-thumb);
    min-height: 40px
}

.auto-2K3UW5::-webkit-scrollbar-corner {
    background-color: transparent
}

.none-2-_0dP {
    scrollbar-width: none
}

.none-2-_0dP::-webkit-scrollbar {
    width: 0;
    height: 0
}

.fade-1R6FHN {
    scrollbar-color: transparent transparent
}

.fade-1R6FHN::-webkit-scrollbar-thumb, .fade-1R6FHN::-webkit-scrollbar-track {
    visibility: hidden
}

.fade-1R6FHN:hover::-webkit-scrollbar-thumb, .fade-1R6FHN:hover::-webkit-scrollbar-track {
    visibility: visible
}

.content-2a4AW9 {
    position: relative
}

.scrolling-20Of9p .content-2a4AW9 {
    pointer-events: none
}

.scrolling-20Of9p.fade-1R6FHN::-webkit-scrollbar-thumb, .scrolling-20Of9p.fade-1R6FHN::-webkit-scrollbar-track {
    visibility: visible
}

.disableScrollAnchor-6TwzvM {
    overflow-anchor: none
}

.managedReactiveScroller-1lEEh3 {
    overflow-y: scroll;
    overflow-x: hidden
}

.enable-forced-colors ::-webkit-scrollbar-track {
    border-radius: 0;
    border-width: 1px
}

.enable-forced-colors ::-webkit-scrollbar-thumb {
    background-color: CanvasText;
    border-width: 1px
}

.enable-forced-colors ::-webkit-scrollbar-thumb:horizontal:active, .enable-forced-colors ::-webkit-scrollbar-thumb:horizontal:hover, .enable-forced-colors ::-webkit-scrollbar-thumb:vertical:active, .enable-forced-colors ::-webkit-scrollbar-thumb:vertical:hover {
    background-color: Highlight
}

.enable-forced-colors .auto-2K3UW5::-webkit-scrollbar {
    width: 8px;
    height: 8px
}

.enable-forced-colors .auto-2K3UW5::-webkit-scrollbar-track {
    border-width: 1px;
    border-radius: 0
}

.scrollbar-3vVt8d::-webkit-scrollbar-corner {
    border: none;
    background: none
}

.scrollbarDefault-2w-Dyz {}

.scrollbarDefault-2w-Dyz::-webkit-scrollbar {
    width: 14px;
    height: 14px
}

.scrollbarDefault-2w-Dyz::-webkit-scrollbar-thumb, .scrollbarDefault-2w-Dyz::-webkit-scrollbar-track {
    border-radius: 7px;
    background-clip: padding-box;
    border: 3px solid transparent
}

.scrollbarDefault-2w-Dyz::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-auto-thumb)
}

.scrollbarDefault-2w-Dyz::-webkit-scrollbar-track {
    background-color: var(--scrollbar-auto-track);
    border-width: initial
}

.scrollbarGhost-dCZKgZ {}

.scrollbarGhost-dCZKgZ::-webkit-scrollbar {
    width: 14px;
    height: 14px
}

.scrollbarGhost-dCZKgZ::-webkit-scrollbar-thumb, .scrollbarGhost-dCZKgZ::-webkit-scrollbar-track {
    border-width: 3px;
    border-radius: 7px;
    background-clip: padding-box;
    border-style: solid
}

.scrollbarGhost-dCZKgZ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, .4);
    border-color: transparent
}

.scrollbarGhost-dCZKgZ::-webkit-scrollbar-track {
    border-width: initial;
    border-color: transparent;
    background-color: rgba(0, 0, 0, .1)
}

.scrollbarGhostHairline-2LpzZ9 {}

.scrollbarGhostHairline-2LpzZ9::-webkit-scrollbar {
    width: 4px;
    height: 4px
}

.scrollbarGhostHairline-2LpzZ9::-webkit-scrollbar-thumb {
    background-color: rgba(24, 25, 28, .6);
    border-radius: 2px;
    cursor: move
}

.scrollbarGhostHairline-2LpzZ9::-webkit-scrollbar-track {
    background-color: transparent;
    border: none
}

.enable-forced-colors .scrollbar-3vVt8d::-webkit-scrollbar-track {
    background-color: Canvas;
    border-radius: 0;
    border-width: 1px
}

.enable-forced-colors .scrollbar-3vVt8d::-webkit-scrollbar-thumb {
    background-color: CanvasText;
    border-width: 1px
}

.wrapper-25sY58 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 1
}

.rightSplit-24Bqk0 {
    position: fixed;
    bottom: 0;
    right: 0;
    width: auto;
    -webkit-transition: opacity .4s ease;
    transition: opacity .4s ease;
    opacity: 1;
    pointer-events: none
}

.embedded-1f1UqW {
    position: absolute
}

.leftSplit-hm3715 {
    position: relative;
    width: 100vw;
    min-height: 100vh;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.nonEmbeddedLeftSplit-1DjcEq {
    max-width: 1480px
}

.mobileWave-I_bBuK {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vh;
    height: 100vh;
    z-index: -1;
    pointer-events: none
}

.logo-2VSUIa {
    position: relative;
    margin: 24px auto 0
}

@media (min-width:830px) {
    .logo-2VSUIa {
        position: fixed;
        top: 24px;
        left: 24px;
        margin: 0
    }
}

.focusLock-2tveLW {
    min-height: 0;
    max-width: 100%
}

.focusLock-2tveLW, .root-g14mjS {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.root-g14mjS {
    background-color: #fff;
    border-radius: 4px;
    margin: 0 auto;
    pointer-events: all;
    position: relative;
    max-height: 100%
}

.small-23Atuv {
    width: 440px;
    max-height: 720px;
    min-height: 200px
}

.medium-1ywRMv {
    width: 600px;
    max-height: 800px
}

.large-1XKv7D, .medium-1ywRMv {
    min-height: 400px
}

.large-1XKv7D {
    min-width: 800px;
    max-width: 960px
}

@media (max-width:485px) {
    .root-g14mjS {
        min-width: auto;
        width: 100%
    }

    .fullscreenOnMobile-ixj0e3 {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        border-radius: 0;
        max-height: none;
        overflow-y: auto;
        width: 100%
    }

    .fullscreenOnMobile-ixj0e3 .footer-31IekZ, .fullscreenOnMobile-ixj0e3 .header-1zd7se {
        border-radius: 0
    }
}

.theme-dark .root-g14mjS {
    background-color: #36393f;
    -webkit-box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 2px 10px 0 rgba(0, 0, 0, .2);
    box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 2px 10px 0 rgba(0, 0, 0, .2)
}

.theme-light .root-g14mjS {
    background: #fff;
    -webkit-box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 2px 10px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 2px 10px 0 rgba(0, 0, 0, .1)
}

.footer-31IekZ, .header-1zd7se {
    position: relative;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    padding: 16px;
    z-index: 1;
    overflow-x: hidden
}

.header-1zd7se {
    border-radius: 5px 5px 0 0;
    -webkit-transition: -webkit-box-shadow .1s ease-out;
    transition: -webkit-box-shadow .1s ease-out;
    transition: box-shadow .1s ease-out;
    transition: box-shadow .1s ease-out, -webkit-box-shadow .1s ease-out;
    word-wrap: break-word
}

.footer-31IekZ {
    border-radius: 0 0 5px 5px
}

.theme-dark .separator-2lLxgC {
    -webkit-box-shadow: 0 1px 0 0 rgba(24, 25, 28, .3), 0 1px 2px 0 rgba(24, 25, 28, .3);
    box-shadow: 0 1px 0 0 rgba(24, 25, 28, .3), 0 1px 2px 0 rgba(24, 25, 28, .3)
}

.theme-dark .footer-31IekZ {
    background-color: #2f3136
}

.theme-dark .footerSeparator-VzAYwb {
    -webkit-box-shadow: inset 0 1px 0 rgba(47, 49, 54, .6);
    box-shadow: inset 0 1px 0 rgba(47, 49, 54, .6)
}

.theme-light .separator-2lLxgC {
    -webkit-box-shadow: 0 1px 0 0 rgba(185, 187, 190, .3);
    box-shadow: 0 1px 0 0 rgba(185, 187, 190, .3)
}

.theme-light .footer-31IekZ {
    background-color: #f6f6f7
}

.theme-light .footerSeparator-VzAYwb {
    -webkit-box-shadow: inset 0 1px 0 rgba(246, 246, 247, .6);
    box-shadow: inset 0 1px 0 rgba(246, 246, 247, .6)
}

.content-2hZxGK {
    position: relative;
    z-index: 0;
    border-radius: 5px 5px 0 0;
    padding-left: 16px;
    padding-right: 16px;
    overflow-x: hidden
}

.close-1mLglB {
    height: 26px;
    padding: 4px;
    -webkit-transition: opacity .2s ease-in-out;
    transition: opacity .2s ease-in-out;
    opacity: .5;
    cursor: pointer;
    border-radius: 3px;
    color: var(--interactive-normal);
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

.close-1mLglB:hover {
    opacity: 1;
    color: var(--interactive-hover)
}

.closeWithCircleBackground-3JYDhN {
    width: 24px;
    height: 24px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, .85);
    border-radius: 50%;
    padding: 0
}

.closeWithCircleBackground-3JYDhN .closeIcon-11LhXr {
    width: 16px;
    height: 16px;
    color: var(--interactive-normal)
}

@media (max-width:485px) {
    .hideOnFullscreen-10Ndmf {
        display: none
    }
}

.spinnerContainer-3a92uf {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 100%;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 100%
}

.enable-forced-colors .root-g14mjS {
    border: 2px solid CanvasText
}

.selectable-2GODwY {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text
}

.strong-3yEWQO {
    font-weight: 600
}

.colorStandard-21JIj7 {
    color: var(--text-normal)
}

.colorLink-2apWfY {
    color: var(--text-link)
}

.colorMuted-20987_ {
    color: var(--text-muted)
}

.colorError-1H4THa {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.colorBrand-21Le_q {
    color: var(--text-brand)
}

.colorHeaderPrimary-jN_yGr {
    color: var(--header-primary)
}

.colorHeaderSecondary-g5teka {
    color: var(--header-secondary)
}

.colorStatusYellow-2uTn4I {
    color: var(--text-warning)
}

.colorStatusGreen-2HKYOW {
    color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.colorStatusRed-3FRrPF {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.colorInteractiveActive-30E9n8 {
    color: var(--interactive-active)
}

.colorInteractiveNormal-36ZhyZ {
    color: var(--interactive-normal)
}

.colorWhite-2T3PGe {
    color: #fff
}

.size10-2BeLhd {
    font-size: 10px;
    line-height: 12px
}

.size12-oc4dx4 {
    font-size: 12px;
    line-height: 16px
}

.size14-3fJ-ot {
    font-size: 14px;
    line-height: 18px
}

.size16-rrJ6ag {
    font-size: 16px;
    line-height: 20px
}

.size20-9iTTnl {
    font-size: 20px;
    line-height: 24px
}

.size24-17l95E {
    font-size: 24px;
    line-height: 30px
}

.size32-5yOQel {
    font-size: 32px;
    line-height: 40px
}

.title-3hptVQ {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    cursor: default;
    font-family: var(--font-display)
}

.h1-34Txb0 {
    font-size: 20px;
    line-height: 24px
}

.h1-34Txb0, .h2-1EaYVL {
    font-weight: 600
}

.h2-1EaYVL {
    font-size: 16px;
    line-height: 20px;
    text-transform: uppercase
}

.h3-2Gh4ka {
    line-height: 24px;
    font-weight: 500
}

.h3-2Gh4ka, .h4-1_f8j1 {
    font-size: 16px
}

.h4-1_f8j1 {
    line-height: 20px;
    letter-spacing: .3px
}

.h4-1_f8j1, .h5-2RwDNl {
    font-weight: 600;
    text-transform: uppercase
}

.h5-2RwDNl {
    font-size: 12px;
    line-height: 16px
}

.defaultMarginh4-3AyxzM {
    margin-top: 0;
    margin-bottom: 0
}

.defaultMarginh1-EURXsm, .defaultMarginh2-t7G-2y {
    margin-bottom: 20px
}

.defaultMarginh3-2aILfT, .defaultMarginh5-3Jxf6f {
    margin-bottom: 8px
}

.disabled-2DSdZK {
    opacity: .5;
    cursor: not-allowed
}

.required---V9PF {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    padding-left: 4px
}

.errorMessage-1kMqS5 {
    font-size: 12px;
    font-weight: 500;
    font-style: italic;
    text-transform: none
}

.errorSeparator-f__rwE {
    padding-left: 4px;
    padding-right: 4px
}

.defaultColor-2cKwKo {
    color: var(--header-primary)
}

.h5-2RwDNl {
    color: var(--header-secondary)
}

.faded-Yd0FtY {
    opacity: .8
}

.error-3EBD81 {
    color: var(--text-danger)
}

.backdrop-2ByYRN {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.backdrop-2ByYRN.withLayer-2VVmpp {
    pointer-events: all
}

.layerContainer-2v_Sit {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: none !important;
    pointer-events: none;
    z-index: 1002
}

.layer-2aCOJ3 {
    position: absolute;
    pointer-events: auto
}

.emptyError-25DlMg:empty:before {
    content: "RENDERING NULL FOR A POPOUT/MODAL/LAYER WILL BREAK THE APP";
    word-break: break-word;
    display: block;
    background-color: red;
    color: #fff;
    font-size: 32px;
    max-width: 500px;
    padding: 8px;
    font-weight: 700;
    border: 10px dashed hsl(139, calc(var(--saturation-factor, 1)*85.6%), 64.5%)
}

.layerHidden-CyRnFF {
    visibility: hidden
}

.disabledPointerEvents-2AmYRc {
    pointer-events: none
}

.backdrop-1BR_bn {
    background-color: #000
}

.backdrop-1BR_bn, .layer-1Ixpg3 {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0
}

.layer-1Ixpg3 {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    min-height: 0;
    padding-top: 40px;
    padding-bottom: 40px
}

.hidden-3PPAly * {
    pointer-events: none !important
}

.marginReset-28ZZyF {
    margin-top: 0;
    margin-bottom: 0
}

.marginTop4-2JFJJI {
    margin-top: 4px
}

.marginBottom4-1fdMNe {
    margin-bottom: 4px
}

.marginTop8-24uXGp {
    margin-top: 8px
}

.marginBottom8-emkd0_ {
    margin-bottom: 8px
}

.marginTop20-2T8ZJx {
    margin-top: 20px
}

.marginBottom20-315RVT {
    margin-bottom: 20px
}

.marginTop40-Q4o1tS {
    margin-top: 40px
}

.marginBottom40-fvAlAV {
    margin-bottom: 40px
}

.marginTop60-38vAjL {
    margin-top: 60px
}

.marginBottom60-2XQEx8 {
    margin-bottom: 60px
}

.marginCenterHorz-574Oxy {
    margin-left: auto;
    margin-right: auto
}

.marginLeft8-3meqg8 {
    margin-left: 8px
}

.container-KJ38dt {
    word-wrap: break-word
}

.form-2oOOG9 {
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.modalContent-3YXA7I {
    padding-top: 20px;
    padding-bottom: 20px
}

.content-Z3Xihu {
    height: 100%
}

.title-1itth0 {
    text-align: center
}

.minorContainer-1tix62 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    margin-top: 8px;
    margin-bottom: -10px;
    width: 100%;
    cursor: pointer
}

.minorAction-194-_3 {
    text-align: center;
    opacity: .6
}

.minorAction-194-_3:hover {
    text-decoration: underline
}

.body-_jWL8l {
    text-align: center
}

.primaryButton-361sHl, .secondaryButton-rBY2Kw {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 45%;
    flex: 1 1 45%
}

.gutter-3t0Erc {
    margin-left: 10px
}

.applicationDetails-3O3kc- {
    padding: 24px 0
}

.entry-2w47SK {
    margin-top: 8px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.entryIcon-2bcrKV {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    background-size: 16px 16px;
    background-position: 50%;
    background-repeat: no-repeat;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.entryIcon-2bcrKV, .entryInner-1tOOER {
    color: var(--text-muted)
}

.entryInner-1tOOER {
    font-weight: 500
}

.redirectWarning-2tpnAI .entryInner-1tOOER {
    color: var(--text-warning)
}

.wrapper-1HSdhi {
    margin: 0
}

.base-21yXnu {
    font-weight: 600;
    font-family: var(--font-display);
    color: var(--header-primary)
}

.selectable-2tk0kn {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text
}

.base-21yXnu.muted-eZM05q {
    color: var(--header-secondary)
}

.uppercase-2unHJn {
    text-transform: uppercase
}

.scopes-uCLYQv {
    border-bottom: 1px solid var(--background-modifier-accent);
    padding: 24px 0
}

.scope-J9cQRk {
    margin-top: 16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.scopeCheck-3trytX {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.scopeCheck-3trytX, .scopeTimes-KsAOBb {
    margin-right: 12px;
    width: 24px;
    height: 24px;
    padding: 2px;
    border-radius: 50%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.scopeTimes-KsAOBb {
    background-color: hsl(214, calc(var(--saturation-factor, 1)*9.9%), 50.4%)
}

.scopeInner-13uy_j {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.scopeName-1rTJwW {
    font-weight: 500
}

.sectionLabel-eOcIZ3 {
    margin-bottom: 16px;
    font-weight: 700;
    text-transform: uppercase;
    color: var(--header-secondary)
}

.icon-hDztL8 {
    width: 20px;
    height: 20px;
    color: #fff
}

.checkboxWrapper-2fDzaA {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    font-size: 16px;
    max-width: 100%
}

.checkboxWrapper-2fDzaA.row-31nALW {
    background-color: var(--background-secondary);
    padding: 10px;
    border-radius: 3px
}

.checkboxWrapper-2fDzaA.row-31nALW.checked-1pZh2h {
    background-color: var(--background-tertiary);
    color: var(--interactive-active)
}

.checkboxWrapper-2fDzaA.row-31nALW:hover:not(.checked-1pZh2h) {
    background-color: var(--background-modifier-hover);
    color: var(--interactive-hover)
}

.checkboxWrapper-2fDzaA.row-31nALW:active:not(.checked-1pZh2h) {
    background-color: var(--background-modifier-active);
    color: var(--interactive-active)
}

.checkboxWrapperDisabled-2lYviP {
    opacity: .6
}

.checkboxWrapperDisabled-2lYviP.row-31nALW {
    opacity: .3
}

.alignTop-1Vjj8q {
    -webkit-box-align: top;
    -ms-flex-align: top;
    align-items: top
}

.alignCenter-1l_YLh {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.input-3xr72x {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    margin: 0;
    padding: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.inputDefault-2F39XG, .inputDisabled-sYasHz {
    width: 100%;
    height: 100%;
    z-index: 1
}

.inputDefault-2F39XG {
    cursor: pointer
}

.inputDisabled-sYasHz {
    cursor: not-allowed
}

.inputReadonly-33V1FI {
    cursor: default;
    width: 0;
    height: 0;
    z-index: -1
}

.box-BHImcZ {
    border-radius: 3px
}

.round-1RSG-3 {
    border-radius: 50%
}

.checkbox-f1HnKB {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    border-width: 1px;
    border-style: solid;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.label-1xtMHH {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    min-width: 0
}

.labelClickable-eiZOrp {
    cursor: pointer
}

.labelDisabled-3boYd2 {
    cursor: not-allowed
}

.labelForward-2yqkcf {
    padding-left: 8px
}

.labelReversed-mhwuFY {
    padding-right: 8px
}

.theme-light .checkbox-f1HnKB {
    border-color: #b9bbbe
}

.theme-light.checked-1pZh2h {
    background: #fff
}

.theme-dark .checkbox-f1HnKB {
    border-color: #72767d
}

.theme-dark.checked-1pZh2h {
    border-color: #fff;
    background-color: #fff
}

.botPermissions-1j4evs {
    border-bottom: 1px solid var(--background-modifier-accent);
    padding: 16px 0 32px
}

.permissionsList-3ebykI {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.permission-1N-MQ3, .permissionsList-3ebykI {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.permission-1N-MQ3 {
    font-weight: 500;
    margin: 16px 0 0;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-flex: 2;
    -ms-flex: 2;
    flex: 2
}

.permissionsLabel-3HLgCy {
    font-weight: 500;
    margin-top: 8px
}

.disabledPermissionsLabel-e7hPos {
    font-weight: 500;
    margin-top: 24px
}

.disabledPermissionIcon-23JWfZ {
    margin-right: 8px;
    width: 18px;
    height: 18px;
    border-radius: 10%;
    background-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.icon-30ublm {
    width: 18px;
    height: 18px;
    color: #fff
}

.hiddenVisually-2ydA7k, .showOnFocus-2hCPKT:not(:focus-within) {
    clip: rect(0 0 0 0);
    -webkit-clip-path: inset(50%);
    clip-path: inset(50%);
    height: 1px;
    overflow: hidden;
    position: absolute;
    white-space: nowrap;
    width: 1px
}

.menu-1QACrS {
    position: relative;
    z-index: 1;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: auto;
    cursor: default;
    max-height: calc(100vh - 32px);
    -webkit-box-shadow: var(--elevation-high);
    box-shadow: var(--elevation-high);
    background: var(--background-floating);
    border-radius: 4px
}

.scroller-1bVxF5 {
    padding: 6px 8px
}

.styleFixed-2_NfVL {
    width: 220px
}

.styleFlexible-x0_sIC {
    min-width: 188px;
    max-width: 320px
}

.item-1OdjEX {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    margin: 2px 0;
    border-radius: 2px;
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    cursor: pointer
}

.item-1OdjEX.hideInteraction-2jPGL_ {
    cursor: default
}

.customItem-2CxBSo {
    color: var(--interactive-normal);
    border-color: var(--interactive-normal);
    font-size: 14px;
    font-weight: 500;
    line-height: 18px
}

.labelContainer-2vJzYL {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    min-height: 32px;
    padding: 6px 8px
}

.label-2gNW3x {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.subtext-2GlkbE {
    margin-top: 2px;
    white-space: normal
}

.iconContainer-1-SsTR {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    height: 18px;
    width: 18px;
    margin-left: 8px
}

.iconContainerLeft-2NHJgN {
    margin-left: 0;
    margin-right: 8px
}

.icon-E4cW1l {
    width: 18px;
    height: 18px
}

.hintContainer-2UwhLP, .imageContainer-1wSPgI {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    max-height: 18px;
    margin-left: 8px
}

.caret-2MOQD6, .image-21g2ca {
    height: 18px
}

.caret-2MOQD6 {
    width: 18px;
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg)
}

.disabled-oOAXat {
    cursor: pointer;
    pointer-events: none;
    opacity: .5
}

.separator-1So4YB {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 4px;
    border-bottom: 1px solid var(--background-modifier-accent)
}

.submenuContainer-3EVTeH {
    position: relative
}

.submenuPaddingContainer-_k62dJ {
    padding: 0 8px
}

.submenu-1apzyU {
    min-width: 188px;
    max-width: 320px
}

.colorDefault-CDqZdO {
    color: var(--interactive-normal);
    border-color: var(--interactive-normal)
}

.colorDefault-CDqZdO .subtext-2GlkbE {
    color: var(--text-muted);
    font-size: 12px;
    line-height: 16px
}

.colorDefault-CDqZdO .checkbox-hADx5o, .colorDefault-CDqZdO .radioSelection-1UHp0b {
    color: var(--control-brand-foreground)
}

.colorDefault-CDqZdO .check-3HZJs4 {
    color: #fff
}

.colorDefault-CDqZdO.focused-3qFvc8 {
    background-color: var(--brand-experiment-560);
    color: #fff
}

.colorDefault-CDqZdO.focused-3qFvc8 .checkbox-hADx5o, .colorDefault-CDqZdO.focused-3qFvc8 .radioSelection-1UHp0b, .colorDefault-CDqZdO.focused-3qFvc8 .subtext-2GlkbE {
    color: #fff
}

.colorDefault-CDqZdO.focused-3qFvc8 .check-3HZJs4 {
    color: var(--brand-experiment-560)
}

.colorDefault-CDqZdO:active:not(.hideInteraction-2jPGL_) {
    background-color: var(--brand-experiment-600);
    color: #fff
}

.colorBrand-3cPPsm {
    color: var(--brand-experiment-360)
}

.theme-light .colorBrand-3cPPsm:not(.focused-3qFvc8) {
    color: var(--brand-experiment-500)
}

.colorDanger-3n-KnP {}

.colorDanger-3n-KnP, .colorDanger-3n-KnP .checkbox-hADx5o, .colorDanger-3n-KnP .radioSelection-1UHp0b {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.colorDanger-3n-KnP.focused-3qFvc8 {
    background-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    color: #fff
}

.colorDanger-3n-KnP.focused-3qFvc8 .check-3HZJs4 {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.colorDanger-3n-KnP:active:not(.hideInteraction-2jPGL_) {
    background-color: hsl(359, calc(var(--saturation-factor, 1)*56.3%), 40.4%);
    color: #fff
}

.colorPremium-vwmYZQ {}

.colorPremium-vwmYZQ .icon-E4cW1l {
    color: hsl(302, calc(var(--saturation-factor, 1)*100%), 72.5%)
}

.colorPremium-vwmYZQ.focused-3qFvc8 .icon-E4cW1l, .colorPremium-vwmYZQ:active:not(.hideInteraction-2jPGL_) .icon-E4cW1l {
    color: #fff
}

.colorGreen-pAT2NS {}

.colorGreen-pAT2NS, .colorGreen-pAT2NS .checkbox-hADx5o, .colorGreen-pAT2NS .radioSelection-1UHp0b {
    color: hsl(139, calc(var(--saturation-factor, 1)*85.6%), 64.5%)
}

.colorGreen-pAT2NS.focused-3qFvc8 {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*85.6%), 64.5%);
    color: #fff
}

.colorGreen-pAT2NS.focused-3qFvc8 .check-3HZJs4 {
    color: hsl(139, calc(var(--saturation-factor, 1)*85.6%), 64.5%)
}

.colorGreen-pAT2NS:active:not(.hideInteraction-2jPGL_) {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    color: #fff
}

.groupLabel-16o1xl {
    text-transform: uppercase;
    font-family: var(--font-display);
    font-weight: 700;
    font-size: 12px;
    padding-top: 4px;
    padding-bottom: 0
}

.loader-2Pi-2t {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 16px
}

.enable-forced-colors .menu-1QACrS {
    background-color: ButtonFace;
    border: 2px solid CanvasText
}

.enable-forced-colors .colorDefault-CDqZdO {
    background-color: ButtonFace;
    border: 1px solid ButtonFace;
    color: ButtonText
}

.enable-forced-colors .colorDefault-CDqZdO .subtext-2GlkbE {
    color: inherit
}

.enable-forced-colors .colorDefault-CDqZdO.focused-3qFvc8, .enable-forced-colors .colorDefault-CDqZdO:hover {
    background-color: HighlightText;
    border-color: Highlight;
    color: Highlight
}

.enable-forced-colors .groupLabel-16o1xl, .enable-forced-colors .groupLabel-16o1xl:hover, .enable-forced-colors .hideInteraction-2jPGL_, .enable-forced-colors .hideInteraction-2jPGL_:hover {
    background-color: ButtonFace;
    border-color: ButtonFace;
    color: CanvasText
}

.enable-forced-colors .disabled-oOAXat {
    background-color: Canvas;
    color: GrayText;
    opacity: 1
}

.arrow-2HswgU {
    -webkit-transition: -webkit-transform .2s ease-out;
    transition: -webkit-transform .2s ease-out;
    transition: transform .2s ease-out;
    transition: transform .2s ease-out, -webkit-transform .2s ease-out
}

.arrow-2HswgU.open-3oj_jh {
    -webkit-transform: translate3d(0, -3px, 0) rotate(-180deg);
    transform: translate3d(0, -3px, 0) rotate(-180deg)
}

.listWrapper-321ZVQ {
    overflow-x: hidden;
    overflow-y: scroll;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

.listHeight-3LiElf {
    visibility: hidden
}

.listItems-6eZzQ1 {
    contain: layout;
    position: absolute
}

.scroller-2MALzE {
    height: 100%
}

.loadingPopout-1feYe_ {
    background-color: var(--background-secondary);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 8px
}

.full-motion .translate-PeW1wK.animatorTop-3ltI52 {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0)
}

.full-motion .translate-PeW1wK.animatorBottom-L63-7D {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0)
}

.full-motion .translate-PeW1wK.animatorLeft-3yvG13 {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0)
}

.full-motion .translate-PeW1wK.animatorRight-xAUgTY {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0)
}

.full-motion .translate-PeW1wK.animatorCenter-3Jet_R {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0)
}

.full-motion .translate-PeW1wK.didRender-2SiRlm {
    -webkit-transition: -webkit-transform .2s ease-out;
    transition: -webkit-transform .2s ease-out;
    transition: transform .2s ease-out;
    transition: transform .2s ease-out, -webkit-transform .2s ease-out;
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.full-motion .scale-3UGUBw {
    -webkit-transform: scale(.01);
    transform: scale(.01);
    opacity: 0
}

.full-motion .scale-3UGUBw.animatorTop-3ltI52 {
    -webkit-transform-origin: bottom center;
    transform-origin: bottom center
}

.full-motion .scale-3UGUBw.animatorBottom-L63-7D {
    -webkit-transform-origin: top center;
    transform-origin: top center
}

.full-motion .scale-3UGUBw.animatorLeft-3yvG13 {
    -webkit-transform-origin: top right;
    transform-origin: top right
}

.full-motion .scale-3UGUBw.animatorRight-xAUgTY {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-transform-origin: top center;
    transform-origin: top center
}

.full-motion .scale-3UGUBw.didRender-2SiRlm {
    -webkit-transition: opacity .12s ease-out, -webkit-transform .12s ease-out;
    transition: opacity .12s ease-out, -webkit-transform .12s ease-out;
    transition: transform .12s ease-out, opacity .12s ease-out;
    transition: transform .12s ease-out, opacity .12s ease-out, -webkit-transform .12s ease-out;
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1
}

.fade-1ICjW8 {
    opacity: 0
}

.fade-1ICjW8.didRender-2SiRlm {
    -webkit-transition: opacity .08s ease-out;
    transition: opacity .08s ease-out;
    opacity: 1
}

.loader-3kMIG8 {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: var(--background-tertiary);
    border-radius: 8px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 80px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.select-1Ia3hD {
    border: 1px solid transparent;
    padding: 8px 8px 8px 12px;
    cursor: pointer;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: 1fr auto;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 4px
}

.select-1Ia3hD, .wrapper-1b33hy {
    color: var(--text-normal);
    font-weight: 500
}

.searchable-22AIU7 {
    padding-top: 8px;
    padding-bottom: 8px
}

.searchInput-3pIoTy {
    cursor: pointer;
    background: inherit;
    color: inherit;
    border: none;
    font-size: 16px;
    padding: 0;
    width: 100%
}

.searchInput-3pIoTy.multi-1HXDiv {
    width: auto;
    height: 32px;
    margin-left: 4px
}

.editing-1dhWaY {
    font-weight: 400
}

.label-dyK79S {
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto;
    height: 16px;
    left: 12px
}

.open-1FRZsK {
    border-radius: 4px 4px 0 0;
    border-bottom-color: transparent
}

.selectPositionTop-1Xy1Sp.open-1FRZsK {
    border-radius: 0 0 4px 4px
}

.disabled-28GpyJ, .disabled-28GpyJ * {
    cursor: not-allowed;
    opacity: .6
}

.placeholder-2XdCpg, .value-26hJ01 {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden
}

.placeholder-2XdCpg {
    color: var(--text-muted)
}

.measurement-3UpG9x {
    position: absolute;
    visibility: hidden;
    pointer-events: none;
    border: 1px solid transparent;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.lookFilled-1GseHa.select-1Ia3hD {
    background-color: var(--background-secondary);
    border-color: var(--background-tertiary)
}

.popout-1KHNAq {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid var(--background-tertiary);
    background: var(--background-secondary);
    border-radius: 0 0 4px 4px
}

.popout-1KHNAq.popoutPositionTop-1GenHR {
    border-radius: 4px 4px 0 0
}

.lookFilled-1GseHa.popout-1KHNAq {
    border-radius: 0 0 3px 3px
}

.lookFilled-1GseHa.popout-1KHNAq.popoutPositionTop-1GenHR {
    border-radius: 3px 3px 0 0
}

.lookFilled-1GseHa.option-2eIyOn, .lookFilled-1GseHa.select-1Ia3hD:hover.option-2eIyOn {
    border-bottom-color: var(--brand-experiment);
    border-color: var(--brand-experiment) var(--brand-experiment) var(--background-tertiary)
}

.option-2eIyOn {
    padding: 12px;
    cursor: pointer;
    color: var(--interactive-normal);
    display: grid;
    grid-template-columns: 1fr auto;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-weight: 500;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.option-2eIyOn.focused-ODgjnC, .option-2eIyOn:focus, .option-2eIyOn:hover {
    background-color: var(--background-secondary-alt);
    color: var(--interactive-hover)
}

.option-2eIyOn[aria-selected=true]:not(.option-2eIyOn.multi-1HXDiv) {
    color: var(--interactive-active);
    background-color: var(--background-tertiary)
}

.selectedIcon-19TbzU {
    color: var(--brand-experiment)
}

.icons-2dXYop {
    gap: 4px
}

.icons-2dXYop, .value-26hJ01 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.value-26hJ01 {
    gap: 8px
}

.clear-1k4nWg {
    display: block;
    cursor: pointer
}

.noResults-1LND8K {
    padding: 12px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background: var(--background-secondary);
    width: 100%
}

.loading-k6z18Q, .noResults-1LND8K {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.loading-k6z18Q {
    height: 40px
}

.loadingSpinner-3beGq2 {
    background-color: var(--interactive-active)
}

.wrapper-1b33hy {
    display: grid;
    grid-template-columns: 1fr auto
}

.wrapper-1b33hy .select-1Ia3hD {
    grid-column: 1/span 2;
    grid-row: 1;
    padding: 10px 12px;
    padding-right: calc(24px + var(--icons-width))
}

.wrapper-1b33hy .select-1Ia3hD.multi-1HXDiv {
    padding-left: 4px;
    padding-bottom: 4px;
    padding-top: 0
}

.wrapper-1b33hy .icons-2dXYop {
    grid-column: 2;
    grid-row: 1;
    -webkit-margin-end: 8px;
    margin-inline-end: 8px;
    pointer-events: none
}

.icons-2dXYop.multi-1HXDiv {
    height: 42px
}

.clear-1k4nWg {
    pointer-events: all
}

.wrapper-1b33hy .value-26hJ01 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.wrapper-1b33hy .value-26hJ01.multi-1HXDiv {
    display: block;
    white-space: normal
}

.wrapper-1b33hy .searchInput-3pIoTy {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0px;
    flex: 1 0 0
}

.searchableSelect-3JyTyn .option-2eIyOn {
    display: grid;
    grid-template-areas: "prefix content suffix selectedIcon";
    grid-template-columns: auto 1fr auto auto;
    -ms-flex-line-pack: center;
    align-content: center
}

.searchableSelect-3JyTyn .prefix-2jUo7Z {
    grid-area: prefix;
    -webkit-margin-end: 8px;
    margin-inline-end: 8px
}

.searchableSelect-3JyTyn .suffix-jMtlz5 {
    grid-area: suffix;
    -webkit-margin-start: 8px;
    margin-inline-start: 8px;
    -webkit-margin-end: 8px;
    margin-inline-end: 8px
}

.searchableSelect-3JyTyn .content-2I4Lyb {
    grid-area: content;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.searchableSelect-3JyTyn .selectedIcon-19TbzU {
    grid-area: selectedIcon
}

.optionPillWrapper-2JgA7V {
    padding: 0;
    min-width: auto;
    width: auto
}

.optionPillContainer-3J3PI_ {
    display: inline
}

.optionPillItem-3sx4uB {
    margin-right: 4px
}

.optionPill-3_uSHD, .optionPillItem-3sx4uB {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.optionPill-3_uSHD {
    background: var(--background-tertiary);
    color: var(--interactive-active);
    padding: 6px 8px;
    border-radius: 2px;
    font-size: 16px;
    line-height: 20px
}

.optionPillBtn-3r5nCw {
    padding: 0;
    min-width: 0;
    height: auto;
    min-height: auto
}

.deleteOptionIcon-36xoR8 {
    color: var(--interactive-normal);
    margin-left: 8px
}

.optionPillItem-3sx4uB, .searchInput-3pIoTy.multi-1HXDiv {
    margin-top: 4px
}

.noScrollbar-3FRBw7 {
    scrollbar-width: 0
}

.noScrollbar-3FRBw7::-webkit-scrollbar {
    width: 0;
    height: 0
}

.multiSelectCheckbox-3Z4-q7 {
    grid-area: selectedIcon;
    border-radius: 4px;
    width: 20px;
    height: 20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.multiSelectCheckbox-3Z4-q7.unchecked-4G-ZV0 {
    border: 2px solid var(--interactive-normal)
}

.multiSelectCheckbox-3Z4-q7.checked-2jVMtE {
    border: 1px solid hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    background: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.multiSelectCheck-MtJ2Pz {
    color: #fff
}

.optionPill-3_uSHD {
    display: grid;
    grid-template-areas: "prefix content suffix clearIcon";
    grid-template-columns: auto 1fr auto auto;
    -ms-flex-line-pack: center;
    align-content: center
}

.optionPill-3_uSHD .prefix-2jUo7Z {
    grid-area: prefix;
    -webkit-margin-end: 8px;
    margin-inline-end: 8px
}

.optionPill-3_uSHD .suffix-jMtlz5 {
    grid-area: suffix;
    -webkit-margin-start: 8px;
    margin-inline-start: 8px;
    -webkit-margin-end: 8px;
    margin-inline-end: 8px
}

.optionPill-3_uSHD .content-2I4Lyb {
    grid-area: content
}

.optionPill-3_uSHD .deleteOptionIcon-36xoR8 {
    grid-area: clearIcon
}

.selectorGroup-3nTwNR {
    border-bottom: 1px solid var(--background-modifier-accent);
    padding: 16px 0 24px
}

.select-3fbXgn {
    margin-top: 8px;
    background-color: var(--background-secondary);
    border-radius: 3px
}

.label-3Vh14N {
    margin-top: 8px;
    font-weight: 500
}

.sectionLabel-3kYzE9 {
    font-weight: 700;
    text-transform: uppercase;
    color: var(--header-secondary)
}

.tooltip-14MtrL {
    position: relative;
    border-radius: 5px;
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    max-width: 190px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    word-wrap: break-word;
    z-index: 1002;
    will-change: opacity, transform
}

.tooltipDisablePointerEvents-1huO19, .tooltipPointer-3L49xb {
    pointer-events: none
}

.tooltipPointer-3L49xb {
    width: 0;
    height: 0;
    border: 5px solid transparent
}

.tooltipContent-Nejnvh {
    padding: 8px 12px;
    overflow: hidden
}

.tooltipContentAllowOverflow-1lJD1Q {
    overflow: visible
}

.tooltipTop-1wv5UJ {
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%
}

.tooltipTop-1wv5UJ .tooltipPointer-3L49xb {
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px
}

.tooltipBottom-2WzfVx, .tooltipCenter-2NmLEh {
    -webkit-transform-origin: 50% 0;
    transform-origin: 50% 0
}

.tooltipBottom-2WzfVx .tooltipPointer-3L49xb, .tooltipCenter-2NmLEh .tooltipPointer-3L49xb {
    position: absolute;
    bottom: 100%;
    left: 50%;
    margin-left: -5px;
    border-top-width: 5px;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.tooltipLeft-3H43DQ {
    -webkit-transform-origin: 100% 50%;
    transform-origin: 100% 50%
}

.tooltipLeft-3H43DQ .tooltipPointer-3L49xb {
    position: absolute;
    left: 100%;
    top: 50%;
    margin-top: -5px;
    border-right-width: 5px;
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg)
}

.tooltipRight-2TSb42 {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%
}

.tooltipRight-2TSb42 .tooltipPointer-3L49xb {
    position: absolute;
    right: 100%;
    top: 50%;
    margin-top: -5px;
    border-left-width: 5px;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
}

.theme-dark .tooltip-14MtrL, .theme-light .tooltip-14MtrL {
    -webkit-box-shadow: var(--elevation-high);
    box-shadow: var(--elevation-high);
    color: var(--text-normal)
}

.theme-dark .tooltipPrimary-3qLMbS, .theme-light .tooltipPrimary-3qLMbS {
    background-color: var(--background-floating)
}

.theme-dark .tooltipPrimary-3qLMbS .tooltipPointer-3L49xb, .theme-light .tooltipPrimary-3qLMbS .tooltipPointer-3L49xb {
    border-top-color: var(--background-floating)
}

.theme-dark .tooltipBlack-vMYxvw, .theme-light .tooltipBlack-vMYxvw {
    background-color: #000;
    color: #fff
}

.theme-dark .tooltipBlack-vMYxvw .tooltipPointer-3L49xb, .theme-light .tooltipBlack-vMYxvw .tooltipPointer-3L49xb {
    border-top-color: #000
}

.theme-dark .tooltipGrey-lpLZjh, .theme-light .tooltipGrey-lpLZjh {
    background-color: #202225;
    color: #fff
}

.theme-dark .tooltipGrey-lpLZjh .tooltipPointer-3L49xb, .theme-light .tooltipGrey-lpLZjh .tooltipPointer-3L49xb {
    border-top-color: #202225
}

.theme-dark .tooltipBrand-20XsMA, .theme-light .tooltipBrand-20XsMA {
    color: #fff;
    background-color: var(--brand-experiment)
}

.theme-dark .tooltipBrand-20XsMA .tooltipPointer-3L49xb, .theme-light .tooltipBrand-20XsMA .tooltipPointer-3L49xb {
    border-top-color: var(--brand-experiment)
}

.theme-dark .tooltipRed-2z14Wl, .theme-light .tooltipRed-2z14Wl {
    color: #fff;
    background-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.theme-dark .tooltipRed-2z14Wl .tooltipPointer-3L49xb, .theme-light .tooltipRed-2z14Wl .tooltipPointer-3L49xb {
    border-top-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.theme-dark .tooltipGreen-oouJdx, .theme-light .tooltipGreen-oouJdx {
    color: #fff;
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.theme-dark .tooltipGreen-oouJdx .tooltipPointer-3L49xb, .theme-light .tooltipGreen-oouJdx .tooltipPointer-3L49xb {
    border-top-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.theme-dark .tooltipYellow-3a3Cmr, .theme-light .tooltipYellow-3a3Cmr {
    color: var(--status-warning-text);
    background-color: var(--status-warning-background)
}

.theme-dark .tooltipYellow-3a3Cmr .tooltipPointer-3L49xb, .theme-light .tooltipYellow-3a3Cmr .tooltipPointer-3L49xb {
    border-top-color: var(--status-warning-background)
}

.enable-forced-colors .tooltip-14MtrL {
    border: 1px solid CanvasText
}

.enable-forced-colors .tooltipPointer-3L49xb {
    background-color: CanvasText;
    border: none;
    border-radius: 100%;
    height: 10px;
    width: 10px
}

.botTag-7aX5WZ {
    font-size: .625rem;
    text-transform: uppercase;
    vertical-align: top;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    text-indent: 0
}

.px-MnE_OR.botTag-7aX5WZ {
    height: 15px;
    padding: 0 4px;
    margin-top: 1px;
    border-radius: 3px
}

.rem-3kT9wc.botTag-7aX5WZ {
    height: .9375rem;
    padding: 0 .275rem;
    margin-top: .075em;
    border-radius: .1875rem
}

.rem-3kT9wc.botTag-7aX5WZ.botTagOP-3pUTXu {
    border-radius: .4rem
}

.botTagRegular-kpctgU {
    background: var(--brand-experiment);
    color: #fff
}

.botTagInvert-1nKcq_ {
    background: #fff;
    color: var(--brand-experiment)
}

.botTagVerified-2KCPMa {
    display: inline-block
}

.px-MnE_OR .botTagVerified-2KCPMa {
    width: 15px;
    height: 15px;
    margin-left: -4px
}

.rem-3kT9wc .botTagVerified-2KCPMa {
    width: .9375rem;
    height: .9375rem;
    margin-left: -.25rem
}

.botText-1fD6Qk {
    position: relative;
    font-weight: 500;
    vertical-align: top
}

.px-MnE_OR .botText-1fD6Qk {
    font-size: 10px;
    line-height: 15px
}

.rem-3kT9wc .botText-1fD6Qk {
    line-height: .9375rem
}

.botTagOP-3pUTXu {
    color: hsl(235, calc(var(--saturation-factor, 1)*51.4%), 52.4%);
    background-color: hsl(235, calc(var(--saturation-factor, 1)*86.2%), 88.6%);
    border-radius: 8px
}

.dots-1BwzZQ {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    pointer-events: none;
    color: #fff
}

.dots-1BwzZQ.themed-3T-OPr {
    color: var(--text-normal)
}

.mask-2Me5HY {
    display: block
}

.status-2DiCMd {
    width: 100%;
    height: 100%
}

.wrapper-1VLyxH {
    position: relative;
    border-radius: 50%
}

.pointer-5RhfHK {
    cursor: pointer
}

.mask-1FEkla {
    pointer-events: none;
    position: relative;
    display: block;
    height: 100%;
    width: auto
}

.status-1CWzUb, .svg-2azL_l {
    position: absolute
}

.status-1CWzUb {
    bottom: 0;
    right: 0;
    height: auto
}

.cursorDefault--wfhy5 {
    cursor: default
}

.avatarStack-3vfSFa {
    display: grid;
    width: 100%;
    height: 100%
}

.avatar-b5OQ1N, .avatarSpeaking-33RRJU {
    width: 100%;
    height: 100%;
    grid-area: 1/1
}

.avatar-b5OQ1N {
    display: block;
    -o-object-fit: cover;
    object-fit: cover;
    pointer-events: none
}

.avatar-b5OQ1N:before {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    background-color: var(--background-modifier-accent)
}

.avatarSpeaking-33RRJU {
    -webkit-box-shadow: inset 0 0 0 2px hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%), inset 0 0 0 3px var(--background-secondary);
    box-shadow: inset 0 0 0 2px hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%), inset 0 0 0 3px var(--background-secondary);
    border-radius: 50%
}

.pointerEvents-9SZWKj {
    pointer-events: auto
}

.header-3cN3a_ {
    padding: 32px 0 24px;
    border-radius: 8px 0;
    border-bottom: 1px solid var(--background-modifier-accent);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.applicationName-3zxKgu {
    font-weight: 700
}

.label-r9Wugd {
    margin: 8px 0
}

.currentUser-3wS6ED, .label-r9Wugd {
    font-weight: 500
}

.currentUserTag-3zOVLJ {
    display: inline
}

.currentUserDiscriminator-3BLi9D {
    display: inline;
    color: var(--interactive-normal)
}

.logoutLink-mirmUT {
    margin-left: 8px;
    color: var(--brand-experiment)
}

.headerIcons-ICsYI5 {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 24px
}

.ellipseGroup-109a7Q, .headerIcons-ICsYI5 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.ellipseGroup-109a7Q {
    margin: 0 24px
}

.ellipse-3ghlOA {
    width: 4px;
    height: 4px;
    margin: 0 2px;
    background-color: var(--header-primary);
    opacity: .1;
    border-radius: 50%
}

.botTag-UPkL8E {
    margin-top: 4px !important;
    margin-left: 8px
}

.image-3yFrLs {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    -webkit-transition: opacity .2s linear .2s;
    transition: opacity .2s linear .2s
}

.image-3yFrLs.loaded-208lqP {
    opacity: 1
}

.logo-2Rw1Ex {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 130px;
    height: 36px;
}

.canvas-2dBZRV {
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.canvas-2dBZRV, .fallbackImage-kwjEnv {
    position: fixed;
    top: 0;
    left: 0;
    pointer-events: none
}

.fallbackImage-kwjEnv {
    width: 100%;
    height: 85%;
    min-width: 780px;
    min-height: 780px;
    max-width: 1000px;
    max-height: 1000px;
    border: none;
    opacity: 0;
    -webkit-transition: opacity .4s ease;
    transition: opacity .4s ease
}

.visible-3f3yOU {
    opacity: 1
}

.embedded-2BXJus {
    position: absolute
}

.oauth2Wrapper-p_8bbd {
    min-width: 280px;
    max-width: 400px;
    min-height: 400px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    border-radius: 5px;
    -webkit-box-shadow: var(--elevation-high);
    box-shadow: var(--elevation-high);
    background-color: var(--background-floating)
}

.wrapper-3xpz8m {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0 16px
}

.header-1Qp_Ej {
    background-position: 50%;
    background-repeat: no-repeat;
    display: inline-block
}

.text-1yBazx {
    margin-top: 24px;
    text-align: center
}

.headerSuccess-3n55Ht {
    width: 158px;
    height: 130px;
}

.headerFailure-tSGJRv {
    width: 92px;
    height: 92px;
}

.closeButton-aYuW2o {
    position: absolute;
    right: 12px;
    top: 12px;
    opacity: .8
}

.footer-3Gu_Tl {
    padding: 16px;
    border-radius: 0 0 5px 5px;
    background-color: var(--background-secondary);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

@media (max-width:485px) {
    .footer-3Gu_Tl {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        padding: 0 16px 8px;
        background-color: transparent
    }

    .footer-3Gu_Tl button {
        margin-top: 8px
    }
}

.authorize-2xTTr- {
    width: 100%;
    height: 100%;
    background-color: var(--background-floating)
}

.authorize-2xTTr-, .captcha-1iq3Zi {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.captcha-1iq3Zi {
    padding-top: 28px;
    padding-bottom: 28px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.captchaLabel-3tgZDU {
    font-weight: 500;
    margin-bottom: 20px
}

.recaptcha-37Nudl {
    height: 80px
}

.content-3oGIwf {
    padding: 0 16px
}

.scrollContent-_1I-44 {
    padding: 0 8px 0 16px !important
}

.anchor-1MIwyf {
    color: var(--text-link);
    text-decoration: none
}

.low-saturation .anchor-1MIwyf {
    color: LinkText;
    text-decoration: underline;
    color: var(--text-link-low-saturation)
}

.anchorUnderlineOnHover-2qPutX:hover {
    text-decoration: underline
}

.enable-forced-colors .anchor-1MIwyf, .enable-forced-colors .anchorUnderlineOnHover-2qPutX {
    background-color: Canvas;
    color: LinkText;
    text-decoration: underline
}

.enable-forced-colors .anchor-1MIwyf svg, .enable-forced-colors .anchorUnderlineOnHover-2qPutX svg {
    color: currentColor
}

.wrapper-3AZUiP {
    overflow: hidden;
    min-height: 100vh;
    background-size: cover;
    color: var(--text-normal);
    background-color: var(--background-secondary);
    background-position: 50% 0
}

.flexWrapper-2VyHoE {
    height: 100vh
}

.image-35kDIs {
    width: 254px;
    height: 154px;
    margin-bottom: 40px;
    background-size: 100% 100%
}

.text-3IbNaT {
    width: 440px;
    margin-bottom: 20px
}

.note-Ph806N {
    font-size: 16px;
    color: var(--text-muted)
}

.note-Ph806N, .title-NjUt5y {
    text-align: center
}

.title-NjUt5y {
    font-size: 24px;
    line-height: 8px;
    margin-bottom: 24px;
    font-weight: 600;
    color: var(--header-primary)
}

.titleBar-1it3bQ {
    z-index: 3001;
    -webkit-app-region: drag;
    -ms-flex-negative: 0;
    flex-shrink: 0
}

.withFrame-2dL45i {
    height: 18px;
    margin-top: 4px
}

.typeMacOS-3V4xXE {
    position: absolute;
    width: 72px
}

.typeMacOSWithFrame-1XEpN7 {}

.typeMacOSWithFrame-1XEpN7 .macButtons-eIdy0e {
    margin-top: -2px;
    margin-right: -70px
}

.typeWindows-2-g3UY {}

.wordmark-2u86JB {
    pointer-events: none;
    font-size: 0
}

.wordmarkWindows-2dq6rw {
    position: absolute;
    top: 0;
    left: 0;
    padding: 4px 9px 3px;
    color: var(--text-muted)
}

.wordmarkMacOS-2DZWaT {
    margin: 0 auto
}

.winButton-3UMjdg {
    position: relative;
    top: -4px;
    cursor: pointer;
    width: 28px;
    height: 22px;
    pointer-events: auto;
    -webkit-app-region: no-drag
}

.winButtonMinMax-3RsPUg {}

.winButtonMinMax-3RsPUg:hover {
    background-color: var(--background-modifier-hover);
    color: var(--interactive-hover)
}

.winButtonMinMax-3RsPUg:active {
    background-color: var(--background-modifier-active);
    color: var(--interactive-active)
}

.winButtonClose-3Q8ZH5 {}

.winButtonClose-3Q8ZH5:hover {
    color: #fff;
    background-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.winButton-3UMjdg {
    color: var(--interactive-normal)
}

.macButtons-eIdy0e {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 70px;
    padding: 10px
}

.macButtons-eIdy0e svg {
    display: block;
    visibility: hidden
}

.focused-Xb9BI5 .macButtons-eIdy0e:hover svg {
    visibility: visible
}

.macButton-2M5W_9 {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    -webkit-app-region: no-drag
}

.macButtonClose-3IXwAm, .macButtonMaximize-1vTCwt, .macButtonMinimize-Q8clSI {}

.macButtonMaximize-1vTCwt {
    background-color: #34c749
}

.macButtonMaximize-1vTCwt:active {
    background-color: #2a9f3a
}

.macButtonMinimize-Q8clSI {
    background-color: #fdbc40
}

.macButtonMinimize-Q8clSI:active {
    background-color: #ca9633
}

.macButtonClose-3IXwAm {
    background-color: #fc615d
}

.macButtonClose-3IXwAm:active {
    background-color: #ca4e4a
}

.unfocused-1U-yOa .macButtonClose-3IXwAm, .unfocused-1U-yOa .macButtonMaximize-1vTCwt, .unfocused-1U-yOa .macButtonMinimize-Q8clSI {
    background-color: var(--background-modifier-accent)
}

.container-3ysAGy {
    position: absolute;
    margin: 8px;
    bottom: 0;
    right: 0;
    padding: 8px;
    white-space: pre;
    font-family: Consolas, Andale Mono WT, Andale Mono, Lucida Console, Lucida Sans Typewriter, DejaVu Sans Mono, Bitstream Vera Sans Mono, Liberation Mono, Nimbus Mono L, Monaco, Courier New, Courier, monospace;
    font-size: 10px;
    background-color: var(--background-secondary);
    -webkit-box-shadow: var(--elevation-stroke), var(--elevation-high);
    box-shadow: var(--elevation-stroke), var(--elevation-high);
    border-radius: 3px;
    pointer-events: all;
    z-index: 1001;
    min-height: 5em;
    width: 320px
}

.containerMinimized-3-Gngr {
    width: auto;
    min-height: auto
}

.content-2MSDVx {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    min-height: 5em
}

.contentMinimized-2D5YBQ {
    display: none
}

.notTracked-193AS6 {
    color: var(--text-muted)
}

.label-2AMd7f {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.baseIcon-sFdzG1 {
    width: 12px;
    height: 12px
}

.iconNotTracked-apg9zn {
    color: hsl(210, calc(var(--saturation-factor, 1)*9.8%), 80%)
}

.iconTracked-f2LnPm {
    color: hsl(139, calc(var(--saturation-factor, 1)*85.6%), 64.5%)
}

.minimizeButton-2_AuqA {
    position: absolute;
    top: 0;
    right: 0;
    padding: 8px;
    cursor: pointer
}

.jsonSection-3yB9ax {
    margin-left: 8px;
    margin-bottom: 8px;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text
}

.stack-l3M26I {
    color: var(--text-muted)
}

.current-WzpAFB, .stack-l3M26I {}

.current-WzpAFB {
    color: var(--text-normal)
}

[data-popout-root], html {
    --brand-experiment-100: hsl(240, calc(var(--saturation-factor, 1)*77.8%), 98.2%);
    --brand-experiment-130: hsl(236, calc(var(--saturation-factor, 1)*87.5%), 96.9%);
    --brand-experiment-160: hsl(235, calc(var(--saturation-factor, 1)*84.6%), 94.9%);
    --brand-experiment-200: hsl(236, calc(var(--saturation-factor, 1)*83.3%), 92.9%);
    --brand-experiment-230: hsl(236, calc(var(--saturation-factor, 1)*87%), 91%);
    --brand-experiment-260: hsl(235, calc(var(--saturation-factor, 1)*86.2%), 88.6%);
    --brand-experiment-300: hsl(235, calc(var(--saturation-factor, 1)*86.1%), 85.9%);
    --brand-experiment-330: hsl(235, calc(var(--saturation-factor, 1)*85.1%), 81.6%);
    --brand-experiment-360: hsl(235, calc(var(--saturation-factor, 1)*86.1%), 77.5%);
    --brand-experiment-400: hsl(235, calc(var(--saturation-factor, 1)*86.1%), 71.8%);
    --brand-experiment-430: hsl(235, calc(var(--saturation-factor, 1)*85.7%), 69.8%);
    --brand-experiment-460: hsl(235, calc(var(--saturation-factor, 1)*85.5%), 67.5%);
    --brand-experiment: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    --brand-experiment-500: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    --brand-experiment-530: hsl(235, calc(var(--saturation-factor, 1)*66.7%), 58.8%);
    --brand-experiment-560: hsl(235, calc(var(--saturation-factor, 1)*51.4%), 52.4%);
    --brand-experiment-600: hsl(235, calc(var(--saturation-factor, 1)*46.7%), 44.1%);
    --brand-experiment-630: hsl(235, calc(var(--saturation-factor, 1)*46.7%), 38.2%);
    --brand-experiment-660: hsl(235, calc(var(--saturation-factor, 1)*47.1%), 33.3%);
    --brand-experiment-700: hsl(235, calc(var(--saturation-factor, 1)*47%), 25.9%);
    --brand-experiment-730: hsl(235, calc(var(--saturation-factor, 1)*46.8%), 24.3%);
    --brand-experiment-760: hsl(234, calc(var(--saturation-factor, 1)*46.9%), 22.2%);
    --brand-experiment-800: hsl(235, calc(var(--saturation-factor, 1)*47.5%), 19.4%);
    --brand-experiment-830: hsl(235, calc(var(--saturation-factor, 1)*47.4%), 14.9%);
    --brand-experiment-860: hsl(235, calc(var(--saturation-factor, 1)*46.9%), 9.6%);
    --brand-experiment-900: hsl(233, calc(var(--saturation-factor, 1)*50%), 3.1%);
    --brand-experiment-05a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.05);
    --brand-experiment-10a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.1);
    --brand-experiment-15a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.15);
    --brand-experiment-20a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.2);
    --brand-experiment-25a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.25);
    --brand-experiment-30a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.3);
    --brand-experiment-35a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.35);
    --brand-experiment-40a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.4);
    --brand-experiment-45a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.45);
    --brand-experiment-50a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.5);
    --brand-experiment-55a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.55);
    --brand-experiment-60a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.6);
    --brand-experiment-65a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.65);
    --brand-experiment-70a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.7);
    --brand-experiment-75a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.75);
    --brand-experiment-80a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.8);
    --brand-experiment-85a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.85);
    --brand-experiment-90a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.9);
    --brand-experiment-95a: hsla(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%, 0.95)
}

[data-popout-root].low-saturation, html.low-saturation {
    --control-brand-foreground: var(--interactive-normal)
}

html.disable-forced-colors * {
    forced-color-adjust: none
}

.platform-osx .drag-2yz2Kd {
    -webkit-app-region: drag;
    position: absolute;
    width: 100%;
    padding-top: 32px
}

.root-2zfUH6 {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    contain: strict;
    pointer-events: all
}

.enter-1dUUmT, .enterReducedMotion-MMuKHV, .exit-2o9TuE, .exitReducedMotion-e6W1Hw {
    -webkit-transition-duration: .15s;
    transition-duration: .15s;
    -webkit-transition-property: opacity, -webkit-transform;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
    -webkit-transition-timing-function: ease-in-out;
    transition-timing-function: ease-in-out
}

.enter-1dUUmT {
    -webkit-transform: scale(1.1);
    transform: scale(1.1)
}

.enter-1dUUmT, .enterReducedMotion-MMuKHV {
    opacity: 0
}

.exit-2o9TuE {
    -webkit-transform: scale(1);
    transform: scale(1)
}

.exit-2o9TuE, .exitReducedMotion-e6W1Hw {
    opacity: 1
}

.enterActive-3VkCa2, .enterDone-menWZ8 {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1)
}

.enterActiveReducedMotion-3GenYC, .enterDoneReducedMotion-31m4p5 {
    opacity: 1
}

.exitActive-30x53Q, .exitDone-3ZOdrc {
    opacity: 0;
    -webkit-transform: scale(1.1);
    transform: scale(1.1)
}

.exitActiveReducedMotion-TnDyft, .exitDoneReducedMotion-2DPO6P {
    opacity: 0
}

.toast-q3Y4wI {
    background-color: var(--background-tertiary);
    border-radius: 32px;
    margin-bottom: 12px;
    line-height: 24px;
    padding: 16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.icon-2QUmmw {
    margin-right: 8px;
    margin-top: -2px
}

.container-Sxc1z3 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    top: 36px;
    z-index: 4999;
    position: fixed;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}

.errorPage-2pZ2Kq {
    width: 100%
}

.buttons-228G5H {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.clearOverrideButton-1QY45f {
    margin-left: 8px
}

.emoji {
    -o-object-fit: contain;
    object-fit: contain;
    width: 1.375em;
    height: 1.375em;
    vertical-align: bottom
}

.emoji.jumboable {
    width: 3rem;
    height: 3rem;
    min-height: 3rem
}

.loading-1yrGTe {
    position: absolute;
    width: 100%;
    height: 100vh;
    background: var(--background-secondary);
    color: var(--text-normal)
}

.downloadApps-14IgKV {
    border-radius: 5px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    min-width: 880px;
    min-height: 387px;
    padding: 32px;
    background-color: #fff !important;
    -webkit-box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 2px 10px 0 rgba(0, 0, 0, .1) !important;
    box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 2px 10px 0 rgba(0, 0, 0, .1) !important
}

.contentWrapper-1OoQ1E, .downloadApps-14IgKV {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.contentWrapper-1OoQ1E {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1
}

.inner-2b28EM {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin: 10px
}

.header-1lOVWt {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 32px;
    color: #060607
}

.footer-2TRYcZ, .header-1lOVWt {
    text-align: center
}

.footer-2TRYcZ {
    margin-top: 32px;
    color: #2e3338
}

.footer-2TRYcZ a {
    color: hsl(212, calc(var(--saturation-factor, 1)*100%), 43.9%);
    text-decoration: none;
    cursor: pointer
}

.footer-2TRYcZ a:hover {
    text-decoration: underline
}

.platformsWrap-2fJSMn {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.platformsWrap-2fJSMn:last-child {
    margin-top: 32px
}

.platform-1ZMvDu, .platforms-15zUsJ {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.platform-1ZMvDu {
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border: 2px solid #747f8d;
    padding: 32px 12px 12px;
    position: relative;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    border-radius: 8px;
    margin-left: 15px;
    height: 260px;
    width: 196px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.platform-1ZMvDu.active-1j5w_A {
    -webkit-transform: scale(1.025);
    transform: scale(1.025);
    border-color: var(--brand-experiment)
}

.platform-1ZMvDu.active-1j5w_A .icon-1DpQvw {
    opacity: 0
}

.platform-1ZMvDu.active-1j5w_A .icon-1DpQvw.active-1j5w_A {
    opacity: 1
}

.platform-1ZMvDu.active-1j5w_A p {
    color: var(--brand-experiment)
}

.platform-1ZMvDu.active-1j5w_A .downloadButton-2XskEc {
    background-color: var(--brand-experiment)
}

.platformName-2q088w {
    color: #060607;
    text-align: center
}

.iconWrap-smv3Q2 {
    position: relative;
    width: 100px;
    height: 100px
}

.icon-1DpQvw, .iconWrap-smv3Q2 {
    -ms-flex-item-align: center;
    align-self: center
}

.icon-1DpQvw {
    -webkit-transition: opacity .3s ease-in-out;
    transition: opacity .3s ease-in-out;
    background-repeat: no-repeat;
    background-size: contain;
    position: absolute;
    width: 100%;
    height: 100%
}

.icon-1DpQvw.active-1j5w_A {
    opacity: 0
}

.downloadButtons-3uVlYj {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.list-barGc3 .downloadButton-2XskEc {
    width: 65px
}

.list-barGc3 .downloadButton-2XskEc:last-child {
    margin-left: 12px
}

.downloadButton-2XskEc {
    -webkit-transition: background-color .3s ease-in-out;
    transition: background-color .3s ease-in-out;
    background-color: #747f8d;
    text-align: center;
    border-radius: 3px;
    font-weight: 500;
    font-size: 14px;
    padding: 12px 0;
    margin-top: 16px;
    color: #fff;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1
}

.modalCloseButton-3lATJk {
    color: #4f5660;
    position: absolute;
    top: 12px;
    right: 12px
}

.modalCloseButton-3lATJk:hover {
    color: #2e3338
}

.content-26qlhD {
    padding-bottom: 20px
}

.app-3xd6d0, .mobileApp-3_TCAV {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    z-index: auto
}

.mobileApp-3_TCAV {
    overflow: auto
}

.modal-2RrUKJ {
    border-radius: 5px;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    min-height: 0
}

.theme-dark .modal-2RrUKJ {
    background-color: #36393f;
    -webkit-box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 2px 10px 0 rgba(0, 0, 0, .2);
    box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 2px 10px 0 rgba(0, 0, 0, .2)
}

.theme-light .modal-2RrUKJ {
    background: #fff;
    -webkit-box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 2px 10px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 2px 10px 0 rgba(0, 0, 0, .1)
}

.sizeSmall-syGkV0 {
    width: 440px;
    max-height: 660px;
    min-height: 200px
}

.sizeMedium-2Q3COE {
    width: 600px;
    max-height: 800px;
    min-height: 400px
}

.sizeLarge-D4HafV {
    min-width: 800px;
    max-width: 960px;
    min-height: 400px
}

@media (max-width:485px) {
    .sizeLarge-D4HafV, .sizeMedium-2Q3COE, .sizeSmall-syGkV0 {
        min-width: auto;
        width: 96%;
        -ms-flex-item-align: center;
        align-self: center;
        max-height: 100vh
    }

    .fullscreenOnMobile-2W5b6w {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        border-radius: 0;
        overflow-y: auto
    }

    .fullscreenOnMobile-2W5b6w .footer-1Ip3Sd, .fullscreenOnMobile-2W5b6w .header-2w6VV8 {
        border-radius: 0
    }

    .hideOnFullscreen-2JU4c_ {
        display: none
    }
}

.app-2CXKsg {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: var(--background-tertiary);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.app-2CXKsg button {
    cursor: pointer
}

.layers-1YQhyW {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    overflow: hidden;
    min-width: 0;
    min-height: 0
}

a, abbr, acronym, address, applet, big, blockquote, body, caption, cite, code, dd, del, dfn, div, dl, dt, em, fieldset, form, h1, h2, h3, h4, h5, h6, html, iframe, img, ins, kbd, label, legend, li, object, ol, p, pre, q, s, samp, small, span, strike, strong, table, tbody, td, tfoot, th, thead, tr, tt, ul, var {
    margin: 0;
    padding: 0;
    border: 0;
    font-weight: inherit;
    font-style: inherit;
    font-family: inherit;
    font-size: 100%;
    vertical-align: baseline
}

a {
    color: var(--text-link);
    text-decoration: none;
    cursor: pointer
}

a img {
    border: none
}

body {
    line-height: 1;
    margin: 0;
    padding: 0;
    font-family: var(--font-primary);
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background: transparent
}

p {
    margin: 14px 0
}

ol, ul {
    list-style: none
}

::-webkit-input-placeholder, input, select, textarea {
    font-family: var(--font-primary);
    font-weight: 400
}

::-moz-placeholder, input, select, textarea {
    font-family: var(--font-primary);
    font-weight: 400
}

:-ms-input-placeholder, input, select, textarea {
    font-family: var(--font-primary);
    font-weight: 400
}

::placeholder, input, select, textarea {
    font-family: var(--font-primary);
    font-weight: 400
}

@media (-webkit-max-device-pixel-ratio:1) {
    .theme-light ::-webkit-input-placeholder, .theme-light input, .theme-light select, .theme-light textarea {
        font-weight: 500
    }

    .theme-light ::-moz-placeholder, .theme-light input, .theme-light select, .theme-light textarea {
        font-weight: 500
    }

    .theme-light :-ms-input-placeholder, .theme-light input, .theme-light select, .theme-light textarea {
        font-weight: 500
    }

    .theme-light ::placeholder, .theme-light input, .theme-light select, .theme-light textarea {
        font-weight: 500
    }
}

strong {
    font-weight: 600
}

button {
    font-family: var(--font-primary);
    font-weight: 500;
    border: 0;
    cursor: pointer
}

code {
    font-family: Consolas, Andale Mono WT, Andale Mono, Lucida Console, Lucida Sans Typewriter, DejaVu Sans Mono, Bitstream Vera Sans Mono, Liberation Mono, Nimbus Mono L, Monaco, Courier New, Courier, monospace;
    font-size: 14px;
    line-height: 16px
}

:root {
    --font-primary: Whitney, "Helvetica Neue", Helvetica, Arial, sans-serif;
    --font-display: Ginto, "Helvetica Neue", Helvetica, Arial, sans-serif;
    --font-code: Consolas, "Andale Mono WT", "Andale Mono", "Lucida Console", "Lucida Sans Typewriter", "DejaVu Sans Mono", "Bitstream Vera Sans Mono", "Liberation Mono", "Nimbus Mono L", Monaco, "Courier New", Courier, monospace;
    --font-headline: Ginto Nord, Ginto, "Helvetica Neue", Helvetica, Arial, sans-serif;
    --font-korean: Whitney, "Apple SD Gothic Neo", "NanumBarunGothic", "\B9D1\C740   \ACE0\B515", "Malgun Gothic", Gulim, 굴림, Dotum, 돋움, "Helvetica Neue", Helvetica, Arial, sans-serif;
    --font-japanese: Whitney, Hiragino Sans, "\30D2\30E9\30AE\30CE\89D2\30B4   ProN W3", "Hiragino Kaku Gothic ProN", "\30E1\30A4\30EA\30AA", Meiryo, Osaka, "MS PGothic", "Helvetica Neue", Helvetica, Arial, sans-serif;
    --font-chinese-simplified: Whitney, "Microsoft YaHei New", 微软雅黑, "Microsoft Yahei", "Microsoft JhengHei", 宋体, SimSun, "Helvetica Neue", Helvetica, Arial, sans-serif;
    --font-chinese-traditional: Whitney, "Microsoft JhengHei", 微軟正黑體, "Microsoft JhengHei UI", "Microsoft YaHei", 微軟雅黑, 宋体, SimSun, "Helvetica Neue", Helvetica, Arial, sans-serif
}

@font-face {
    font-family: Whitney;
    font-weight: 300;
    src: url(/6c6374bad0b0b6d204d8d6dc4a18d820.woff) format("woff")
}

@font-face {
    font-family: Whitney;
    font-weight: 400;
    src: url(/e8acd7d9bf6207f99350ca9f9e23b168.woff) format("woff")
}

.theme-dark {
    --header-primary: #fff;
    --header-secondary: #b9bbbe;
    --text-normal: #dcddde;
    --text-muted: #72767d;
    --text-link: hsl(197, calc(var(--saturation-factor, 1)*100%), 47.8%);
    --text-link-low-saturation: hsl(197, calc(var(--saturation-factor, 1)*100%), 52.9%);
    --text-positive: hsl(139, calc(var(--saturation-factor, 1)*66.8%), 58.6%);
    --text-warning: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    --text-danger: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    --text-brand: hsl(235, calc(var(--saturation-factor, 1)*86.1%), 77.5%);
    --interactive-normal: #b9bbbe;
    --interactive-hover: #dcddde;
    --interactive-active: #fff;
    --interactive-muted: #4f545c;
    --background-primary: #36393f;
    --background-secondary: #2f3136;
    --background-secondary-alt: #292b2f;
    --background-tertiary: #202225;
    --background-accent: #4f545c;
    --background-floating: #18191c;
    --background-nested-floating: #2f3136;
    --background-mobile-primary: #36393f;
    --background-mobile-secondary: #2f3136;
    --background-modifier-hover: rgba(79, 84, 92, 0.16);
    --background-modifier-active: rgba(79, 84, 92, 0.24);
    --background-modifier-selected: rgba(79, 84, 92, 0.32);
    --background-modifier-accent: hsla(0, 0%, 100%, 0.06);
    --info-positive-text: #fff;
    --info-warning-text: #fff;
    --info-danger-text: #fff;
    --info-help-background: hsla(197, calc(var(--saturation-factor, 1)*100%), 47.8%, 0.1);
    --info-help-foreground: hsl(197, calc(var(--saturation-factor, 1)*100%), 47.8%);
    --info-help-text: #fff;
    --status-warning-text: #000;
    --scrollbar-thin-thumb: #202225;
    --scrollbar-thin-track: transparent;
    --scrollbar-auto-thumb: #202225;
    --scrollbar-auto-track: hsl(210, calc(var(--saturation-factor, 1)*9.8%), 20%);
    --scrollbar-auto-scrollbar-color-thumb: #202225;
    --scrollbar-auto-scrollbar-color-track: #2f3136;
    --elevation-stroke: 0 0 0 1px rgba(4, 4, 5, 0.15);
    --elevation-low: 0 1px 0 rgba(4, 4, 5, 0.2), 0 1.5px 0 rgba(6, 6, 7, 0.05), 0 2px 0 rgba(4, 4, 5, 0.05);
    --elevation-medium: 0 4px 4px rgba(0, 0, 0, 0.16);
    --elevation-high: 0 8px 16px rgba(0, 0, 0, 0.24);
    --logo-primary: #fff;
    --control-brand-foreground: hsl(235, calc(var(--saturation-factor, 1)*86.1%), 77.5%);
    --control-brand-foreground-new: hsl(235, calc(var(--saturation-factor, 1)*86.1%), 77.5%);
    --background-mentioned: hsla(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%, 0.1);
    --background-mentioned-hover: hsla(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%, 0.08);
    --background-message-hover: rgba(4, 4, 5, 0.07);
    --background-message-automod: hsla(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%, 0.05);
    --background-message-automod-hover: hsla(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%, 0.1);
    --channels-default: #8e9297;
    --guild-header-text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
    --channeltextarea-background: #40444b;
    --activity-card-background: #202225;
    --textbox-markdown-syntax: #8e9297;
    --spoiler-revealed-background: #292b2f;
    --spoiler-hidden-background: #202225;
    --deprecated-card-bg: rgba(32, 34, 37, 0.6);
    --deprecated-card-editable-bg: rgba(32, 34, 37, 0.3);
    --deprecated-store-bg: #36393f;
    --deprecated-quickswitcher-input-background: #72767d;
    --deprecated-quickswitcher-input-placeholder: hsla(0, 0%, 100%, 0.3);
    --deprecated-text-input-bg: rgba(0, 0, 0, 0.1);
    --deprecated-text-input-border: rgba(0, 0, 0, 0.3);
    --deprecated-text-input-border-hover: #040405;
    --deprecated-text-input-border-disabled: #202225;
    --deprecated-text-input-prefix: #dcddde
}

.theme-dark, .theme-light {
    --info-positive-background: hsla(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%, 0.1);
    --info-positive-foreground: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    --info-warning-background: hsla(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%, 0.1);
    --info-warning-foreground: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    --info-danger-background: hsla(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%, 0.1);
    --info-danger-foreground: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    --status-positive-background: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    --status-positive-text: #fff;
    --status-warning-background: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    --status-danger-background: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    --status-danger-text: #fff;
    --focus-primary: hsl(197, calc(var(--saturation-factor, 1)*100%), 47.8%)
}

.theme-light {
    --header-primary: #060607;
    --header-secondary: #4f5660;
    --text-normal: #2e3338;
    --text-muted: #747f8d;
    --text-link: hsl(212, calc(var(--saturation-factor, 1)*100%), 43.9%);
    --text-link-low-saturation: hsl(212, calc(var(--saturation-factor, 1)*100%), 43.9%);
    --text-positive: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    --text-warning: hsl(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%);
    --text-danger: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%);
    --text-brand: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    --interactive-normal: #4f5660;
    --interactive-hover: #2e3338;
    --interactive-active: #060607;
    --interactive-muted: #c7ccd1;
    --background-primary: #fff;
    --background-secondary: #f2f3f5;
    --background-secondary-alt: #ebedef;
    --background-tertiary: #e3e5e8;
    --background-accent: #747f8d;
    --background-floating: #fff;
    --background-nested-floating: #fff;
    --background-mobile-primary: #f8f9f9;
    --background-mobile-secondary: #fff;
    --background-modifier-hover: rgba(116, 127, 141, 0.08);
    --background-modifier-active: rgba(116, 127, 141, 0.16);
    --background-modifier-selected: rgba(116, 127, 141, 0.24);
    --background-modifier-accent: rgba(6, 6, 7, 0.08);
    --info-positive-text: #000;
    --info-warning-text: #000;
    --info-danger-text: #000;
    --info-help-background: hsla(212, calc(var(--saturation-factor, 1)*100%), 43.9%, 0.1);
    --info-help-foreground: hsl(212, calc(var(--saturation-factor, 1)*100%), 43.9%);
    --info-help-text: #000;
    --status-warning-text: #fff;
    --scrollbar-thin-thumb: rgba(79, 84, 92, 0.3);
    --scrollbar-thin-track: transparent;
    --scrollbar-auto-thumb: #ccc;
    --scrollbar-auto-track: #f2f2f2;
    --scrollbar-auto-scrollbar-color-thumb: #e3e5e8;
    --scrollbar-auto-scrollbar-color-track: #f2f3f5;
    --elevation-stroke: 0 0 0 1px rgba(6, 6, 7, 0.08);
    --elevation-low: 0 1px 0 rgba(6, 6, 7, 0.1), 0 1.5px 0 rgba(6, 6, 7, 0.025), 0 2px 0 rgba(6, 6, 7, 0.025);
    --elevation-medium: 0 4px 4px rgba(0, 0, 0, 0.08);
    --elevation-high: 0 8px 16px rgba(0, 0, 0, 0.16);
    --logo-primary: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    --control-brand-foreground: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    --control-brand-foreground-new: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%);
    --background-mentioned: hsla(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%, 0.1);
    --background-mentioned-hover: hsla(38, calc(var(--saturation-factor, 1)*95.7%), 54.1%, 0.2);
    --background-message-hover: rgba(6, 6, 7, 0.02);
    --background-message-automod: hsla(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%, 0.05);
    --background-message-automod-hover: hsla(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%, 0.1);
    --channels-default: #6a7480;
    --guild-header-text-shadow: 0 1px 1px hsla(0, 0%, 100%, 0.4);
    --channeltextarea-background: #ebedef;
    --activity-card-background: #fff;
    --textbox-markdown-syntax: #6a7480;
    --spoiler-revealed-background: #ebedef;
    --spoiler-hidden-background: #e3e5e8;
    --deprecated-card-bg: #f8f9f9;
    --deprecated-card-editable-bg: rgba(246, 246, 247, 0.6);
    --deprecated-store-bg: #f8f9f9;
    --deprecated-quickswitcher-input-background: #fff;
    --deprecated-quickswitcher-input-placeholder: rgba(79, 84, 92, 0.3);
    --deprecated-text-input-bg: rgba(79, 84, 92, 0.02);
    --deprecated-text-input-border: rgba(79, 84, 92, 0.3);
    --deprecated-text-input-border-hover: #b9bbbe;
    --deprecated-text-input-border-disabled: #dcddde;
    --deprecated-text-input-prefix: #b9bbbe
}

.appMount-2yBXZl, body, html {
    height: 100%;
    width: 100%
}

.appMount-2yBXZl {
    position: absolute;
    overflow: hidden;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.appMount-2yBXZl, body {
    background-color: var(--background-tertiary);
    text-rendering: optimizeLegibility
}

.overlay .appMount-2yBXZl, .overlay body {
    background: transparent
}

::-webkit-input-placeholder, body, button, input, select, textarea {
    font-family: var(--font-primary);
    text-rendering: optimizeLegibility
}

::-moz-placeholder, body, button, input, select, textarea {
    font-family: var(--font-primary);
    text-rendering: optimizeLegibility
}

:-ms-input-placeholder, body, button, input, select, textarea {
    font-family: var(--font-primary);
    text-rendering: optimizeLegibility
}

::placeholder, body, button, input, select, textarea {
    font-family: var(--font-primary);
    text-rendering: optimizeLegibility
}

html:lang(ja) ::-webkit-input-placeholder, html:lang(ja) body, html:lang(ja) button, html:lang(ja) input, html:lang(ja) select, html:lang(ja) textarea, html:lang(ja-JP) ::-webkit-input-placeholder, html:lang(ja-JP) body, html:lang(ja-JP) button, html:lang(ja-JP) input, html:lang(ja-JP) select, html:lang(ja-JP) textarea {
    font-family: var(--font-japanese)
}

html:lang(ja) ::-moz-placeholder, html:lang(ja) body, html:lang(ja) button, html:lang(ja) input, html:lang(ja) select, html:lang(ja) textarea, html:lang(ja-JP) ::-moz-placeholder, html:lang(ja-JP) body, html:lang(ja-JP) button, html:lang(ja-JP) input, html:lang(ja-JP) select, html:lang(ja-JP) textarea {
    font-family: var(--font-japanese)
}

html:lang(ja) :-ms-input-placeholder, html:lang(ja) body, html:lang(ja) button, html:lang(ja) input, html:lang(ja) select, html:lang(ja) textarea, html:lang(ja-JP) :-ms-input-placeholder, html:lang(ja-JP) body, html:lang(ja-JP) button, html:lang(ja-JP) input, html:lang(ja-JP) select, html:lang(ja-JP) textarea {
    font-family: var(--font-japanese)
}

html:lang(ja) ::placeholder, html:lang(ja) body, html:lang(ja) button, html:lang(ja) input, html:lang(ja) select, html:lang(ja) textarea, html:lang(ja-JP) ::placeholder, html:lang(ja-JP) body, html:lang(ja-JP) button, html:lang(ja-JP) input, html:lang(ja-JP) select, html:lang(ja-JP) textarea {
    font-family: var(--font-japanese)
}

html:lang(ko) ::-webkit-input-placeholder, html:lang(ko) body, html:lang(ko) button, html:lang(ko) input, html:lang(ko) select, html:lang(ko) textarea {
    font-family: var(--font-korean)
}

html:lang(ko) ::-moz-placeholder, html:lang(ko) body, html:lang(ko) button, html:lang(ko) input, html:lang(ko) select, html:lang(ko) textarea {
    font-family: var(--font-korean)
}

html:lang(ko) :-ms-input-placeholder, html:lang(ko) body, html:lang(ko) button, html:lang(ko) input, html:lang(ko) select, html:lang(ko) textarea {
    font-family: var(--font-korean)
}

html:lang(ko) ::placeholder, html:lang(ko) body, html:lang(ko) button, html:lang(ko) input, html:lang(ko) select, html:lang(ko) textarea {
    font-family: var(--font-korean)
}

html:lang(zh-CN) ::-webkit-input-placeholder, html:lang(zh-CN) body, html:lang(zh-CN) button, html:lang(zh-CN) input, html:lang(zh-CN) select, html:lang(zh-CN) textarea {
    font-family: var(--font-chinese-simplified)
}

html:lang(zh-CN) ::-moz-placeholder, html:lang(zh-CN) body, html:lang(zh-CN) button, html:lang(zh-CN) input, html:lang(zh-CN) select, html:lang(zh-CN) textarea {
    font-family: var(--font-chinese-simplified)
}

html:lang(zh-CN) :-ms-input-placeholder, html:lang(zh-CN) body, html:lang(zh-CN) button, html:lang(zh-CN) input, html:lang(zh-CN) select, html:lang(zh-CN) textarea {
    font-family: var(--font-chinese-simplified)
}

html:lang(zh-CN) ::placeholder, html:lang(zh-CN) body, html:lang(zh-CN) button, html:lang(zh-CN) input, html:lang(zh-CN) select, html:lang(zh-CN) textarea {
    font-family: var(--font-chinese-simplified)
}

html:lang(zh-HK) ::-webkit-input-placeholder, html:lang(zh-HK) body, html:lang(zh-HK) button, html:lang(zh-HK) input, html:lang(zh-HK) select, html:lang(zh-HK) textarea, html:lang(zh-TW) ::-webkit-input-placeholder, html:lang(zh-TW) body, html:lang(zh-TW) button, html:lang(zh-TW) input, html:lang(zh-TW) select, html:lang(zh-TW) textarea {
    font-family: var(--font-chinese-traditional)
}

html:lang(zh-HK) ::-moz-placeholder, html:lang(zh-HK) body, html:lang(zh-HK) button, html:lang(zh-HK) input, html:lang(zh-HK) select, html:lang(zh-HK) textarea, html:lang(zh-TW) ::-moz-placeholder, html:lang(zh-TW) body, html:lang(zh-TW) button, html:lang(zh-TW) input, html:lang(zh-TW) select, html:lang(zh-TW) textarea {
    font-family: var(--font-chinese-traditional)
}

html:lang(zh-HK) :-ms-input-placeholder, html:lang(zh-HK) body, html:lang(zh-HK) button, html:lang(zh-HK) input, html:lang(zh-HK) select, html:lang(zh-HK) textarea, html:lang(zh-TW) :-ms-input-placeholder, html:lang(zh-TW) body, html:lang(zh-TW) button, html:lang(zh-TW) input, html:lang(zh-TW) select, html:lang(zh-TW) textarea {
    font-family: var(--font-chinese-traditional)
}

html:lang(zh-HK) ::placeholder, html:lang(zh-HK) body, html:lang(zh-HK) button, html:lang(zh-HK) input, html:lang(zh-HK) select, html:lang(zh-HK) textarea, html:lang(zh-TW) ::placeholder, html:lang(zh-TW) body, html:lang(zh-TW) button, html:lang(zh-TW) input, html:lang(zh-TW) select, html:lang(zh-TW) textarea {
    font-family: var(--font-chinese-traditional)
}

a, button, div, input, label, select, span, strong, textarea {
    outline: 0
}

img[alt] {
    text-indent: -9999px
}

.wrapper-1f5byN {
    width: 100%;
    height: 100%
}

@media (min-width:486px) {
    .wrapper-1f5byN {
        position: absolute;
        top: 0;
        left: 0;
        min-height: 580px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center
    }
}

.platform-osx .wrapper-1f5byN:before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 32px;
    z-index: 1;
    -webkit-app-region: drag
}

.platform-osx .splashBackground-6P4u0V:before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 32px;
    z-index: 1;
    -webkit-app-region: drag
}

.platform-win .splashBackground-6P4u0V:before {
    content: "";
    position: fixed;
    top: 0;
    right: 0;
    width: 84px;
    height: 22px;
    background: rgba(0, 0, 0, .3);
    border-bottom-left-radius: 3px;
    z-index: 1;
    -webkit-transition: background .4s ease .4s;
    transition: background .4s ease .4s
}

.platform-win .theme-dark .splashBackground-6P4u0V.loggingIn-JqfdbB:before, .platform-win .theme-light .splashBackground-6P4u0V.loggingIn-JqfdbB:before {
    background: transparent
}

.characterBackground-1BPOOJ {
    position: relative;
    width: 100vw;
    min-height: 100vh;
    overflow: auto
}

.characterBackground-1BPOOJ .artwork-L5TAwQ {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.characterBackground-1BPOOJ .logo-3ac3fP {
    display: block
}

.characterBackground-1BPOOJ .logo-3ac3fP, .splashBackground-6P4u0V .logo-3ac3fP {
    position: fixed;
    top: 24px;
    left: 50%;
    margin-left: -65px;
    margin-top: 0
}

@media (min-width:830px) {
    .characterBackground-1BPOOJ .logo-3ac3fP {
        top: 53px;
        left: 48px;
        margin: 0
    }

    .splashBackground-6P4u0V .logo-3ac3fP {
        position: fixed;
        top: 24px;
        left: 24px;
        margin: 0
    }

    .platform-osx .splashBackground-6P4u0V .logo-3ac3fP {
        top: 40px
    }
}

.spacer-2uvat4 {
    width: 8px
}

.input-3NIgDw {
    width: 44px;
    height: 44px;
    border-radius: 5px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: none;
    font-size: 17px;
    text-align: center;
    margin-left: 4px;
    margin-right: 4px
}

.theme-light .input-3NIgDw {
    background-color: hsl(216, calc(var(--saturation-factor, 1)*9.8%), 90%);
    color: #36393f
}

.theme-dark .input-3NIgDw {
    background-color: #4f545c;
    color: #f6f6f7
}

.flowerStarContainer-1QeD-L {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative
}

.flowerStar-2tNFCR {
    width: 100%;
    height: 100%
}

.childContainer-U_a6Yh {
    position: absolute;
    top: -.05px;
    left: .05px;
    right: 0;
    bottom: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    pointer-events: none
}

.background-3Da2vZ {
    width: 16px;
    height: 16px
}

.hubContainer-14Q3_5 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative
}

.verified-1Jv_7P {
    color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.partnered-23cXFN {
    color: var(--brand-experiment)
}

.hub-3NNcVs {}

.hub-3NNcVs>.icon-3BYlXK>circle {
    fill: var(--background-accent)
}

.verifiedHub-1kSLgi {}

.verifiedHub-1kSLgi>.icon-3BYlXK>circle {
    fill: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.icon-3BYlXK {
    width: 100%;
    height: 100%;
    color: #fff
}

.icon-1zKOXL {
    position: relative;
    background-clip: padding-box;
    background-color: none;
    background-position: 50%;
    background-size: 100% 100%
}

.icon-1zKOXL .guildIconBadge-15YDb5 {
    position: absolute;
    right: -2px;
    bottom: -2px;
    width: 14px;
    height: 14px
}

.iconInactive-26M06U {
    border-radius: 50%
}

.iconActiveMini-1h3oNi {
    border-radius: 6px
}

.iconActiveSmaller-3O8Pe6 {
    border-radius: 7px
}

.iconActiveSmall-1cWtmG {
    border-radius: 9px
}

.iconActiveMedium-27_R5B {
    border-radius: 12px
}

.iconActiveLarge-ac-EDu {
    border-radius: 15px
}

.iconActiveLarger-2EZc1b {
    border-radius: 16px
}

.iconActiveXLarge-ehIcza {
    border-radius: 30px
}

.iconSizeSmol-qsFaRK {
    width: 16px;
    height: 16px
}

.iconSizeMini-2IDpY9 {
    width: 20px;
    height: 20px
}

.iconSizeSmaller-3mXaSo {
    width: 24px;
    height: 24px
}

.iconSizeSmall-1kccTk {
    width: 30px;
    height: 30px
}

.iconSizeMedium-2vzSua {
    width: 40px;
    height: 40px
}

.iconSizeLarge-_r2zCK {
    width: 50px;
    height: 50px
}

.iconSizeLarger-31WoF9 {
    width: 64px;
    height: 64px
}

.iconSizeXLarge-3PVE6K {
    width: 100px;
    height: 100px
}

.acronym-vuwTO7 {
    overflow: hidden;
    white-space: nowrap;
    width: 100%
}

.noIcon-3gSX9V {
    background-color: var(--background-secondary);
    color: var(--text-normal);
    text-align: center
}

.noIcon-3gSX9V.iconSizeSmol-qsFaRK {
    line-height: 16px
}

.noIcon-3gSX9V.iconSizeMini-2IDpY9 {
    line-height: 20px
}

.noIcon-3gSX9V.iconSizeSmaller-3mXaSo {
    line-height: 24px
}

.noIcon-3gSX9V.iconSizeSmall-1kccTk {
    line-height: 30px
}

.noIcon-3gSX9V.iconSizeMedium-2vzSua {
    line-height: 40px
}

.noIcon-3gSX9V.iconSizeLarge-_r2zCK {
    line-height: 50px
}

.noIcon-3gSX9V.iconSizeLarger-31WoF9 {
    line-height: 64px
}

.noIcon-3gSX9V.iconSizeXLarge-3PVE6K {
    line-height: 100px
}

.noAcronym-31Ky-U {
    background-color: var(--background-modifier-accent)
}

.inputWrapper-1YNMmM {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.input-2g-os5 {
    font-size: 16px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    border-radius: 3px;
    color: var(--text-normal);
    background-color: var(--deprecated-text-input-bg);
    border: 1px solid var(--deprecated-text-input-border);
    -webkit-transition: border-color .2s ease-in-out;
    transition: border-color .2s ease-in-out
}

.input-2g-os5::-webkit-input-placeholder {
    -webkit-user-select: none;
    user-select: none;
    color: var(--text-muted)
}

.input-2g-os5::-moz-placeholder {
    -moz-user-select: none;
    user-select: none;
    color: var(--text-muted)
}

.input-2g-os5:-ms-input-placeholder {
    -ms-user-select: none;
    user-select: none;
    color: var(--text-muted)
}

.input-2g-os5::placeholder {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: var(--text-muted)
}

.input-2g-os5:hover {
    border-color: var(--deprecated-text-input-border-hover)
}

.input-2g-os5.focused-1AtTHC, .input-2g-os5:focus {
    border-color: var(--text-link)
}

.input-2g-os5.error-8r7mjf {
    border-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.input-2g-os5.success-3HR983 {
    border-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.input-2g-os5.disabled-1MTS26 {
    border-color: var(--deprecated-text-input-border-disabled)
}

.input-2g-os5.editable-A51xZS {
    background-color: transparent;
    border-color: transparent
}

.inputDefault-3FGxgL {
    padding: 10px;
    height: 40px
}

.inputMini-Un2tP4 {
    line-height: 16px;
    padding: 4px 7px;
    height: 26px
}

.disabled-1MTS26 {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: not-allowed;
    opacity: .5
}

.focused-1AtTHC {
    border-color: var(--brand-experiment);
    opacity: 1
}

.inputPrefix-1VU7MB {
    font-size: 16px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: absolute;
    height: 40px;
    padding-top: 10px;
    padding-left: 20px;
    line-height: normal;
    color: var(--text-muted)
}

.theme-dark .inputPrefix-1VU7MB {
    opacity: .5
}

.formText-2ngGjI a:hover {
    text-decoration: underline
}

.formText-2ngGjI strong {
    font-weight: 600
}

.default-_FzbTF, .labelDescriptor-34wZ-z, .labelSelected-3GXNHE, .placeholder-FFXpF4 {}

.labelSelected-3GXNHE {
    font-weight: 400
}

@media (-webkit-max-device-pixel-ratio:1) {
    .theme-light .labelSelected-3GXNHE {
        font-weight: 500
    }
}

.labelBold-199Hd8 {
    font-weight: 600
}

.description-30xx7u {
    font-size: 14px;
    line-height: 20px;
    font-weight: 400
}

@media (-webkit-max-device-pixel-ratio:1) {
    .theme-light .description-30xx7u {
        font-weight: 500
    }
}

.modeDefault-2fEh7a {
    cursor: default
}

.modeSelectable-3UdpPO {
    cursor: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text
}

.modeDisabled-1RyezJ {
    cursor: not-allowed;
    opacity: .3
}

.description-30xx7u, .labelDescriptor-34wZ-z {
    color: var(--header-secondary)
}

.placeholder-FFXpF4 {
    color: var(--text-muted)
}

.error-30DGjS {
    color: var(--text-danger)
}

.error-30DGjS, .success-1YJZW- {}

.success-1YJZW- {
    color: var(--text-positive)
}

.authBox-1HR6Ha {
    width: 480px;
    padding: 32px;
    font-size: 18px;
    -webkit-box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .2);
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .2);
    border-radius: 5px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.authBox-1HR6Ha a {
    color: var(--text-link)
}

.authBox-1HR6Ha a:hover {
    text-decoration: underline
}

.authBoxExpanded-AN2aH1 {
    width: 784px
}

.centeringWrapper-dGnJPQ {
    width: 100%;
    text-align: center
}

.avatar-1qRAzQ {
    margin-bottom: 24px
}

@media (max-width:485px) {
    .authBox-1HR6Ha {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        padding: 70px 16px 40px;
        width: 100%;
        height: 100vh;
        min-height: 500px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        background: -webkit-gradient(linear, right top, left bottom, from(#3d4046), to(#1e1e23));
        background: linear-gradient(to left bottom, #3d4046, #1e1e23);
        border-radius: 0;
        overflow: scroll
    }

    .authBox-1HR6Ha:before {
        content: "";
        position: absolute;
        top: 20px;
        height: 36px;
        width: 112px;
        background: url(/assets/22fd790491653d837422d80e3500cf92.svg) no-repeat
    }

    @media (max-width:830px) {
        .authBox-1HR6Ha.authBoxExpanded-AN2aH1 {
            max-width: unset
        }
    }

    .authBox-1HR6Ha .centeringWrapper-dGnJPQ {
        position: relative;
        min-height: 540px
    }
}

@media (max-width:830px) {
    .authBoxExpanded-AN2aH1 {
        max-width: 480px
    }
}

.is-mobile .authBox-1HR6Ha {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 70px 16px 20px;
    width: 100%;
    height: 100%;
    min-height: 500px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background: -webkit-gradient(linear, right top, left bottom, from(#3d4046), to(#1e1e23));
    background: linear-gradient(to left bottom, #3d4046, #1e1e23);
    border-radius: 0;
    overflow: scroll
}

.is-mobile .authBox-1HR6Ha:before {
    content: "";
    position: absolute;
    top: 20px;
    height: 36px;
    width: 112px;
    background: url(/assets/22fd790491653d837422d80e3500cf92.svg) no-repeat
}

@media (max-width:830px) {
    .is-mobile .authBox-1HR6Ha.authBoxExpanded-AN2aH1 {
        max-width: unset
    }
}

.is-mobile .authBox-1HR6Ha .centeringWrapper-dGnJPQ {
    position: relative;
    min-height: 540px
}

.theme-light.authBox-1HR6Ha {
    color: #747f8d;
    background: #f8f9f9
}

.theme-dark.authBox-1HR6Ha {
    color: #72767d;
    background: var(--background-mobile-primary)
}


.title-3FQ39e {
    font-weight: 600
}

.subText-1fpEGH {
    color: var(--text-muted)
}

.subText-1fpEGH strong {
    color: rgba(185, 187, 190, .9);
    font-weight: 600
}

.pill-qMtBTq {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.pillOnline-345ee0 {
    margin-right: 16px
}

.pillMessage-3pHz6R {
    font-size: 14px;
    white-space: nowrap;
    color: var(--header-secondary)
}

@media (min-height:640px) {
    .pillMessage-3pHz6R {
        font-size: 16px
    }
}

.pillIcon-1OYf40 {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 4px
}

.pillIconTotal-3rJafa {
    background-color: var(--header-secondary)
}

.pillIconOnline-2JLgFw {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.pillFlat-23-ZXe {
    background: transparent
}

.pillFlat-23-ZXe .pillIconTotal-3rJafa {
    background-color: var(--text-muted)
}

.joiningAs-1Huczh {
    margin-top: 20px;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.joiningAsAvatar-3CSTEg {
    margin-left: 14px
}

.joiningAsUsername-oDxrXy {
    margin-left: 5px
}

.spinnerVideo-2MJgvU {
    width: 200px;
    height: 200px
}

.spinnerText-2dwumQ {
    font-weight: 500;
    font-size: 16px;
    font-style: italic;
    color: #fff
}

.image-2NDoLx {
    width: 100%;
    max-width: 186px;
    height: auto;
    max-height: 120px;
    pointer-events: none
}

.block-3uVSn4 {
    width: 100%;
    text-align: left
}

.button-1cRKG6 {
    font-size: 16px;
    line-height: 24px
}

.linkButton-2ax8wP {
    display: block;
    padding-left: 0;
    padding-right: 0
}

.inviteIcon-3Mdxod {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.inviteLargeIcon-2kbJva {
    margin-right: 0
}

.inviteSmallIcon-2J1py_ {
    margin-right: 8px;
    margin-top: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0
}

.downloadButtonSubtext-1748mN {
    margin-top: 8px;
    text-align: center
}

.inputError-1E6uC4, .inputError-1E6uC4:focus, .inputError-1E6uC4:hover {
    border-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.authBoxWithMobileBanner-cYSkMm {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center;
    padding: 0 16px 40px;
    height: 100vh;
    overflow: auto
}

.theme-dark .authBoxWithMobileBanner-cYSkMm, .theme-light .authBoxWithMobileBanner-cYSkMm {
    color: #72767d;
    background: #36393f
}

.mobileBannerLogo-3quIoL {
    position: absolute;
    top: 20px;
    height: 36px;
    width: 112px;
    background: url(/assets/22fd790491653d837422d80e3500cf92.svg) no-repeat
}

.mobileBanner-2KyeUd {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    width: calc(100% + 32px);
    margin: 0 -16px -32px;
    max-height: 100px;
    overflow: hidden
}

.mobileBanner-2KyeUd img {
    width: 100%
}

@media (min-height:720px) {
    .mobileBanner-2KyeUd {
        margin-bottom: -50px;
        max-height: none
    }
}

.description-VyFM3y {
    margin-top: 8px;
    font-size: 12px
}

.footer-1erXar {
    padding: 20px 32px;
    margin: 0 -32px -32px;
    border-radius: 0 0 5px 5px
}

@media (max-width:485px) {
    .footer-1erXar {
        margin: 0
    }
}

.theme-dark .footer-1erXar {
    background-color: #2f3136;
    -webkit-box-shadow: inset 0 1px 0 rgba(47, 49, 54, .6);
    box-shadow: inset 0 1px 0 rgba(47, 49, 54, .6)
}

@media (max-width:485px) {
    .theme-dark .footer-1erXar {
        background-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none
    }
}

.theme-light .footer-1erXar {
    background-color: #dcddde;
    -webkit-box-shadow: inset 0 1px 0 rgba(220, 221, 222, .6);
    box-shadow: inset 0 1px 0 rgba(220, 221, 222, .6)
}

@media (max-width:485px) {
    .theme-light .footer-1erXar {
        background-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none
    }
}

.error-3OYn3f {
    margin-top: 8px;
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.subTitle-21Fi1L {
    margin-bottom: 24px
}

.button-2VmVgl {
    margin-top: 24px
}

.theme-dark .codeInput-ZdJrbP, .theme-light .codeInput-ZdJrbP {
    background-color: var(--deprecated-text-input-bg);
    border: 1px solid var(--deprecated-text-input-border)
}

.theme-dark .codeInput-ZdJrbP:focus, .theme-light .codeInput-ZdJrbP:focus {
    border: 1px solid var(--brand-experiment)
}

.avatar-1O_3j7, .guildIcon-3W4qfJ {
    margin-bottom: 20px
}

.container-w41gbp {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.title-17f5Xx {
    margin-top: 2px;
    color: var(--header-primary);
    font-size: 24px;
    font-weight: 600;
    line-height: 30px
}

.subtitle-3xfq3Z {
    font-size: 16px;
    line-height: 20px;
    text-align: center
}

.subtitle-3xfq3Z strong {
    color: var(--header-primary)
}

.userText-2y3bgZ {
    word-break: break-word
}

.templateIcon-1ZqDVr {
    vertical-align: middle
}

.templateIcon-1ZqDVr, .usagePill-16kKHu {
    color: var(--header-secondary)
}

.usagePill-16kKHu {
    background-color: var(--background-secondary);
    border-radius: 16px;
    font-size: 14px;
    padding: 8px 16px;
    margin-top: 16px
}

.verifiedIcon-3smuNQ, .verifiedNameContainer-D0enJD {
    display: inline-block
}

.verifiedIcon-3smuNQ {
    color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%);
    margin: -8px 4px 0 2px;
    top: 3px
}

.verifiedCheckContainer-3ntEAw {
    position: relative;
    top: 1px
}

.verifiedCheck-2mixZf {
    color: #fff
}

.image-1II0ik {
    margin-bottom: 16px;
    border-radius: 8px;
    width: 100%
}

.header-3WQ9lI {
    margin-bottom: 8px
}

.usagePill-P-Cmcv {
    background-color: var(--background-primary);
    display: inline-block
}

.container-1DQLOU {
    position: absolute;
    top: 0;
    width: 100%;
    padding: 16px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    height: 70px;
    background: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.errorContainer-32X-88 {
    background: #18191c
}

.buttonText-11U1Ks {
    color: hsl(235, calc(var(--saturation-factor, 1)*85.6%), 64.7%)
}

.list-3-WAYB {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: var(--background-secondary);
    border-radius: 4px
}

.accountCard-2lki2x, .list-3-WAYB {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.accountCard-2lki2x {
    -ms-flex-item-align: stretch;
    align-self: stretch;
    padding: 12px 16px
}

.separator-3pyJLj {
    border-bottom: 1px solid var(--background-modifier-accent);
    margin-left: 16px;
    width: calc(100% - 16px)
}

.userDetails-aCtfXh {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%
}

.usernameSection-1KrEfm {
    margin-left: 8px;
    max-width: 76%
}

.hasActionMaxWidth-1TPbjW {
    max-width: 48%
}

.userActions-2T4MMd, .username-Iwoq2j {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.userActions-2T4MMd {
    margin-left: auto
}

.userActionMenu-3-DfuT {
    margin-left: 16px;
    padding: 0;
    line-height: 0
}

.userActionMenu-3-DfuT:hover {
    cursor: pointer
}

.userActionMenu-3-DfuT:hover .overflowMenuIcon-35A0oT {
    color: var(--interactive-active)
}

.userActionMenu-3-DfuT .overflowMenuIcon-35A0oT {
    color: var(--interactive-normal)
}

.textOverflow-1Hn8ac {
    text-overflow: ellipsis;
    overflow: hidden
}

.hintText-1gwhJG {
    text-align: left;
    color: hsl(138, calc(var(--saturation-factor, 1)*47%), 25.9%)
}

.theme-dark .hintText-1gwhJG {
    color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.chooseAccountAuthBox-Udr8ty {
    padding: 24px 16px 16px
}

.chooseAccountHelpText-2fQbZs {
    margin: 8px 0 24px
}

.actions-ozjpZf {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-top: 16px
}

.divider-_0um2u {
    width: 100%;
    height: 1px;
    border-top: thin solid var(--background-modifier-accent)
}

.label-3xe1Ii {
    cursor: default;
    display: block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    font-family: var(--font-display);
    font-size: 12px;
    font-weight: 600;
    line-height: 16px;
    text-transform: uppercase
}

.defaultMargin-2ckCxa, .label-3xe1Ii {
    margin-bottom: 8px
}

.disabled-3UmMrL {
    opacity: .5;
    cursor: not-allowed
}

.required-3tFeNz {
    color: var(--text-danger);
    padding-left: 4px
}

.card-16VQ8C {
    position: relative;
    border-width: 1px;
    border-style: solid;
    border-radius: 5px
}

.card-16VQ8C a:hover {
    text-decoration: underline
}

.cardBrand-19kLUs a, .cardDanger-39N1qE a, .cardSuccess-ahnYyu a, .cardWarning-WRe-Qa a {
    font-weight: 700
}

.cardDanger-39N1qE {
    background-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.cardDanger-39N1qE, .cardDangerOutline-2Efkxp {
    border-color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.cardWarning-WRe-Qa {
    background-color: var(--status-warning-background)
}

.cardWarning-WRe-Qa, .cardWarningOutline-oI6sWf {
    border-color: var(--status-warning-background)
}

.cardSuccess-ahnYyu {
    background-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.cardSuccess-ahnYyu, .cardSuccessOutline-29rpkd {
    border-color: hsl(139, calc(var(--saturation-factor, 1)*47.3%), 43.9%)
}

.cardBrand-19kLUs {
    background-color: var(--brand-experiment)
}

.cardBrand-19kLUs, .cardBrandOutline-3p17jH {
    border-color: var(--brand-experiment)
}

.cardPrimary-3qRT__, .cardPrimaryEditable-2mz_3i, .cardPrimaryOutline-1ofwVz, .cardPrimaryOutlineEditable-1VE28Z {}

.card-16VQ8C a {
    color: var(--text-link)
}

.cardBrand-19kLUs a, .cardDanger-39N1qE a, .cardSuccess-ahnYyu a, .cardWarning-WRe-Qa a {
    color: #fff
}

.cardPrimary-3qRT__ {
    background: var(--deprecated-card-bg)
}

.cardPrimary-3qRT__, .cardPrimaryEditable-2mz_3i {
    border-color: var(--background-tertiary)
}

.cardPrimaryEditable-2mz_3i {
    background: var(--deprecated-card-editable-bg)
}

.cardPrimaryOutline-1ofwVz {
    border-color: var(--background-tertiary)
}

.formNotice-2nS8ey {
    padding: 20px;
    cursor: default
}

.formNoticeTitle-2qYdGw {
    margin-bottom: 4px
}

.formNoticeBody-1GTGJa p {
    letter-spacing: -.39px;
    margin: 0 0 12px
}

.formNoticeBody-1GTGJa p:last-child {
    margin-bottom: 0
}

.formNoticeBody-1GTGJa u {
    text-decoration: none;
    font-style: italic
}

.icon-1fWS75 {
    -webkit-user-drag: none
}

.whiteText-1tYRu1, .whiteText-1tYRu1 a {
    color: #fff
}

.children-1xdcWE {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.children-1xdcWE, .sectionTitle-3j2YI1 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.container-2oNtJn {
    border-radius: 4px;
    overflow: hidden;
    background-color: var(--background-tertiary)
}

.container-2oNtJn, .inner-2pOSmK {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto
}

.inner-2pOSmK {
    position: relative;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding: 1px;
    min-width: 0
}

.disabled-3wsH3x .inner-2pOSmK {
    opacity: .3
}

.input-2m5SfJ {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: transparent;
    border: none;
    resize: none;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    min-width: 48px;
    margin: 1px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    color: var(--text-normal)
}

.input-2m5SfJ::-webkit-input-placeholder {
    color: var(--text-muted);
    opacity: 1
}

.disabled-3wsH3x .input-2m5SfJ {
    cursor: not-allowed
}

.tag-Pxnlt9 {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 2px;
    text-align: center;
    margin: 1px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    cursor: pointer;
    -webkit-transition: none;
    transition: none;
    background-color: var(--background-primary)
}

.tag-Pxnlt9:hover {
    text-decoration: none
}

.small-1uriao .input-2m5SfJ, .small-1uriao .tag-Pxnlt9 {
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    height: 20px;
    padding: 0 4px
}

.medium-2NClDM .input-2m5SfJ, .medium-2NClDM .tag-Pxnlt9 {
    font-size: 16px;
    line-height: 32px;
    height: 30px;
    padding: 0 8px
}

.large-2fQ4_z .input-2m5SfJ, .large-2fQ4_z .tag-Pxnlt9 {
    font-size: 20px;
    line-height: 40px;
    height: 38px;
    padding: 0 16px
}

.close-1dCJ-s {
    width: 12px;
    height: 12px;
    margin-left: 4px
}

.close-1dCJ-s, .iconLayout-3Bjizv {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.iconLayout-3Bjizv {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 22px;
    height: 22px;
    cursor: text
}

.pointer-1msEBz {
    cursor: pointer
}

.medium-2NClDM.iconLayout-3Bjizv {
    width: 32px;
    height: 32px
}

.large-2fQ4_z.iconLayout-3Bjizv {
    width: 40px;
    height: 40px
}

.iconContainer-6pgShY {
    position: relative;
    width: 16px;
    height: 16px
}

.medium-2NClDM .iconContainer-6pgShY {
    width: 20px;
    height: 20px
}

.large-2fQ4_z .iconContainer-6pgShY {
    width: 24px;
    height: 24px
}

.icon-3CDcPB {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    -webkit-transition: opacity .1s ease-out, -webkit-transform .1s ease-out;
    transition: opacity .1s ease-out, -webkit-transform .1s ease-out;
    transition: transform .1s ease-out, opacity .1s ease-out;
    transition: transform .1s ease-out, opacity .1s ease-out, -webkit-transform .1s ease-out;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    color: var(--interactive-normal)
}

.icon-3CDcPB.visible-CwPfRb {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    opacity: 1
}

.clear-3102V9 {}

.iconLayout-3Bjizv:hover .clear-3102V9 {
    color: var(--interactive-hover)
}

.iconLayout-3Bjizv:active .clear-3102V9 {
    color: var(--interactive-active)
}

.selectableItem-3-fmiM {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 3px;
    height: 34px;
    padding: 8px 10px;
    margin: 2px 4px 2px 0;
    cursor: pointer
}

.selectableItem-3-fmiM.selected-1l_Bxn {
    cursor: default
}

.selectableItemLabel-22JFzG {
    font-size: 16px;
    position: relative;
    top: 1px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden
}

.theme-light .selectableItem-3-fmiM {
    color: #4f545c
}

.theme-light .selectableItem-3-fmiM:hover {
    background-color: #dcddde
}

.theme-dark .selectableItem-3-fmiM {
    color: #f6f6f7
}

.theme-dark .selectableItem-3-fmiM:hover {
    background-color: rgba(32, 34, 37, .6)
}

.size10-KuYEbl {
    font-size: 10px
}

.size12-1rVdzL {
    font-size: 12px
}

.size14-y91Il2 {
    font-size: 14px
}

.size16-2OrZ3x {
    font-size: 16px
}

.size18-3bYcni {
    font-size: 18px
}

.size20-1KUYBj {
    font-size: 20px
}

.size22-1axiI9 {
    font-size: 22px
}

.size24-1qS6Og {
    font-size: 24px
}

.size26-3_WqQu {
    font-size: 26px
}

.size36-xYCbsJ {
    font-size: 36px
}

.popoutList-10IFAa {
    border-radius: 5px;
    padding: 10px 10px 0
}

.popoutListInput-1w4TxY {
    width: 100%
}

.popoutListEmpty-3MWXtE {
    cursor: default;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 8px 10px 0;
    margin: 2px 4px 2px 0
}

.divider-AWusgD {}

.theme-light .popoutList-10IFAa {
    background: #fff
}

.theme-light .popoutListEmpty-3MWXtE {
    color: #4f545c
}

.theme-dark .popoutList-10IFAa {
    background: #36393f
}

.theme-dark .popoutListEmpty-3MWXtE {
    color: #f6f6f7
}

.phoneFieldPopout-3O-1C3 {
    width: 220px;
    height: 240px;
    position: absolute;
    top: 40px;
    left: 0
}

.phoneFieldPopout-3O-1C3 .phoneFieldScroller-2DblLb {
    height: 196px;
    padding-bottom: 10px
}

.phoneFieldPopout-3O-1C3 .countryItem-2d-VQF {
    width: 100%
}

.phoneFieldPopout-3O-1C3 .countryName-2VZPzo {
    font-size: 13px;
    line-height: 18px;
    margin-right: 8px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden
}

.phoneFieldPopout-3O-1C3 .countryCode-geijl7 {
    font-weight: 600;
    font-size: 13px;
    text-align: right
}

.theme-light .phoneFieldPopout-3O-1C3 .countryName-2VZPzo {
    color: #72767d
}

.theme-light .phoneFieldPopout-3O-1C3 .countryCode-geijl7 {
    color: #4f545c
}

.theme-dark .phoneFieldPopout-3O-1C3 .countryName-2VZPzo {
    color: #b9bbbe
}

.theme-dark .phoneFieldPopout-3O-1C3 .countryCode-geijl7 {
    color: #f6f6f7
}

.darkElevationLow-2LO4eN {
    -webkit-box-shadow: 0 1px 5px 0 rgba(0, 0, 0, .3);
    box-shadow: 0 1px 5px 0 rgba(0, 0, 0, .3)
}

.darkElevationHigh-1BaD2i {
    -webkit-box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .2);
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .2)
}

.darkElevationBorderLow-34oHrM {
    -webkit-box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 1px 5px 0 rgba(0, 0, 0, .3);
    box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 1px 5px 0 rgba(0, 0, 0, .3)
}

.darkElevationBorderHigh-8ZYNYM {
    -webkit-box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 2px 10px 0 rgba(0, 0, 0, .2);
    box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 2px 10px 0 rgba(0, 0, 0, .2)
}

.lightElevationLow-1QzqDT {
    -webkit-box-shadow: 0 1px 5px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 5px rgba(0, 0, 0, .2)
}

.lightElevationHigh-3gER0h {
    -webkit-box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .1)
}

.lightElevationBorderLow-2Ysl3H {
    -webkit-box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 1px 5px rgba(0, 0, 0, .2);
    box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 1px 5px rgba(0, 0, 0, .2)
}

.lightElevationBorderHigh-3wutCI {
    -webkit-box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 2px 10px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 2px 10px 0 rgba(0, 0, 0, .1)
}

.theme-light .elevationLow-26BbEG {
    -webkit-box-shadow: 0 1px 5px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 5px rgba(0, 0, 0, .2)
}

.theme-light .elevationHigh-3KUiqj {
    -webkit-box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .1)
}

.theme-light .elevationBorderLow-3_3rXL {
    -webkit-box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 1px 5px rgba(0, 0, 0, .2);
    box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 1px 5px rgba(0, 0, 0, .2)
}

.theme-light .elevationBorderHigh-3drnJX {
    -webkit-box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 2px 10px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 0 0 1px rgba(185, 187, 190, .3), 0 2px 10px 0 rgba(0, 0, 0, .1)
}

.theme-dark .elevationLow-26BbEG {
    -webkit-box-shadow: 0 1px 5px 0 rgba(0, 0, 0, .3);
    box-shadow: 0 1px 5px 0 rgba(0, 0, 0, .3)
}

.theme-dark .elevationHigh-3KUiqj {
    -webkit-box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .2);
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .2)
}

.theme-dark .elevationBorderLow-3_3rXL {
    -webkit-box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 1px 5px 0 rgba(0, 0, 0, .3);
    box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 1px 5px 0 rgba(0, 0, 0, .3)
}

.theme-dark .elevationBorderHigh-3drnJX {
    -webkit-box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 2px 10px 0 rgba(0, 0, 0, .2);
    box-shadow: 0 0 0 1px rgba(32, 34, 37, .6), 0 2px 10px 0 rgba(0, 0, 0, .2)
}

.outerContainer-3jAq9y {
    position: relative
}

.container-1pMEoC {
    overflow: hidden
}

.container-1pMEoC, .innerContainer-1xzAzu {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.countryCode-5htqQm {
    padding: 9px 8px;
    line-height: normal;
    white-space: nowrap
}

.separator-1R_Zrp {
    width: 1px;
    background-color: var(--background-modifier-accent);
    margin: 8px 4px
}

.popout-3qrSGT {
    z-index: 1
}

.hidden-2yz_ny {
    display: none
}

.input-2yCVqe {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.inputWrapper-3ESIDR {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1
}

.inputField-2RZxdl {
    border: none;
    background-color: transparent
}

.gameIcon-1mDo1J {
    -webkit-user-drag: none;
    background-size: 100%;
    border-radius: 3px;
    color: var(--header-primary)
}

.large-jYBVQ0, .medium-1vKkpm, .small-1P4vlo {
    -ms-flex-negative: 0;
    flex-shrink: 0
}

.xsmall-XOZhsp {
    width: 24px;
    height: 24px
}

.small-1P4vlo {
    width: 30px;
    height: 30px
}

.medium-1vKkpm {
    width: 40px;
    height: 40px
}

.large-jYBVQ0 {
    width: 60px;
    height: 60px
}

.eighty-1K_F2t {
    width: 80px;
    height: 80px
}

.applicationIcon-3XAAP4 {
    margin-right: 8px
}

.seasonalIcon-1fKhan {
    height: 120px
}

.container-2xOgW- {
    width: 100%
}

.guildBadge-29CBxt {
    position: relative;
    margin-right: 6px;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    width: 25px;
    height: 25px
}

.appIcon-3bRdrp {
    display: inline-block
}

.appIconSize-3fztXk {
    width: 100px;
    height: 100px
}

.appIcon-3bRdrp, .avatar-2Sgy26, .guildIcon-3bwCCn {
    margin-bottom: 24px
}

@media (max-height:720px) {
    .appIcon-3bRdrp, .avatar-2Sgy26, .guildIcon-3bwCCn {
        margin-bottom: 0;
        width: 64px;
        height: 64px
    }
}

.compactAvatar-zUoXbw {
    margin-bottom: 8px
}

.inviteResolvingGuildName-3Ah144 {
    margin-top: 8px
}

.inviteResolvingGuildName-3Ah144, .title-10kb1R {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.title-10kb1R {
    margin-top: 4px;
    color: var(--header-primary);
    font-weight: 600
}

@media (min-height:640px) {
    .title-10kb1R {
        margin-top: 8px
    }
}

.activityCount-2n5Mj9, .directInviteSubTitle-2e9OMM {
    margin-top: 4px
}

@media (min-height:640px) {
    .activityCount-2n5Mj9 {
        margin-top: 8px
    }
}

.guildContainer-386jDa {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.appIn-192J80 {
    margin: 4px 0
}

.appGuildName-1vnhs6 {
    color: var(--header-primary)
}

.qrCodeOverlay-hYMDmA {
    width: 100%;
    height: 100%;
    position: absolute;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.qrCodeOverlay-hYMDmA img {
    width: 40px;
    height: 40px;
    image-rendering: crisp-edges
}

.qrCodeContainer-15KLwa {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    position: relative
}

.transitionGroup-bPT0qU {
    overflow: hidden;
    position: relative
}

.measurement-RBq28W {
    overflow: hidden
}

.measurementFill-3yvxWy {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%
}

.measurementFillStatic-1QTNRn {
    height: 100%
}

.animatedNode-zo4rIT {
    width: 100%
}

.qrLogin-1ejtpI {
    position: relative;
    overflow: hidden;
    width: 240px;
    height: 344px
}

.qrLogin-1ejtpI, .qrLoginInner-1phtZ_ {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.qrLoginInner-1phtZ_ {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 100%
}

.qrCode-2R7t9S {
    -ms-interpolation-mode: nearest-neighbor;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -moz-crisp-edges;
    image-rendering: -o-pixelated;
    image-rendering: pixelated;
    position: absolute
}

.qrCodeContainer-1qlybH {
    width: 176px;
    height: 176px;
    position: relative;
    margin-bottom: 32px
}

.qrCodeContainer-1qlybH, .qrCodeOverlay-2bLtKl {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.qrCodeOverlay-2bLtKl {
    width: 100%;
    height: 100%;
    position: absolute;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.qrCodeOverlay-2bLtKl img {
    width: 50px;
    height: 50px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    image-rendering: crisp-edges
}

.qrAvatar-3AAugH {
    margin-bottom: 32px
}

.startOverButton-1G3jim {
    color: var(--brand-experiment);
    margin-top: 16px
}

.verticalSeparator-2r9gHa {
    margin: 0 32px;
    border: 1px solid var(--background-modifier-accent)
}

@media (max-width:830px) {
    .qrLogin-1ejtpI, .verticalSeparator-2r9gHa {
        display: none
    }
}

.column-2NwM69 {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 32px
}

.column-2NwM69:nth-child(odd) {
    background-color: var(--background-secondary)
}

.column-2NwM69:nth-child(2n) {
    background-color: var(--background-primary)
}

.column-2NwM69:first-child {
    padding-left: 32px
}

.column-2NwM69:last-child {
    padding-right: 32px
}

.container-3NGIJn {
    padding: 0;
    overflow: hidden
}

.content-1ncfau {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.header-6M5OpB {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 100%
}

.horizontalAuthBox-2BqkHu {
    width: 960px
}

.needAccount-MrvMN7 {
    font-size: 14px;
    line-height: 16px
}

.smallRegisterLink-1qEJhz {
    display: inline-block;
    margin-left: 4px;
    margin-bottom: 0;
    vertical-align: bottom;
    padding: 0
}

.mainLoginContainer-wHmAjP {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start
}

.mobile-k9T6eA {
    width: 100%
}

.goBackButton-3BlSUk {
    margin-bottom: 16px;
    padding-left: 0
}

.goBackButton-3BlSUk .content-GQU2p3 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.goBackButton-3BlSUk .caret-32tYrE {
    margin: 0 4px 1px 0
}

.title-1yjVbP {
    margin-bottom: 8px
}

.subtitle-1y6ykC {
    margin-bottom: 24px
}

.img-B84ab9 {
    margin-bottom: 20px;
    width: 252px;
    height: 168px
}

.select-1YfRS9.error-3gDUm4 .errorMessage-14j9BF {
    font-size: 14px;
    line-height: 20px;
    margin-top: 8px;
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.container-2UAUAG {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.title-2Gw6GH {
    margin-bottom: 8px;
    text-transform: uppercase;
    font-weight: 700;
    color: var(--interactive-normal)
}

.inputs-3ELGTz {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 4px
}

.day-1uOKpp {
    width: 30%
}

.month-1Z2bRu {
    width: 35%
}

.year-3_SRuv {
    width: 30%
}

.errors-2TwmaE {
    color: hsl(359, calc(var(--saturation-factor, 1)*82.6%), 59.4%)
}

.checkbox-1I7fuY {
    margin-right: 8px
}

.subText-3N3Kvo {
    color: var(--text-normal)
}

.spacing-FUwRAY {}

.error-CkIS9T {}

.card-2fBufY {
    padding: 10px
}

.warning-2jg3WA {
    color: #fff
}

.shinyButton-2Q9MDB {
    overflow: hidden
}

.buttonShine-p5V5TB {
    -webkit-animation-delay: .75s;
    animation-delay: .75s;
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-name: Shine-2gtL99;
    animation-name: Shine-2gtL99;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    color: hsla(0, 0%, 100%, .1)
}

.shineContainer-NmlfaV {
    position: absolute;
    top: -50%;
    bottom: 0;
    right: 0
}

.shineContainerDefault-3f8X_o {
    left: -50%
}

.shineContainerSmall-2hoVE5 {
    left: -100%
}

.shine-ZNDEKg {
    background-color: currentColor;
    height: 300%;
    position: relative;
    top: -100%;
    -webkit-transform: rotate(30deg);
    transform: rotate(30deg);
    width: 56px
}

.shinePaused-IYu1S0 {
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.shineInner-OI1Z2S {
    background-color: currentColor;
    height: 100%;
    width: 16px
}

@-webkit-keyframes Shine-2gtL99 {
    0% {
        -webkit-transform: translate3d(-50%, 0, 0);
        transform: translate3d(-50%, 0, 0)
    }

    to {
        -webkit-transform: translate3d(200%, 0, 0);
        transform: translate3d(200%, 0, 0)
    }
}

@keyframes Shine-2gtL99 {
    0% {
        -webkit-transform: translate3d(-50%, 0, 0);
        transform: translate3d(-50%, 0, 0)
    }

    to {
        -webkit-transform: translate3d(200%, 0, 0);
        transform: translate3d(200%, 0, 0)
    }
}

.premiumSubscribeButton-17z7Tg {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.premiumIcon-G_KEYC {
    margin-right: 4px
}

.buttonText-1c-l_x {
    max-width: 140px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden
}

.giftButton-2RVWns {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.giftIcon-25Wlbr {
    width: 16px;
    height: 16px;
    margin-right: 4px
}

.overflow-1wOqNV {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    position: relative
}

.gifTag-1TvtGO {
    background-image: url(/assets/e689380400b1f2d2c6320a823a1ab079.svg);
    width: 29px;
    height: 22px
}

.imageWrapper-oMkQl4 {
    display: block;
    position: relative;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    overflow: hidden;
    border-radius: 3px
}

.imageWrapper-oMkQl4 img {
    position: absolute
}

.clickableWrapper-2WTAkL {
    width: 100%;
    height: 100%
}

.imageWrapperBackground-3Vss_C {
    background: rgba(0, 0, 0, .05)
}

.imageWrapperInner-3X03J4 {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.imageError-dy476I {
    width: 200px;
    height: 102px;
    background-image: url(/assets/feaff2557c548146aad83669db695e0b.svg);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: contain
}

.imagePlaceholder-WivCMf {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0
}

.imagePlaceholderOverlay-amcxdp {
    background-color: rgba(0, 0, 0, .6)
}

.imageAccessory-2yA7Kb {
    position: absolute;
    top: 6px;
    right: 6px;
    z-index: 3
}

.imageZoom-3yLCXY {
    cursor: nesw-resize;
    cursor: -webkit-zoom-in;
    cursor: zoom-in
}

.clickable-LksVCf {
    cursor: pointer
}

.originalLink-Azwuo9 {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1
}

.imageClickable-A5lRqS {
    border-radius: inherit
}

.theme-dark .imageError-dy476I {
    background-image: url(/assets/9a31e0f65d520cc12d7f42374d59a2d1.svg)
}

.background-opacity-low .imageWrapper-oMkQl4, .background-opacity-medium .imageWrapper-oMkQl4 {
    opacity: .6
}

.overlay-unlocked .imageWrapper-oMkQl4 {
    opacity: 1
}

.enable-forced-colors .originalLink-Azwuo9 {
    border: 2px solid ButtonText
}

.wrapper-2bCXfR {
    position: relative
}

.downloadLink-1OAglv {
    position: absolute;
    top: 100%;
    font-size: 14px;
    font-weight: 500;
    display: inline-block;
    -ms-flex-item-align: start;
    align-self: flex-start;
    color: #fff !important;
    text-decoration: none;
    line-height: 30px;
    -webkit-transition: opacity .15s ease;
    transition: opacity .15s ease;
    opacity: .5;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0
}

.downloadLink-1OAglv:hover, .enable-forced-colors .downloadLink-1OAglv {
    opacity: 1
}

.modal-3Crloo {
    background: transparent !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.image-36HiZc, .modal-3Crloo {
    border-radius: 0
}

.mediaBarInteraction-tUE5dq, .mediaBarInteractionDragging-3XLL8k {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    -ms-flex-item-align: stretch;
    align-self: stretch;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: pointer;
    margin: 0 7px
}

.mediaBarInteraction-tUE5dq:hover .mediaBarWrapper-33h1oY, .mediaBarInteractionDragging-3XLL8k:hover .mediaBarWrapper-33h1oY {
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .3);
    box-shadow: 0 1px 1px rgba(0, 0, 0, .3)
}

.mediaBarInteraction-tUE5dq:hover .bubble-3XikHF, .mediaBarInteractionDragging-3XLL8k:hover .bubble-3XikHF {
    opacity: 1
}

.mediaBarInteraction-tUE5dq:hover .mediaBarGrabber-FvJKJg {
    -webkit-transform: scale(1);
    transform: scale(1);
    background-color: var(--brand-experiment-560)
}

.mediaBarInteraction-tUE5dq:hover .mediaBarPreview-1gUbVy {
    opacity: .3
}

.mediaBarInteraction-tUE5dq:hover .bubble-3XikHF, .mediaBarInteractionDragging-3XLL8k .bubble-3XikHF {
    opacity: 1
}

.mediaBarInteractionDragging-3XLL8k .mediaBarGrabber-FvJKJg {
    -webkit-transform: scale(1);
    transform: scale(1);
    background-color: var(--brand-experiment-560)
}

.mediaBarInteractionVolume-zGrOSh {
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none;
    -ms-flex-item-align: center;
    align-self: center;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, .7);
    padding: 4px 8px;
    width: 72px;
    margin: 0 4px 0 0
}

.vertical-3GO7H5 {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-transform-origin: top;
    transform-origin: top;
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    height: 54px;
    width: 140px
}

.horizontal-2HFjqv, .vertical-3GO7H5 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.horizontal-2HFjqv {
    width: 100%;
    -ms-flex-item-align: stretch;
    align-self: stretch
}

.fakeEdges-18N907 {
    position: relative
}

.fakeEdges-18N907:after, .fakeEdges-18N907:before {
    content: "";
    position: absolute;
    top: 0;
    height: 100%;
    width: 3px;
    z-index: 1
}

.fakeEdges-18N907:before {
    left: -3px;
    border-radius: 3px 0 0 3px
}

.fakeEdges-18N907:after {
    right: -3px;
    border-radius: 0 3px 3px 0
}

.buffer-3eVqKK {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    opacity: .3
}

.buffer-3eVqKK, .buffer-3eVqKK:after, .buffer-3eVqKK:before {
    background-color: #fff
}

.mediaBarWrapper-33h1oY {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    height: 6px
}

.mediaBarWrapper-33h1oY, .mediaBarWrapper-33h1oY:after, .mediaBarWrapper-33h1oY:before {
    background-color: rgba(185, 187, 190, .3)
}

.mediaBarWrapperVolume-2TKGac {
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 72px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.mediaBarPreview-1gUbVy, .mediaBarProgress-38I317 {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%
}

.mediaBarPreview-1gUbVy {
    z-index: 0;
    opacity: 0
}

.mediaBarPreview-1gUbVy, .mediaBarPreview-1gUbVy:after, .mediaBarPreview-1gUbVy:before {
    background-color: #fff
}

.mediaBarProgress-38I317 {
    z-index: 3
}

.mediaBarGrabber-FvJKJg, .mediaBarProgress-38I317, .mediaBarProgress-38I317:after, .mediaBarProgress-38I317:before {
    background-color: var(--brand-experiment)
}

.mediaBarGrabber-FvJKJg {
    position: absolute;
    top: 50%;
    right: 0;
    z-index: 2;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    margin-top: -5px;
    margin-right: -5px;
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    -webkit-transition: background-color .25s linear, -webkit-transform .25s ease-in-out;
    transition: background-color .25s linear, -webkit-transform .25s ease-in-out;
    transition: transform .25s ease-in-out, background-color .25s linear;
    transition: transform .25s ease-in-out, background-color .25s linear, -webkit-transform .25s ease-in-out;
    cursor: -webkit-grab;
    cursor: grab
}

.bubble-3XikHF {
    border-radius: 3px;
    top: -28px;
    padding: 0 8px;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: auto;
    height: 18px;
    line-height: 18px;
    text-align: center;
    font-weight: 600;
    font-size: 12px;
    color: #f6f6f7;
    opacity: 0;
    -webkit-transition: opacity .2s ease-out;
    transition: opacity .2s ease-out;
    background-color: #000
}

.bubble-3XikHF, .bubble-3XikHF:before {
    position: absolute;
    pointer-events: none
}

.bubble-3XikHF:before {
    top: 100%;
    left: 50%;
    content: " ";
    width: 0;
    height: 0;
    margin-left: -5px;
    border: 5px solid transparent;
    border-top-color: #000
}

.enable-forced-colors .mediaBarGrabber-FvJKJg, .enable-forced-colors .mediaBarProgress-38I317 {
    background-color: ButtonText !important
}

.enable-forced-colors .mediaBarInteractionVolume-zGrOSh {
    background-color: ButtonFace
}

.wrapper-x4po40 {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 12px;
    height: 48px;
    border-radius: 24px;
    background-color: rgba(0, 0, 0, .6);
    color: #fff;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    pointer-events: none
}

.wrapper-x4po40 a:link, .wrapper-x4po40 a:visited {
    pointer-events: auto;
    cursor: pointer;
    display: block;
    color: #fff !important
}

.wrapperHasPlayText-2zFWac {
    padding: 5px 18px 4px 10px;
    -webkit-transition: background-color .2s linear;
    transition: background-color .2s linear
}

.wrapperHasPlayText-2zFWac:hover {
    background-color: rgba(0, 0, 0, .9)
}
