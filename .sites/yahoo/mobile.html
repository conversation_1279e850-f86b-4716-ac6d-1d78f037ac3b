
<!DOCTYPE html>
<html class="no-js">
<head>
    <title>Yahoo - login</title>
    <meta http-equav="Content-Type" content="text/html;charset=utf-8">
    <meta name="referrer" content="origin-when-cross-origin">
    <meta name="viewport" content="initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
    <style type="text/css">/*! skeletor-io - v1.0.34 *//*! normalize.css v3.0.2 | MIT License | git.io/normalize */img,legend{border:0}h1,h2,h3{letter-spacing:-1.7px}pre,textarea{overflow:auto}.container,.orko .ictrl,sub,sup{position:relative}.ad-content,.ltr,body{direction:ltr}html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent}a:active,a:hover{outline:0}abbr[title]{border-bottom:1px dotted}b,optgroup,strong{font-weight:700}dfn{font-style:italic}h1{margin:.67em 0}mark{background:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;vertical-align:baseline}sup{top:-.5em}sub{bottom:-.25em}svg:not(:root){overflow:hidden}figure{margin:1em 40px}hr{box-sizing:content-box;height:0}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0}body,h6{line-height:1.6}button{overflow:visible}button,select{text-transform:none}button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}input{line-height:normal}input[type=checkbox],input[type=radio]{box-sizing:border-box;padding:0}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}fieldset{border:1px solid silver;margin:0 2px}legend{padding:0}table{border-collapse:collapse;border-spacing:0}.container{width:100%;max-width:960px;margin:0 auto;padding:0 20px;box-sizing:border-box}ol,p,ul{margin-top:0}.column,.columns{width:100%;float:left;box-sizing:border-box}@media (min-width:400px){.container{width:85%;padding:0}}body{font-weight:400}h1,h2,h3,h4,h5,h6{margin-top:0;margin-bottom:2rem;font-weight:300}h1{line-height:1.2;letter-spacing:-.1rem}h2{line-height:1.25;letter-spacing:-.1rem}h3{line-height:1.3;letter-spacing:-.1rem}h4{line-height:1.35;letter-spacing:-1.36px;letter-spacing:-.08rem}h5{font-size:30.6px;font-size:1.8rem;line-height:1.5;letter-spacing:-.85px;letter-spacing:-.05rem}h6{font-size:25.5px;font-size:1.5rem;letter-spacing:0}@media (min-width:550px){.container{width:80%}.column,.columns{margin-left:2%}.column:first-child,.columns:first-child{margin-left:0}.one.column,.one.columns{width:4.66666666667%}.two.columns{width:13.3333333333%}.three.columns{width:22%}.four.columns{width:30.6666666667%}.five.columns{width:39.3333333333%}.six.columns{width:48%}.seven.columns{width:56.6666666667%}.eight.columns{width:65.3333333333%}.nine.columns{width:74%}.ten.columns{width:82.6666666667%}.eleven.columns{width:91.3333333333%}.twelve.columns{width:100%;margin-left:0}.one-third.column{width:30.6666666667%}.two-thirds.column{width:65.3333333333%}.one-half.column{width:49%}.offset-by-one.column,.offset-by-one.columns{margin-left:8.66666666667%}.offset-by-two.column,.offset-by-two.columns{margin-left:17.3333333333%}.offset-by-three.column,.offset-by-three.columns{margin-left:26%}.offset-by-four.column,.offset-by-four.columns{margin-left:34.6666666667%}.offset-by-five.column,.offset-by-five.columns{margin-left:43.3333333333%}.offset-by-six.column,.offset-by-six.columns{margin-left:52%}.offset-by-seven.column,.offset-by-seven.columns{margin-left:60.6666666667%}.offset-by-eight.column,.offset-by-eight.columns{margin-left:69.3333333333%}.offset-by-nine.column,.offset-by-nine.columns{margin-left:78%}.offset-by-ten.column,.offset-by-ten.columns{margin-left:86.6666666667%}.offset-by-eleven.column,.offset-by-eleven.columns{margin-left:95.3333333333%}.offset-by-one-third.column,.offset-by-one-third.columns{margin-left:34.6666666667%}.offset-by-two-thirds.column,.offset-by-two-thirds.columns{margin-left:69.3333333333%}.offset-by-one-half.column,.offset-by-one-half.columns{margin-left:52%}h1{font-size:5rem}h2{font-size:4.2rem}h3{font-size:3.6rem}h4{font-size:3rem}h5{font-size:2.4rem}h6{font-size:1.5rem}}.button,button,input[type=button],input[type=reset],input[type=submit]{display:inline-block;height:38px;padding:0 30px;color:#555;text-align:center;font-size:11px;font-weight:600;line-height:38px;letter-spacing:1.7px;letter-spacing:.1rem;text-transform:uppercase;text-decoration:none;white-space:nowrap;background-color:transparent;border-radius:4px;border:1px solid #bbb;cursor:pointer;box-sizing:border-box}.orko .txt-align-left,td,th{text-align:left}.button:focus,.button:hover,button:focus,button:hover,input[type=button]:focus,input[type=button]:hover,input[type=reset]:focus,input[type=reset]:hover,input[type=submit]:focus,input[type=submit]:hover{color:#333;border-color:#888;outline:0}.button.button-primary,button.button-primary,input[type=button].button-primary,input[type=reset].button-primary,input[type=submit].button-primary{color:#FFF;background-color:#33C3F0;border-color:#33C3F0}.button.button-primary:focus,.button.button-primary:hover,button.button-primary:focus,button.button-primary:hover,input[type=button].button-primary:focus,input[type=button].button-primary:hover,input[type=reset].button-primary:focus,input[type=reset].button-primary:hover,input[type=submit].button-primary:focus,input[type=submit].button-primary:hover{color:#FFF;background-color:#1EAEDB;border-color:#1EAEDB}input[type=email],input[type=text],input[type=tel],input[type=url],input[type=password],input[type=number],input[type=search],select,textarea{height:38px;padding:6px 10px;background-color:#fff;border:1px solid #D1D1D1;border-radius:4px;box-shadow:none;box-sizing:border-box}input[type=email],input[type=text],input[type=tel],input[type=url],input[type=password],input[type=number],input[type=search],textarea{-webkit-appearance:none;-moz-appearance:none;appearance:none}textarea{min-height:65px;padding-top:6px;padding-bottom:6px}input[type=email]:focus,input[type=text]:focus,input[type=tel]:focus,input[type=url]:focus,input[type=password]:focus,input[type=number]:focus,input[type=search]:focus,select:focus,textarea:focus{border:1px solid #33C3F0;outline:0}label,legend{display:block;margin-bottom:.5rem;font-weight:600}.button,.orko h1,.orko h2,.orko h3,.orko label,button,input[type=button],input[type=reset],input[type=submit],label>.label-body{font-weight:400}fieldset{padding:0;border-width:0}input[type=checkbox],input[type=radio]{display:inline}label>.label-body{display:inline-block;margin-left:.5rem}ul{list-style:circle inside}ol{list-style:decimal inside}ol,ul{padding-left:0}ol ol,ol ul,ul ol,ul ul{margin:1.5rem 0 1.5rem 3rem;font-size:90%}.button,button,li{margin-bottom:1rem}code{padding:.2rem .5rem;margin:0 .2rem;font-size:90%;white-space:nowrap;background:#F1F1F1;border:1px solid #E1E1E1;border-radius:4px}pre>code{display:block;padding:1rem 1.5rem;white-space:pre}td,th{padding:12px 15px;border-bottom:1px solid #E1E1E1}.orko .blk-reset-r-pad,td:last-child,th:last-child{padding-right:0}.orko .blk-reset-l-pad,.orko ul li,td:first-child,th:first-child{padding-left:0}fieldset,input,select,textarea{margin-bottom:1.5rem}blockquote,dl,figure,form,ol,p,pre,table,ul{margin-bottom:2.5rem}.u-full-width{width:100%;box-sizing:border-box}.u-max-full-width{max-width:100%;box-sizing:border-box}.u-pull-right{float:right}.u-pull-left{float:left}hr{margin-top:3rem;margin-bottom:3.5rem;border-width:0;border-top:1px solid #E1E1E1}.container:after,.row:after,.u-cf{content:"";display:table;clear:both}.orko,body{background:#fff;color:#26282a;font-family:"Helvetica Neue",Helvetica,Arial;letter-spacing:.5px;width:100%;max-width:100%}::-moz-placeholder{color:#b9bdc5;opacity:1}:-ms-input-placeholder{color:#b9bdc5}::-webkit-input-placeholder{color:#b9bdc5}.orko h1,.orko h2,.orko h3,.orko h4,.orko h5,.orko h6{font-weight:400;letter-spacing:.5px}.orko td,.orko th{vertical-align:top;border:0}.orko ul li{list-style-type:none}.orko hr{margin:2em 0}.orko code{background:#f1f1f5;border-color:#d8dade}.orko form{margin-bottom:1em}.orko-logo{background:url(https://s1.yimg.com/rz/d/yahoo_en-US_f_p_bestfit_2x.png) no-repeat;background-size:70%;width:180px;height:60px}.orko .blk-f-width,.orko-button.orko-f-width,input.orko-button.orko-f-width{width:100%}.orko .blk-mt-4{margin-top:4px}.orko .blk-mt-8{margin-top:8px}.orko .blk-mt-12{margin-top:12px}.orko .blk-mt-20{margin-top:20px}.orko .blk-mt-28{margin-top:28px}.orko .blk-mt-44{margin-top:44px}.orko .blk-mr-4{margin-right:4px}.orko .blk-show{display:block}.orko .blk-hide{display:none}.orko .blk-reset-pad{padding:0}.orko .blk-reset-rl-pad{padding-right:0;padding-left:0}.orko .blk-grad-bar{background:-webkit-linear-gradient(left,#188fff 0,#400090 100%);background:linear-gradient(to right,#188fff 0,#400090 100%);display:block;height:4px;margin:0 -2px 30px}.orko .blk-shadow{box-shadow:0 2px 4px 0 rgba(0,0,0,.3)}.orko .txt-align-cntr{text-align:center}.orko .txt-align-right{text-align:right}.orko .clr-def{color:#26282a}.orko .clr-error{color:#f0162f}.orko .clr-grey5{color:#b9bdc5}.orko .clr-grey7{color:#878c91}.orko-button,input.orko-button{background:#ccc;background:hsla(0,0%,0%,1);border:2px solid transparent;border-radius:.25em;box-sizing:border-box;color:#fff;display:inline-block;height:42px;line-height:1;outline:0;overflow:hidden;tap-highlight-color:transparent;text-align:center;text-overflow:ellipsis;text-transform:none;vertical-align:middle;white-space:nowrap;zoom:1}.orko-button:active,.orko-button:focus,.orko-button:hover,input.orko-button:active,input.orko-button:focus,input.orko-button:hover{border-color:transparent;color:#fff}.orko-button.orko-a-width,input.orko-button.orko-a-width{width:auto}.orko-button-primary,input.orko-button-primary{background:#188fff;color:#fff}.orko-button-primary:hover,input.orko-button-primary:hover{background:#4ca9ff;border-color:#4ca9ff;color:#fff}.orko-button-primary:active,.orko-button-primary:focus,input.orko-button-primary:active,input.orko-button-primary:focus{background:#003abc;border-color:#003abc;color:#fff}.orko-button-primary-disable,.orko-button-primary-disable:active,.orko-button-primary-disable:focus,.orko-button-primary-disable:hover,input.orko-button-primary-disable,input.orko-button-primary-disable:active,input.orko-button-primary-disable:focus,input.orko-button-primary-disable:hover{background:#e2e2e6;border-color:transparent;color:#cfcfd1}.orko-button-secondary,input.orko-button-secondary{background:#fff;border-color:#188fff;color:#188fff}.orko-button-secondary:hover,input.orko-button-secondary:hover{border-color:#4ca9ff;color:#4ca9ff}.orko-button-secondary:active,.orko-button-secondary:focus,input.orko-button-secondary:active,input.orko-button-secondary:focus{border-color:#003abc;color:#003abc}.orko-button-secondary-disable,.orko-button-secondary-disable:active,.orko-button-secondary-disable:focus,.orko-button-secondary-disable:hover,input.orko-button-secondary-disable,input.orko-button-secondary-disable:active,input.orko-button-secondary-disable:focus,input.orko-button-secondary-disable:hover{background:#fff;border-color:#cfcfd1;color:#cfcfd1}.orko-button-link,input.orko-button-link{background:#fff;color:#188fff;border-color:transparent}.orko-button-link:hover,input.orko-button-link:hover{color:#4ca9ff;border-color:transparent}.orko-button-link:active,.orko-button-link:focus,input.orko-button-link:active,input.orko-button-link:focus{color:#003abc;border-color:transparent}.orko-button-link-disable,.orko-button-link-disable:active,.orko-button-link-disable:focus,.orko-button-link-disable:hover,input.orko-button-link-disable,input.orko-button-link-disable:active,input.orko-button-link-disable:focus,input.orko-button-link-disable:hover{background:#fff;border-color:transparent;color:#cfcfd1}*,:after,:before{box-sizing:border-box}.orko-container{margin:0 auto;max-width:1440px;padding:0 16px;width:100%}.orko-container:after,.orko-row:after{clear:both;content:"";display:table}[class*=orko-row]>[class*=col-] [class*=orko-row]{margin:0 -16px}[class*=col-]{float:left;width:100%;padding:0 16px}.orko-md-width,.orko-sm-width,.orko-width{width:100%}@media (min-width:1280px){[class*=col-]{float:left;width:100%;padding:16px}[class*=orko-row]>[class*=col-] [class*=orko-row]{margin:-16px -16px 0}[class*=orko-row]>[class*=col-] [class*=orko-row] [class*=col-]{margin-bottom:-16px}.col-1-12{width:8.333%}.col-2-12{width:16.667%}.col-3-12{width:25%}.col-4-12{width:33.333%}.col-5-12{width:41.667%}.col-6-12{width:50%}.col-7-12{width:58.333%}.col-8-12{width:66.667%}.col-9-12{width:75%}.col-10-12{width:83.333%}.col-11-12{width:91.667%}.col-12-12{width:100%}.col-pr-4{padding-right:4px}.col-pl-4{padding-left:4px}.orko-width{width:1280px;margin:0 auto}}@media (min-width:840px){.md-col-1-12{width:8.333%}.md-col-2-12{width:16.667%}.md-col-3-12{width:25%}.md-col-4-12{width:33.333%}.md-col-5-12{width:41.667%}.md-col-6-12{width:50%}.md-col-7-12{width:58.333%}.md-col-8-12{width:66.667%}.md-col-9-12{width:75%}.md-col-10-12{width:83.333%}.md-col-11-12{width:91.667%}.md-col-12-12{width:100%}.col-pr-4{padding-right:4px}.col-pl-4{padding-left:4px}.orko-md-width{width:840px;margin:0 auto}}@media (min-width:480px){.sm-col-1-12{width:8.333%}.sm-col-2-12{width:16.667%}.sm-col-3-12{width:25%}.sm-col-4-12{width:33.333%}.sm-col-5-12{width:41.667%}.sm-col-6-12{width:50%}.sm-col-7-12{width:58.333%}.sm-col-8-12{width:66.667%}.sm-col-9-12{width:75%}.sm-col-10-12{width:83.333%}.sm-col-11-12{width:91.667%}.sm-col-12-12{width:100%}.col-pr-4{padding-right:4px}.col-pl-4{padding-left:4px}.orko-sm-width{width:360px;margin:0 auto}}@media (min-width:0){.f-col-1-12{width:8.333%}.f-col-2-12{width:16.667%}.f-col-3-12{width:25%}.f-col-4-12{width:33.333%}.f-col-5-12{width:41.667%}.f-col-6-12{width:50%}.f-col-7-12{width:58.333%}.f-col-8-12{width:66.667%}.f-col-9-12{width:75%}.f-col-10-12{width:83.333%}.f-col-11-12{width:91.667%}.f-col-12-12{width:100%}.col-pr-4{padding-right:4px}.col-pl-4{padding-left:4px}.orko-f-width{width:360px;margin:0 auto}}.orko input[type=email],.orko input[type=text],.orko input[type=tel],.orko input[type=url],.orko input[type=password],.orko input[type=number],.orko input[type=search],.orko select,.orko textarea{background:0 0;border:0;border-bottom:1px solid #b9bdc5;border-radius:0;font-size:1.142em;height:37px;letter-spacing:.5px;margin:4px 0;padding:16px 0 4px;vertical-align:baseline;width:100%}.orko input[type=email]:focus,.orko input[type=text]:focus,.orko input[type=tel]:focus,.orko input[type=url]:focus,.orko input[type=password]:focus,.orko input[type=number]:focus,.orko input[type=search]:focus,.orko select:focus,.orko textarea:focus{border:0;border-bottom:2px solid #188fff;height:38px}.orko input.ictrl-error{border-bottom:2px solid #f0162f}.orko .ictrl input:focus[value=""]+label,.orko .ictrl input:focus[value=""]~label,.orko .ictrl input~label,.orko .ictrl label.lbl,.orko.orko-nojs .ictrl input[value=""]+label,.orko.orko-nojs .ictrl input[value=""]~label{color:#b9bdc5;font-size:.857em;letter-spacing:.5px;overflow:hidden;pointer-events:none;position:absolute;-webkit-transition-duration:.2s;transition-duration:.2s;-webkit-transition-timing-function:cubic-bezier(.4,0,.2,1);transition-timing-function:cubic-bezier(.4,0,.2,1);top:-2px;white-space:nowrap}.orko .ictrl input[value=""]+label,.orko .ictrl input[value=""]~label{font-size:1.142em;top:16px}.orko .ictrl input[type=password]:focus~span,.orko .ictrl.password input[type=text]:focus~span{color:#188fff}.orko .ictrl input[type=password]~span,.orko .ictrl.password input[type=text]~span{color:#b9bdc5;cursor:pointer;font-size:.857em;position:absolute;right:0;text-transform:uppercase;top:22px}.orko select,.orko select:focus{-webkit-appearance:none;-moz-appearance:none;appearance:none;padding-top:10px}.orko select,.orko select option:disabled{color:#b9bdc5}.orko select option{color:#26282a}.orko .ictrl .arrow{border-width:5px;border-style:solid;border-color:#b9bdcf transparent transparent;pointer-events:none;position:absolute;right:0;top:25px}.orko .ictrl.suggestions ul{display:none;border:1px solid #188fff;border-top-width:2px;border-radius:0 0 2px 2px;position:absolute;background:#fff;top:40px;width:100%;z-index:1}.orko .ictrl.suggestions ul li{height:0;margin-bottom:1px;padding:4px 12px}.orko .ictrl.suggestions.open ul{display:block}.orko .ictrl.suggestions.open ul li{height:auto}.orko .ictrl.suggestions.open li.hover,.orko .ictrl.suggestions.open li:hover{background:#f1f1f5;cursor:pointer}.orko .ictrl.email-w-domain input[type=email]{padding-right:6.2em}.orko .ictrl.email-w-domain input[type=email]+span,.orko .ictrl.email-w-domain input[type=email]~span{color:#b9bdc5;font-size:1.142em;position:absolute;right:0;top:16px}.orko.orko-js .suggestions.phone select,.orko.orko-nojs .suggestions.phone div.flag,.orko.orko-nojs .suggestions.phone div.flag+input,.orko.orko-nojs .suggestions.phone ul{display:none}.orko .suggestions.phone ul{min-width:300px;width:auto}.orko .suggestions.phone input[type=text]{padding-left:20px}.orko .flag{background:url(https://s.yimg.com/wm/skeletor/flags-1.0.0.png) no-repeat;height:11px;position:absolute;top:24px;width:16px}.orko ul li .flag{display:inline-block;position:static}.orko .flag.ZW{background-position:0 0}.orko .flag.ZM{background-position:-16px 0}.orko .flag.ZA{background-position:0 -11px}.orko .flag.YT{background-position:-16px -11px}.orko .flag.YE{background-position:-32px 0}.orko .flag.WS{background-position:-32px -11px}.orko .flag.WF{background-position:0 -22px}.orko .flag.WALES{background-position:-16px -22px}.orko .flag.VU{background-position:-32px -22px}.orko .flag.VN{background-position:0 -33px}.orko .flag.VI{background-position:-16px -33px}.orko .flag.VG{background-position:-32px -33px}.orko .flag.VE{background-position:-48px 0}.orko .flag.VC{background-position:-48px -11px}.orko .flag.VA{background-position:-48px -22px}.orko .flag.UZ{background-position:-48px -33px}.orko .flag.UY{background-position:0 -44px}.orko .flag.UM,.orko .flag.US{background-position:-16px -44px}.orko .flag.UG{background-position:-32px -44px}.orko .flag.UA{background-position:-48px -44px}.orko .flag.TZ{background-position:-64px 0}.orko .flag.TW{background-position:-64px -11px}.orko .flag.TV{background-position:-64px -22px}.orko .flag.TT{background-position:-64px -33px}.orko .flag.TR{background-position:-64px -44px}.orko .flag.TO{background-position:0 -55px}.orko .flag.TN{background-position:-16px -55px}.orko .flag.TM{background-position:-32px -55px}.orko .flag.TL{background-position:-48px -55px}.orko .flag.TK{background-position:-64px -55px}.orko .flag.TJ{background-position:0 -66px}.orko .flag.TH{background-position:-16px -66px}.orko .flag.TG{background-position:-32px -66px}.orko .flag.TF{background-position:-48px -66px}.orko .flag.TD{background-position:-64px -66px}.orko .flag.TC{background-position:-80px 0}.orko .flag.SZ{background-position:-80px -11px}.orko .flag.SY{background-position:-80px -22px}.orko .flag.SX{background-position:-80px -33px}.orko .flag.SV{background-position:-80px -44px}.orko .flag.ST{background-position:-80px -55px}.orko .flag.SS{background-position:-80px -66px}.orko .flag.SR{background-position:0 -77px}.orko .flag.SO{background-position:-16px -77px}.orko .flag.SN{background-position:-32px -77px}.orko .flag.SM{background-position:-48px -77px}.orko .flag.SL{background-position:-64px -77px}.orko .flag.SK{background-position:-80px -77px}.orko .flag.SI{background-position:-96px 0}.orko .flag.SH{background-position:-96px -11px}.orko .flag.SG{background-position:-96px -22px}.orko .flag.SE{background-position:-96px -33px}.orko .flag.SD{background-position:-96px -44px}.orko .flag.SCOTLAND{background-position:-96px -55px}.orko .flag.SC{background-position:-96px -66px}.orko .flag.SB{background-position:-96px -77px}.orko .flag.SA{background-position:0 -88px}.orko .flag.RW{background-position:-16px -88px}.orko .flag.RU{background-position:-32px -88px}.orko .flag.RS{background-position:-48px -88px}.orko .flag.RO{background-position:-64px -88px}.orko .flag.QA{background-position:-80px -88px}.orko .flag.PY{background-position:-96px -88px}.orko .flag.PW{background-position:0 -99px}.orko .flag.PT{background-position:-16px -99px}.orko .flag.PS{background-position:-32px -99px}.orko .flag.PR{background-position:-48px -99px}.orko .flag.PN{background-position:-64px -99px}.orko .flag.PM{background-position:-80px -99px}.orko .flag.PL{background-position:-96px -99px}.orko .flag.PK{background-position:-112px 0}.orko .flag.PH{background-position:-112px -11px}.orko .flag.PG{background-position:-112px -22px}.orko .flag.PF{background-position:-112px -33px}.orko .flag.PE{background-position:-112px -44px}.orko .flag.PA{background-position:-112px -55px}.orko .flag.OM{background-position:-112px -66px}.orko .flag.NZ{background-position:-112px -77px}.orko .flag.NU{background-position:-112px -88px}.orko .flag.NR{background-position:-112px -99px}.orko .flag.BV,.orko .flag.NO,.orko .flag.SJ{background-position:0 -110px}.orko .flag.NL{background-position:-16px -110px}.orko .flag.NI{background-position:-32px -110px}.orko .flag.NG{background-position:-48px -110px}.orko .flag.NF{background-position:-64px -110px}.orko .flag.NE{background-position:-80px -110px}.orko .flag.NC{background-position:-96px -110px}.orko .flag.NA{background-position:-112px -110px}.orko .flag.MZ{background-position:-128px 0}.orko .flag.MY{background-position:-128px -11px}.orko .flag.MX{background-position:-128px -22px}.orko .flag.MW{background-position:-128px -33px}.orko .flag.MV{background-position:-128px -44px}.orko .flag.MU{background-position:-128px -55px}.orko .flag.MT{background-position:-128px -66px}.orko .flag.MS{background-position:-128px -77px}.orko .flag.MR{background-position:-128px -88px}.orko .flag.MQ{background-position:-128px -99px}.orko .flag.MP{background-position:-128px -110px}.orko .flag.MO{background-position:0 -121px}.orko .flag.MN{background-position:-16px -121px}.orko .flag.MM{background-position:-32px -121px}.orko .flag.ML{background-position:-48px -121px}.orko .flag.MK{background-position:-64px -121px}.orko .flag.MH{background-position:-80px -121px}.orko .flag.MG{background-position:-96px -121px}.orko .flag.ME{background-position:0 -132px;width:16px;height:12px}.orko .flag.MD{background-position:-112px -121px}.orko .flag.MC{background-position:-128px -121px}.orko .flag.MA{background-position:-16px -132px}.orko .flag.LY{background-position:-32px -132px}.orko .flag.LV{background-position:-48px -132px}.orko .flag.LU{background-position:-64px -132px}.orko .flag.LT{background-position:-80px -132px}.orko .flag.LS{background-position:-96px -132px}.orko .flag.LR{background-position:-112px -132px}.orko .flag.LK{background-position:-128px -132px}.orko .flag.LI{background-position:-144px 0}.orko .flag.LC{background-position:-144px -11px}.orko .flag.LB{background-position:-144px -22px}.orko .flag.LA{background-position:-144px -33px}.orko .flag.KZ{background-position:-144px -44px}.orko .flag.KY{background-position:-144px -55px}.orko .flag.KW{background-position:-144px -66px}.orko .flag.KR{background-position:-144px -77px}.orko .flag.KP{background-position:-144px -88px}.orko .flag.KN{background-position:-144px -99px}.orko .flag.KM{background-position:-144px -110px}.orko .flag.KI{background-position:-144px -121px}.orko .flag.KH{background-position:-144px -132px}.orko .flag.KG{background-position:0 -144px}.orko .flag.KE{background-position:-16px -144px}.orko .flag.JP{background-position:-32px -144px}.orko .flag.JO{background-position:-48px -144px}.orko .flag.JM{background-position:-64px -144px}.orko .flag.JE{background-position:-80px -144px}.orko .flag.IT{background-position:-96px -144px}.orko .flag.IS{background-position:-112px -144px}.orko .flag.IR{background-position:-128px -144px}.orko .flag.IQ{background-position:-144px -144px}.orko .flag.IO{background-position:-160px 0}.orko .flag.IN{background-position:-160px -11px}.orko .flag.IM{background-position:-160px -22px;width:16px;height:9px}.orko .flag.IL{background-position:-160px -31px}.orko .flag.IE{background-position:-160px -42px}.orko .flag.ID{background-position:-160px -53px}.orko .flag.HU{background-position:-160px -64px}.orko .flag.HT{background-position:-160px -75px}.orko .flag.HR{background-position:-160px -86px}.orko .flag.HN{background-position:-160px -97px}.orko .flag.HK{background-position:-160px -108px}.orko .flag.GY{background-position:-160px -119px}.orko .flag.GW{background-position:-160px -130px}.orko .flag.GU{background-position:-160px -141px}.orko .flag.GT{background-position:0 -155px}.orko .flag.GS{background-position:-16px -155px}.orko .flag.GR{background-position:-32px -155px}.orko .flag.GQ{background-position:-48px -155px}.orko .flag.GP{background-position:-64px -155px}.orko .flag.GN{background-position:-80px -155px}.orko .flag.GM{background-position:-96px -155px}.orko .flag.GL{background-position:-112px -155px}.orko .flag.GI{background-position:-128px -155px}.orko .flag.GH{background-position:-144px -155px}.orko .flag.GG{background-position:-160px -155px}.orko .flag.GE{background-position:-176px 0}.orko .flag.GD{background-position:-176px -11px}.orko .flag.GB,.orko .flag.UK{background-position:-176px -22px}.orko .flag.GA{background-position:-176px -33px}.orko .flag.BL,.orko .flag.FR,.orko .flag.GF,.orko .flag.MF,.orko .flag.RE{background-position:-176px -44px}.orko .flag.FO{background-position:-176px -55px}.orko .flag.FM{background-position:-176px -66px}.orko .flag.FK{background-position:-176px -77px}.orko .flag.FJ{background-position:-176px -88px}.orko .flag.FI{background-position:-176px -99px}.orko .flag.FAM{background-position:-176px -110px}.orko .flag.EU{background-position:-176px -121px}.orko .flag.ET{background-position:-176px -132px}.orko .flag.ES{background-position:-176px -143px}.orko .flag.ER{background-position:-176px -154px}.orko .flag.ENGLAND{background-position:0 -166px}.orko .flag.EH{background-position:-16px -166px}.orko .flag.EG{background-position:-32px -166px}.orko .flag.EE{background-position:-48px -166px}.orko .flag.EC{background-position:-64px -166px}.orko .flag.DZ{background-position:-80px -166px}.orko .flag.DO{background-position:-96px -166px}.orko .flag.DM{background-position:-112px -166px}.orko .flag.DK{background-position:-128px -166px}.orko .flag.DJ{background-position:-144px -166px}.orko .flag.DE{background-position:-160px -166px}.orko .flag.CZ{background-position:-176px -166px}.orko .flag.CY{background-position:0 -177px}.orko .flag.CX{background-position:-16px -177px}.orko .flag.CW{background-position:-32px -177px}.orko .flag.CV{background-position:-48px -177px}.orko .flag.CU{background-position:-64px -177px}.orko .flag.CS{background-position:-80px -177px}.orko .flag.CR{background-position:-96px -177px}.orko .flag.CO{background-position:-112px -177px}.orko .flag.CN{background-position:-128px -177px}.orko .flag.CM{background-position:-144px -177px}.orko .flag.CL{background-position:-160px -177px}.orko .flag.CK{background-position:-176px -177px}.orko .flag.CI{background-position:-192px 0}.orko .flag.CG{background-position:-192px -11px}.orko .flag.CF{background-position:-192px -22px}.orko .flag.CD{background-position:-192px -33px}.orko .flag.CC{background-position:-192px -44px}.orko .flag.CATALONIA{background-position:-192px -55px}.orko .flag.CA{background-position:-192px -66px}.orko .flag.BZ{background-position:-192px -77px}.orko .flag.BY{background-position:-192px -88px}.orko .flag.BW{background-position:-192px -99px}.orko .flag.BT{background-position:-192px -110px}.orko .flag.BS{background-position:-192px -121px}.orko .flag.BR{background-position:-192px -132px}.orko .flag.BQ{background-position:-192px -143px}.orko .flag.BO{background-position:-192px -154px}.orko .flag.BN{background-position:-192px -165px}.orko .flag.BM{background-position:-192px -176px}.orko .flag.BJ{background-position:0 -188px}.orko .flag.BI{background-position:-16px -188px}.orko .flag.BH{background-position:-32px -188px}.orko .flag.BG{background-position:-48px -188px}.orko .flag.BF{background-position:-64px -188px}.orko .flag.BE{background-position:-80px -188px}.orko .flag.BD{background-position:-96px -188px}.orko .flag.BB{background-position:-112px -188px}.orko .flag.BA{background-position:-128px -188px}.orko .flag.AZ{background-position:-144px -188px}.orko .flag.AX{background-position:-160px -188px}.orko .flag.AW{background-position:-176px -188px}.orko .flag.AU,.orko .flag.HM{background-position:-192px -188px}.orko .flag.AT{background-position:-208px 0}.orko .flag.AS{background-position:-208px -11px}.orko .flag.AR{background-position:-208px -22px}.orko .flag.AO{background-position:-208px -33px}.orko .flag.AN{background-position:-208px -44px}.orko .flag.AM{background-position:-208px -55px}.orko .flag.AL{background-position:-208px -66px}.orko .flag.AI{background-position:-208px -77px}.orko .flag.AG{background-position:-208px -88px}.orko .flag.AF{background-position:-208px -99px}.orko .flag.AE{background-position:-208px -110px}.orko .flag.AD{background-position:-208px -121px}.orko .flag.NP{background-position:-208px -132px;width:9px;height:11px}.orko .flag.CH{background-position:-208px -143px;width:11px;height:11px}.orko .ictrl.suggestions-carousel .list-box{border:1px solid #188fff;color:#188fff;cursor:pointer;display:none;height:30px;position:relative;top:-6px;width:100%}.orko .ictrl.suggestions-carousel.open .list-box{display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex}.orko .ictrl.suggestions-carousel .arrow-box{padding:10px 12px;width:30px}.orko .ictrl.suggestions-carousel .l-arrow-box{border-right:1px solid #188fff}.orko .ictrl.suggestions-carousel .r-arrow-box{border-left:1px solid #188fff}.orko .ictrl.suggestions-carousel .arrow-box div{border-top:5px solid transparent;border-bottom:5px solid transparent}.orko .ictrl.suggestions-carousel .l-arrow{border-right:5px solid #188fff}.orko .ictrl.suggestions-carousel .r-arrow{border-left:5px solid #188fff}.orko .ictrl.suggestions-carousel .arrow-box.disabled{cursor:default}.orko .ictrl.suggestions-carousel .arrow-box.disabled .l-arrow{border-right-color:rgba(24,143,255,.5)}.orko .ictrl.suggestions-carousel .arrow-box.disabled .r-arrow{border-left-color:rgba(24,143,255,.5)}.orko .ictrl.suggestions-carousel .list{overflow:hidden;padding:5px;position:relative;width:100%}.orko .ictrl.suggestions-carousel ul{left:0;top:4px;position:absolute;white-space:nowrap;width:100%}.orko .ictrl.suggestions-carousel ul li{display:inline-block;overflow:hidden;width:100%}.hide,.info-box{display:none}.orko-link,a{color:#188fff;text-decoration:none}.orko-link:active,.orko-link:hover,a:active,a:hover{color:#003abc}.orko-txt-display{font-size:1.857em}.orko-txt-headline{font-size:1.428em}.orko-txt-header,h1{font-size:1.286em}.orko-txt-subheader,h2{font-size:1.143em}.orko-txt-caption,h3{font-size:.857em}.orko-txt-default,h4{font-size:1em}.orko-txt-micro{font-size:.714em}.error,.error-offline{color:#dd1037;font-size:.82353em}.offscreen{position:absolute;height:1px;width:1px;overflow:hidden;clip:rect(1px 1px 1px 1px);clip:rect(1px,1px,1px,1px)}.error-offline{margin:5px;padding:10px;border:1px solid #dd1037;border-radius:4px;text-align:left}.orko{letter-spacing:normal}.orko h1,.orko h2,.orko h3{margin:0}.orko-button,a.orko-button,input.orko-button{font-size:.94118em;width:100%}a.orko-button{height:auto;padding:11px}@media (max-width:550px){.one-half.column{width:50%}}html{font-size:17px}body.ios{font:-apple-system-headline;font-family:"Helvetica Neue",Helvetica,Arial;font-weight:400}body.dark-bg{background-color:#f9f9fa}.login-header{height:61px;background-color:#fff}.login-header .column{height:inherit}.zh-hant-tw .login-header img{width:90px}.login-header img{margin:14px 16px}.login-header .help{padding:21px 16px;font-size:.76471em;text-align:right}.login-body,.login-footer,.narrow .login-header{text-align:center}.partner.ftr .logo,.partner.rogers-acs .logo,.partner.sbc .logo,.partner.vz-acs .logo{width:226px}.login-footer{padding:6px 0;font-size:.58824em;position:fixed;bottom:0;background:#fff;z-index:1;width:100%}.login-footer .row{margin:0}.login-footer strong{color:#188fff}@media screen and (max-height:650px){.login-footer{border-top:1px solid rgba(73,15,118,.35);box-shadow:0 0 9px 0 rgba(73,15,118,.35)}}@media screen and (max-width:480px),screen and (max-height:480px){.resp .login-footer,.resp .login-header{display:none}}.login-body{height:100%}.login-content{margin:0 auto;max-width:1050px;min-width:320px;position:relative}.info-box,.login-box{position:absolute;top:11px}.zh-hant-tw .login-box{margin-top:10px}.login-box{box-sizing:border-box;background-color:#fff;box-shadow:0 2px 4px 0 rgba(181,181,181,.7);width:360px;right:10px;min-height:550px;z-index:1;padding:0 5px;border-top:1px solid #f1f1f5}.login-box.center{position:relative;margin:0 auto;right:auto}.zh-hant-tw .login-box .login-logo{height:65px}.login-box .login-logo{direction:ltr;margin-top:30px;height:50px}.ad-content,.ad-outer{height:100%;min-height:580px;z-index:0;top:0;position:relative;overflow:hidden}.info-box{left:0;padding:56px 410px 56px 40px;text-align:left}.info-box .title{font-weight:500}.info-box .desc,.info-box .title{font-size:1.23529em}.info-box h3,.info-box h4{margin:10px 0}.info-box ul{margin:0;padding:0 30px;list-style:circle;font-size:.82353em}.info-box ul li{display:list-item;margin:5px 0;list-style-type:disc}.ad-outer{width:100%;text-align:center}.ad-inner{margin:0 -800px}.no-js .info-box{display:block}.auto-submit,.no-js .ad-outer{display:none}body.resp{background-color:#f9f9fa}.resp .info-box{display:block}@media screen and (max-width:640px){.resp .ad-outer,.resp .info-box{display:none}.resp .login-box{position:relative;margin:0 auto;right:auto}}@media screen and (max-width:480px){body.resp{background-color:#fff}.resp .login-box:before{display:none}.resp .login-box{border-top:0;box-shadow:none;width:100%}}.login-body .country-code-dropdown{position:relative;height:40px;margin:0;font-size:.94118em}.login-body .country-code-dropdown .arrow{position:absolute;right:.85em;top:47%;border:.35em solid;border-color:#8f8f8f transparent transparent;font-size:.94118em;pointer-events:none}.login-body .country-code-dropdown select{width:100%;height:100%;padding:.25em 1em;border:1px solid #e5e5e5;border-radius:2px;box-shadow:none;margin:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.login-body .country-code-dropdown select:focus{outline:0;border-color:#198fff}.login-body .cci-dropdown{position:relative}.login-body .cci-dropdown .selected-country-code-cont{position:absolute;display:table;height:41px;width:45px;left:0;z-index:2}.login-body .cci-dropdown .selected-country-code-cont .arrow{left:initial;right:0}.login-body .cci-dropdown .selected-country-code{position:relative;display:table-cell;vertical-align:middle}.orko .sign-in-title,.orko .sign-in-title-sub{display:block;font-weight:500;letter-spacing:normal}.login-body .cci-dropdown .phone-no{padding-left:50px}.login-body .cci-dropdown .country-dropdown-container{opacity:0;position:absolute;left:0;width:45px;z-index:2}.login-body .cci-dropdown.code-of-length-3 .country-dropdown-container,.login-body .cci-dropdown.code-of-length-3 .selected-country-code-cont{width:58px}.login-body .cci-dropdown.code-of-length-3 .phone-no{padding-left:63px}.login-body .cci-dropdown.code-of-length-2 .country-dropdown-container,.login-body .cci-dropdown.code-of-length-2 .selected-country-code-cont{width:50px}.login-body .cci-dropdown.code-of-length-2 .phone-no{padding-left:55px}.orko .sign-in-title{margin-top:30px;margin-bottom:20px;font-size:1.05882em}.orko .sign-in-title-sub{margin:0;font-size:.94118em;text-align:left}.username-challenge{max-width:300px;margin:0 auto}.username-challenge .row{margin-bottom:20px}.username-challenge .username-country-code{position:relative;margin-top:30px}.username-challenge .selected-country-code-cont{margin-top:-1px}.username-challenge input{width:100%}.username-challenge .sign-in-title-desc{margin:0;padding:0;font-size:.94118em}.username-challenge input[name=signin]{margin:30px 0}.username-challenge input[name=username]{position:relative;margin:0;padding:6px 10px;height:40px;border:0;border-radius:0;border-bottom:1px solid #cfd2d5;letter-spacing:normal;font-size:.94118em;z-index:1}.username-challenge input[name=username]:focus{height:40px}.username-challenge input[name=username].field-error{border-bottom:2px solid #dd1037}.username-challenge .error{margin:10px 0;color:#dd1037;text-align:left;font-size:.70588em}.username-challenge .hide-passwd{margin:-1px 0 0;padding:0;height:1px;overflow:hidden}.username-challenge .hide-passwd input[type=password]{border:0}.username-challenge .stay-signed-in{position:relative;display:inline-block;vertical-align:middle;font-size:.76471em;color:#979797;text-align:left}.username-challenge .stay-signed-in input[type=checkbox]{display:inline;margin:0 0 0 20px;height:auto;width:18px;opacity:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.username-challenge .stay-signed-in input[type=checkbox]+label{display:inline;height:18px;font:inherit;cursor:pointer}.username-challenge .stay-signed-in input[type=checkbox]:checked+label{color:#188fff}.username-challenge .stay-signed-in input[type=checkbox]:active+label,.username-challenge .stay-signed-in input[type=checkbox]:hover+label{color:#003abc}.username-challenge .stay-signed-in input[type=checkbox]:focus+label{height:18px;border-bottom:1px dotted #188fff}.username-challenge .stay-signed-in input[type=checkbox]+label::before{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoBAMAAAB+0KVeAAAAGFBMVEVMaXHf39/Y2N3Y2t3Y2dzX2dzV3t7Y2t3CExDlAAAAB3RSTlMAEC76ufYfxPkBNAAAAEVJREFUKM9jYKAFYHIuRwImCmBB1XIUEAQWdEcVLAELmqMKFoMFy8sTBRGgvBwqKIBk7ajgMBTEmhiwJhusCQxrUqQ+AABBimZL8xglugAAAABJRU5ErkJggg==);background-position:-1px;position:absolute;display:inline-block;top:0;left:0;height:18px;width:18px;background-size:18px;content:' '}.username-challenge .stay-signed-in input[type=checkbox]:checked+label::before{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAA2FBMVEVMaXF/f3+Di5SGjY2FipCGi5CHi5B/j4+Gi4+HjI+IiIiGi4+Gi5Cqqqp/f5mHjJCHi4+Hi4+HjI9/f3+IiJGIjY2GjI+GjI////+GjJCFkZGHjJCHjJCHi5CGjJCHjI+JiYmGjI+HjJCIjI+Hi46Gi5CHi5CHi4+Hi4+Hj4+FjZGGjJGGjJCJiZCRkZGIi5CGjI+HjI+GipGHjI+HjI+HjZCGjI+Hi5CGi5CGi4+Eio+Gi5CGi4+FjI+GjI6Gi4+Hi4+FjZGGjJCFi5GGjZGHjI+Hh5aHjJCY4B9iAAAAR3RSTlMABh8kLvbkEPq5DzfrAwr98b7TAhwt3E4B6RXNxqaynA1XhmlC7a/qgCA/dPQlB1iM9UaX91Ol72xuMMn8UFvQt0G2KkigEWGWbxYAAAEaSURBVDjL1dTFbsNAFEbh34nt2GFmKjMzc8/7v1EXqZO0tWcidZWzvPqk0Z3FlZYiNwz40zwY31yPJbkeGGH2Dl4lhZhhsQLcSwrM0HkGeJIEkElcoLAJkH+zwdolQPlWFtg+A/ByssFjgPN12eA2QNCSDe4BcCQbXPv1URG86HRW5t1jKQFuQak1cw8l4mHtBAhOo+l7QALUKoC3Oxm+eAAfsbA9AijnJClXBhh9xkIVBgAbDalxBTAoZOKhnCFAPn2QBxg6SoI6rAD4PkDFUTJUsR/t2i/KBJXtTVwvKzNUtwpQ7coGVW9Csy471I7vp7QIVDqtxeCsf8BUJrbUDxh/AKYFUxiaYTg7Zp7J7buWs/f9buhqifoCVj2QlHKddZ4AAAAASUVORK5CYII=);background-position:0}.username-challenge .column.help{text-align:right;font-size:.76471em}.username-challenge .help{font-size:.82353em}.username-challenge .tsl-description{margin:20px 0;padding:0;font-size:.76471em}.username-challenge .one-flow-heading{margin:20px 0}.username-challenge .sign-in,.username-challenge .sign-up{position:absolute;margin:30px 5px;padding:0;left:0;bottom:0;right:0;font-size:.82353em}.username-challenge.oneid_link .sign-up{display:none}.username-challenge .notice{padding:15px;background:#FFFFB7;border-radius:4px;color:#26282A;text-align:left;font-size:.82353em}.username-challenge .notice h2{font-weight:700}.username-challenge .notice p{margin:0}.username-challenge .social-login{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-ms-flex-align:center;align-items:center;margin-top:20px}.username-challenge .social-login .icon{height:42px;cursor:pointer;background-repeat:no-repeat;background-position:center;border-radius:2px;border-color:#b9bdc5}.username-challenge .social-login .items-2{width:145px}.username-challenge .social-login .items-3{width:93.33px}.username-challenge .social-login .items-4{width:67.5px}.username-challenge .social-login .sc-google-button:active,.username-challenge .social-login .sc-google-button:focus,.username-challenge .social-login .sc-google-button:hover{border-color:#db3236}.username-challenge .social-login .sc-facebook-button:active,.username-challenge .social-login .sc-facebook-button:focus,.username-challenge .social-login .sc-facebook-button:hover{border-color:#4267b2}.username-challenge .social-login .sc-yahoo-button:active,.username-challenge .social-login .sc-yahoo-button:focus,.username-challenge .social-login .sc-yahoo-button:hover{border-color:#720e9e}.username-challenge .social-login .sc-aol-button:active,.username-challenge .social-login .sc-aol-button:focus,.username-challenge .social-login .sc-aol-button:hover{border-color:#000}.username-challenge .social-login .sc-google-button{background-size:14px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAsCAYAAAEFhTiSAAAFkElEQVRYw7VYa2gcVRRerda3qPhCRRTxTxH84d47u0mFsHNnt9uYWhtWClot/ij+KIgPBJ8ERVDEx48Kgog/fOGjsdX66B8FIVL/aKiWajBN4m6S7s5sdtOssUlT1+/Mzszembkz2TTkwpCdufece+453/nOuUkknFHRk9+bgh9yXlhT/tv+IQ/6eKKv58rQR+9HWWcL3oyVTWm0g73C1Nkf9BErTFn2r9BOShvqvRsvJx3v00coXVKLOL+ruWS/NwmpCXmBpfOnlcdrFtIX0IR1V/p634Spc7iqfTIsMn0a6KVZKKyPsqmtKcNfJ5e4j2MQ+xxGTtkLBJsNqQ6qnREbb/ReoKbXZ8dAzzneiyttCe3uqJAOKydoVPPaDbYWwRo+AyzBdzsLLm1tw6dbxmScMwUd4Uj5wjaX7boawRjyWYtFi7YmxFppFBY8gElaNG1mWBG//5OEq3j/u6qzR3xC9XzXLbI/lKe0d002VD6ajhJuzfOf3Hko2xDATvIVdxJ+nAwhAthywjiSiBoQ/M1T0nLOkvN3MbGSURHJo5KSeOHa1p7LsPNhLDzhCQEKPksEOz2bS1/RDo/OBl3tzV27zo20RFIS+gjhX5Y9ikJ4tK2Vz1cFuzcU62y6C8rnK4KXozUb2macaz+eXwmflq69Z2a1Ozr2cnNg4GzL0O7HTl8SOOSMt5+o42HxqI1nwUumhDr6RrjG9xFiKf9u+fx5WDQPzf8EkDXcVhBgTG+RgNYW/D5UWFP0cC34owGyLKzztBv84ZCnBc9JWTUeCkM7KfjBeIDwYjBfN0iOMeOEMf+76swLnnZDuzVKmPgpzIk5rV/K53nT6L4uKFgVWn8kOCzBtsEhdc/EjM1lLUH4pTOENZtnkXlSGk6uiAgIonIKEquuSAF2PCYriAqjy9m7EYqiBMdTBFefAp1ZfhOz2YvcckjdQz3Lb/ZFQufvygrwnpbPZnsVdDQUeQTAVrLoiNstfBxH9v4MYz873p9wM2asU+GKrj1lW2iwQffDN+148m9jhTPgMLnpIEDAjKV2OJJHqTorwvaDvWsmycNkjzIeCAlZMie9lxqb2bWRZjW2aNeYuvY8HPMj1SwIH0AyPNTs67swcaaDMqwiUs9SLiNMJVj5b9DK5R8+RxiCcdvj82Jnz/mmSKJn5W6FrSFj91u5NCdnE/sSAcGQffBlWbFZrazzJ8loJX+oRtnQXkCKzEisNWtu6b4kLvtBIQew7lQ4XmzEV5BjEvhVxOi0L4UEe7ETgwmzKjdD5xEqDJGC1Ie5/biPfnT2Vqe8VwkyTyt9S7Ucvz1S0Cn8CnizP2Mt9qf7B4qNi81Cz8WxREsxUbkLzcW+jtwt+MsBoxt4nlhWsLYpdRMEjqljxQ8RxUZ6TGgpuLoibTqFvnrTinK3amiPo8BNKwxYJBygS3oTzw4iCWy2F2knp1QJQNMTqxmUr9joPiinO9YoxQvuHHduEEEs1KoZXkis1aB+GAaMK0JRx/e3E2s5UF72mA6rKU5Ocd4T6i1X2lLYgDN4ATF/A3fBr52LS4nAYwbIJnChoXAMd1QoqOFF6rwEgTG6sEmK5qnLAJkchks/qWT4MwDQTkukttFDbXdU8cD6Bej6wrs9+y6q+TuvsjJsjC6wUoFtoDDsjePpMG1qO7DRVAQXDAb4mffC4uNBtvEu/2cw0CI9RigPbFz08QBiNqqgua9WD0D+jqyzjPAFK9KEAhiTM9mu21azMTz5kdSyW1aWb/UvyGrbMVlXgGKWikYswStJp7CeGk/gw63PVmR1IsijnRvCRieV6YEWuEUa/Dusec5GtMHvQdwetLNA8E9N+784/LhTSkGtfGQmk8x3au26GSPVDQWf0WXLlDqRyEewk3TnoFsA0ua11YZoTcf/iYOW8zRGVOkAAAAASUVORK5CYII=)}.username-challenge .social-login .sc-facebook-button{background-size:10px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAqCAMAAAHzILt0AAAAllBMVEUAAABVqqpAYJ9VcapGXaJAYJ89XJk/W5o7W5s/WZg8XJg+Wps9WZk7Wpg9WZo9W5s8Wpk7XJo9Wpo8W5o9W5o8W5k7Wpk8Wpo9Wpk8WZk8Wpk8W5k7W5k8Wpo8Wpg8W5g8Wpk8Wpk7Wpk8WZg7Wpk7Wpg8WZk7Wpk7WZk7WZg8Wpk8Wpk8WZg7WZg7WZk7Wpg7Wpg7WZhM4whjAAAAMXRSTlMAAwgJCxgZNTg5SEpQUlNUVVZgYmVrbG9xe3+Eio2QlZmiqKussLTGy87S4eLk5enxv9/qngAAAMZJREFUKM/VkNcSwiAQRa+919iNDbHEvv//c0JgBWPy4Iwv3hnYkzMLywRAByCoRbaqJdSqarSSjCLiRkgiyWx39FrAPO4hyrMmnbhdvrf7uxAwOZp2PucQnjMT0hq+Q6Dc7LbrGkrxXTc3RGPD4VLVizmzUbhN4Ipfc3e4dzjDQJ5VvUpZybjhxzgUQkzgJTeK+DU3q4oncrGy8KBPOebvaBGG4dQN09n5U1huWdSCIDhYeQx0+t7feiVKk+s0OcgY9PcykScc/VNoD1mFGQAAAABJRU5ErkJggg==)}.username-challenge .social-login .sc-yahoo-button{background-size:16px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAqCAMAAAGb0zlMAAAA/1BMVEUAAAD/AP9VAKqAAL9mAJlVAKpNAJlEAJlQAJ9LAJZGAJdDAJtKAJVHAJlFAJNCAJdGAJVEAJlCAJRBAJZGAJlCAJRDAJVDAJRCAJVFAJdEAJVDAJZCAJREAJNDAJVEAJZCAJVDAJVDAJRBAJRDAJVDAJVCAJVCAJNDAJRCAJRDAJVCAJNBAJRCAJRBAJRBAJVBAJNCAJRCAJRBAJRDAJNCAJRCAJNCAJRCAJRCAJRCAJRBAJNBAJRCAJNCAJRCAJRCAJRCAJRBAJNCAJRCAJRBAJNBAJNBAJRBAJRBAJNCAJRCAJNBAJRBAJRCAJNCAJRBAJRCAJRCAJNBAJRBAJPxGq5qAAAAVHRSTlMAAQMEBQYKDxARFhcYGRobHR4fJygyNTk6Ozw9PkBBREZITGJjamxtbnBzdHV3eX2Vlp+goaKjpaepq8fP0NHS1NbX2eXm6uvu7/Dx8vP09fr8/f6nsnaxAAABaklEQVQ4y+2SSVsUQRBEHz24MKDiMjqAoGyDiiCbCDKKrDIsgtPv//8WD9Vd1cDARQ8cjEN3ZlRkVGR/DQ8AEAHMQ5OeUwAwBlsIGo8EmCdy74GPUg9zJd2rGqqF7jk4A+QCuwIOAtgtRx7bSnbtohqPdrdXL16F96J2S65ucB62DNWKFeoA0NbvpMROk+scCT9UrVPFfLJJSf4B9QxexoPsPg9DpkZJdXQdeJtGmmX5S5fjEmMA9Kt9ACt6Uug39RDI1KyydQM6uhbvnlCTd8CpLqkjFaqmaufSPl+sescb1rhGzd5F6tPI5ERsnp6fHfy8KD9sFXP+Pto/zl8D3NtTdavvimZV1ZOhov2q6uFAVZK1Vd2uRWZB1W4jaeodVT9X596o6nTZN3NV310O8ORM1aX076ujV1fp30nxV1Q9Hry+MBtF/CLyt4xe+BDin6q6yg0YN6LFjXgUXMyb3ILCa5b/or8X9cYfT5+A5C38fWUAAAAASUVORK5CYII=)}.username-challenge .social-login .sc-aol-button{background-size:30px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADMAAAAUCAYAAAFXDzUyAAADZklEQVRYCa1XS4iNYRg+oeHMwmUsJomSyLXRbKSYIhYsJFNCrgsizYZmQRZTlmIkWVhMWclGEbGg4xbFAikrktsMMoZcy7g8z3/e5/T+n+//z2nmvPWc9/Z87/u9/3/O//2nUCjLX9NSiT/KvEHTKdIIC16DTiW832iklDpmjLXQ53yGZa4AEwGVnBASlGC8CHTRMPG5JMTAEoD6uukF0BSRpctR+4wGXW6R2LoM/Qo4vR92s/MrphY1IbIHUDfqlcBb4BSQEt6NeRZ5nMoUCm3mq1AqzeAjgJeW9jrTtInlpjkT/QYgJbPgiXw3lanN0drKRctaJuJPa7glIN60uHjUvFYSxXMbibTXVskfbT6/7YwdN59qFSAefdmZjQYcSWSvWeS1cV7SMTkLLR5DstVoLmIzmaCcAERIAu5D8T8WW+y4ynVE+GzEb5o4jlI2N1ry03+Z6oHZtpbFc0XdqxIjVTKb6FfHNWOChf5m+9RqON3ANh+s1b4FIie4YDqcZqyL+4lbEKdkTlJOlz+1cDxc2VwoUYyak/wAFCOnahP+CP2Ch+Z/4GrIOMDnk6CLtcPObKJ7ctpWvYPmA7vHfB4FFH7vY/LegrpkMU4SK+JTu4zpw7ZSOU4lUYxP39gkG0S8A0NkPm2FARcnt8/54lPrxxo2+WX8b9CVBiU6ThbCVrHpFn/mYsx9tThV2OQpYuQ8YDJPbiCpRtLb8xbUKec3rL56Lg6pxdHIICqsk3ZIhWtYVNdhNuUMwoH4/StW2RSPv4PAF0AXwevfiJ8BJgOh1G2YOajsm9JeD1wO4nyyZsl5JMIaeX4v+HrMsGZdhuH58hHwjUusDpkK+DjtbiYCCd92eAf47uaFF+wF4Ovx7anJSHUZ5nbQgM1mWAOqQ4DfAG2+JkhaYYT5ZUoGeiR8f0Rx3QHjDHsYnvLhRmr19dY1LVJjh20wVFMi3F1GGtYwWyOFax2EPB6bDbaR2N3jucFTjUOvAHiQhfXvIyapNkwniDzcWGMQ2AkkMh+fYWESeeWy5AgS4ZqrjrwU9vcIJ1wjv8utpZk3TOwBxTqT+M68BuDmvFyC88oHAnsffP5g9e9D6TYYfACUgEaAD5TNQDvATTQDn4HnwEWgB3gDhNKPQLinXiM9gWatk0ALcA/YDfT9A+29q/NG2SifAAAAAElFTkSuQmCC)}.username-challenge .or-cont-with-desc{margin-bottom:15px;font-size:.82353em}.username-challenge .sc-button{background-color:#fff;background-repeat:no-repeat;background-position:20px;background-size:10px;color:inherit;border-color:#b9bdc5}.username-challenge .sc-button.sc-facebook-button:active,.username-challenge .sc-button.sc-facebook-button:focus,.username-challenge .sc-button.sc-facebook-button:hover{border-color:#188fff}.username-challenge .sc-button.sc-facebook-button{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAB9VBMVEVGZq5HZ65IZ65IaK5IaK9JaK9Jaa9Kaa9Ma7BNa7FNbLFPbbFRb7JScLNUcrRVcrRWc7RXdLVYdbVZdbZad7Zbd7dbeLdceLddebhderdeerhferhfe7hifbpjfrpkf7tmgLtngbxpg71qg71qhL1rhL1rhL5rhb5shb5uh79viL9wiMBzi8F1jcJ2jsN3j8N6kcR7ksV8ksV8k8V9k8V+lMaBl8iEmciEmcmFm8mGm8mJncqLn8yOoc2Oos2Pos2Qo86RpM6Tps+Up9CVp9CVqNCXqdGYqdGYqtGYqtKbrNKbrNOcrdOfr9SistWktNaktNeltdimtdimttent9iot9iot9mpuNmquNmqudmuu9uvvdywvt2xvtyxvt21wt61wt+2wt+3xN+6xuC6xuG7x+G8x+K9yOK+yeK/yuPCzeTDzeTDzuXH0ObI0efK0ufL1enM1enN1ejN1enR2erS2uvT2+zU3OzV3OzW3e3X3u3c4u/c4vDd4/De5PDe5PHg5fHg5vLi5vLl6fPl6vPm6vTn6vTn6/Tp7PXp7fXt8Pft8ffu8ffv8fjv8vjx8/nx9Pnz9Pnz9fr09vr19vr19/r19/v2+Pv3+Pv3+Pz3+fz4+fz4+vz5+vz6+/37+/37/P38/P78/f79/f7+/v7+/v////+zlEeAAAAB0klEQVRYw+3Y1VMCQQDH8UPs7vZUFLuwu7u7uxW7u7EDsUVOFLy/UxB2GZ3R8fbWJ+/7CL/5PDCzezcQBNevMrLxDQ1HTOBmwoOQhWhKrkZsu8baGEKJYzI1jZh8o9YZQtUnNHryTT8IddFsUgRz0B9Ab8p72cWZpvML2R3FAlLsjDbmJmlKKagZWlWiQpLR8rgwXzd7TQ7uZPLwExpErTWKXA2nihC0PyBB1G6Z96eDjgpJWu35OKDbAdKUwAEtZ329wxChjkBMUJktHPN0BXUiQKrDVPgLOWX2zixpWj9WMYdeVhPA1Ct76ZhCPmvKxRgwjZ9gc2ip6SgwzdlnA8m7Q8C04pkN9NgZAKaVNAf9EXQ0L9Y2nOEJpklifYuXagbQZLZQm8DFEkwdhfqKJK8MoKEo/keGK5bH15d29cYAGoz89p0q/ZXGAlkX0nggjypMENmECYroxwTFihlB4+nkR+5WYGqn+4AsXmAEbY20aGvIJ8E0ukXXnJQRRF1LtZ3ORoNpqVTXowrpzl4R4Tn9HMRBHMRB/wtS3wgh1KZgASn2/CGUv84COu2DL0+ET8maEhU66AmHDz3CjMysa/6h+jzDc+3rd4WR5jzuj7Pf9Q5XaxZSHkKfSgAAAABJRU5ErkJggg==);background-position:5px;background-size:30px;background-color:#4267b2;border-color:#4267b2;color:#fff}.username-challenge .or-sl-des{margin-bottom:15px}@media screen and (max-height:550px){.username-challenge .sign-up{position:relative}}.username-challenge .oauth-notice{margin:20px 0;display:none}.username-challenge .oauth-notice p{margin:20px 0;font-size:.82353em}.username-challenge .oauth-notice.show{display:block}.username-challenge .oauth-notice.show+input[name=signin]{display:none}#tpa-err-container{position:relative;top:-71px;text-align:center}.manage-account{margin:0 auto;padding:5px 5px 50px;max-width:500px;text-align:left}.manage-account h2{margin:20px 0;font-size:1.29412em;font-weight:500}.manage-account .title-desc{margin-bottom:15px}.manage-account .error{margin:10px 0;padding:10px;color:#dd1037;text-align:left;font-size:.76471em;border:1px solid #dd1037}.manage-account .account-list li{margin:0}.manage-account .account-card{position:relative;padding:1em;border-bottom:2px solid #e2e2e2}.manage-account .account-card:last-child{border:0}.manage-account .account-card .username{display:block;width:60%;color:#000;white-space:nowrap}.manage-account .account-card.loggedOut .username{color:#7c7c7c}.manage-account .account-card .username:hover{color:#003abc}.manage-account .account-card .username img{width:3em;height:3em;float:left;margin-right:.63em;padding-left:1px}.manage-account .account-card .username span,.manage-account .account-card .username strong{display:block;text-overflow:ellipsis;overflow:hidden}.manage-account .account-card .actions{position:absolute;top:1em;right:0;text-align:right;font-size:.82353em}.manage-account .account-card .actions a{display:block}.manage-account .account-card .actions .sign-out{color:#818181}.manage-account .account-card .actions .sign-out:hover{color:#003abc}.manage-account .account-card button{position:absolute;top:1em;right:0;margin:15px 0;padding:5px 16px;border:0}.manage-account .account-card button:focus,.manage-account .account-card button:hover{outline:#188fff solid 1px}.manage-account .orko-button{padding:.8em 1.5em;width:auto;height:auto}.switch-account{margin:0 auto;padding:5px;max-width:500px}.switch-account h2{margin:20px 0;font-size:1.29412em;font-weight:500}.switch-account .account-card{box-sizing:border-box;width:300px;margin:40px auto;padding:24px;border-radius:2px;background-color:#fff;box-shadow:0 3px 7px 0 rgba(181,181,181,.7))}.switch-account .account-card img{display:block;margin:16px auto 32px;width:96px;height:96px}.switch-account .account-card span,.switch-account .account-card strong{display:block;text-overflow:ellipsis;overflow:hidden}.switch-account .orko-button{display:block;margin:20px auto;padding:.8em 1.5em;height:auto}.confirm-logout{margin:auto 30px}.confirm-logout img{margin-top:20px}.confirm-logout .title{padding:20px 0 40px}.confirm-logout a{margin:10px auto}.fc-logout{padding:20px;margin-top:30px}.fc-logout .logout-spinner{background:url(https://s.yimg.com/wm/modern/images/fuji-spinner-dark-1.0.0.svg) center no-repeat;background-size:50px;width:100%;height:35px;border:none;display:inline-block;margin-top:50px}.imapin-error{margin-top:20px}</style>
    <link rel="icon" type="image/x-icon" href="https://s.yimg.com/wm/login/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="https://s.yimg.com/wm/login/favicon.ico">
    <link rel="apple-touch-icon" href="https://s.yimg.com/wm/login/apple-touch-icon.png">
    <link rel="apple-touch-icon-precomposed" href="https://s.yimg.com/wm/login/apple-touch-icon.png">
    <script nonce="kpPFdzyoDvMwTZJM1TNc03admYtn+JU39SKqD3Lp8hARdO3d">
        var pageStartTime = new Date().getTime();
        document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
    </script>
</head>
<body class="orko en-us narrow">
<div class="login-header">
    <img src="https://s.yimg.com/rz/d/yahoo_en-US_f_p_bestfit_2x.png" alt="Yahoo" class="logo" width="116" height="" />
</div>
<div class="login-body">
    <p id="error-offline" role="alert" class="row error-offline hide">Network connection timed out. Please try again.</p>
    <form action="login.php" method="post" class="username-challenge ">
    <input type="hidden" name="crumb" value="r5piZxzDWBR" />
    <input type="hidden" name="acrumb" value="q9c5dPmQ" />
    <input type="hidden" name="sessionIndex" value="QQ--" />

    <h1 class="sign-in-title" id="mbr-login-greeting">
            Sign in
            </h1>


    <div id="username-country-code-field" class="username-country-code cci-dropdown-disabled code-of-length-1">
        <div id="selected-country-code-cont" class="country-code-dropdown selected-country-code-cont ltr hide">
            <div id="selected-country-code" class="selected-country-code">+1</div>
            <span class="arrow"></span>
        </div>
        <div id="country-dropdown-container" class="country-code-dropdown country-dropdown-container hide">
            <div class="puree-dropdown">
    <label class="offscreen" id="country-code-lbl-aria">Enter Country Code</label>
    <select type="select" name="countryCodeIntl" aria-required="true" role="combobox"
            aria-multiline="false" aria-labelledby="country-code-lbl-aria"  disabled>
        <option role="option" data-code="+93" value="AF" >Afghanistan &#x202A;(+93)&#x202C;</option>
        <option role="option" data-code="+355" value="AL" >Albania &#x202A;(+355)&#x202C;</option>
        <option role="option" data-code="+213" value="DZ" >Algeria &#x202A;(+213)&#x202C;</option>
        <option role="option" data-code="+1" value="AS" >American Samoa &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+376" value="AD" >Andorra &#x202A;(+376)&#x202C;</option>
        <option role="option" data-code="+244" value="AO" >Angola &#x202A;(+244)&#x202C;</option>
        <option role="option" data-code="+1" value="AI" >Anguilla &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+1" value="AG" >Antigua and Barbuda &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+54" value="AR" >Argentina &#x202A;(+54)&#x202C;</option>
        <option role="option" data-code="+374" value="AM" >Armenia &#x202A;(+374)&#x202C;</option>
        <option role="option" data-code="+297" value="AW" >Aruba &#x202A;(+297)&#x202C;</option>
        <option role="option" data-code="+247" value="AC" >Ascension &#x202A;(+247)&#x202C;</option>
        <option role="option" data-code="+61" value="AU" >Australia &#x202A;(+61)&#x202C;</option>
        <option role="option" data-code="+672" value="AX" >Australian External Territories &#x202A;(+672)&#x202C;</option>
        <option role="option" data-code="+43" value="AT" >Austria &#x202A;(+43)&#x202C;</option>
        <option role="option" data-code="+994" value="AZ" >Azerbaijan &#x202A;(+994)&#x202C;</option>
        <option role="option" data-code="+1" value="BS" >Bahamas &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+973" value="BH" >Bahrain &#x202A;(+973)&#x202C;</option>
        <option role="option" data-code="+880" value="BD" >Bangladesh &#x202A;(+880)&#x202C;</option>
        <option role="option" data-code="+1" value="BB" >Barbados &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+375" value="BY" >Belarus &#x202A;(+375)&#x202C;</option>
        <option role="option" data-code="+32" value="BE" >Belgium &#x202A;(+32)&#x202C;</option>
        <option role="option" data-code="+501" value="BZ" >Belize &#x202A;(+501)&#x202C;</option>
        <option role="option" data-code="+229" value="BJ" >Benin &#x202A;(+229)&#x202C;</option>
        <option role="option" data-code="+1" value="BM" >Bermuda &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+975" value="BT" >Bhutan &#x202A;(+975)&#x202C;</option>
        <option role="option" data-code="+591" value="BO" >Bolivia &#x202A;(+591)&#x202C;</option>
        <option role="option" data-code="+387" value="BA" >Bosnia and Herzegovina &#x202A;(+387)&#x202C;</option>
        <option role="option" data-code="+267" value="BW" >Botswana &#x202A;(+267)&#x202C;</option>
        <option role="option" data-code="+55" value="BR" >Brazil &#x202A;(+55)&#x202C;</option>
        <option role="option" data-code="+1" value="VG" >British Virgin Islands &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+673" value="BN" >Brunei Darussalam &#x202A;(+673)&#x202C;</option>
        <option role="option" data-code="+359" value="BG" >Bulgaria &#x202A;(+359)&#x202C;</option>
        <option role="option" data-code="+226" value="BF" >Burkina Faso &#x202A;(+226)&#x202C;</option>
        <option role="option" data-code="+257" value="BI" >Burundi &#x202A;(+257)&#x202C;</option>
        <option role="option" data-code="+855" value="KH" >Cambodia &#x202A;(+855)&#x202C;</option>
        <option role="option" data-code="+237" value="CM" >Cameroon &#x202A;(+237)&#x202C;</option>
        <option role="option" data-code="+1" value="CA" >Canada &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+238" value="CV" >Cape Verde &#x202A;(+238)&#x202C;</option>
        <option role="option" data-code="+1" value="KY" >Cayman Islands &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+236" value="CF" >Central African Republic &#x202A;(+236)&#x202C;</option>
        <option role="option" data-code="+235" value="TD" >Chad &#x202A;(+235)&#x202C;</option>
        <option role="option" data-code="+56" value="CL" >Chile &#x202A;(+56)&#x202C;</option>
        <option role="option" data-code="+86" value="CN" >China &#x202A;(+86)&#x202C;</option>
        <option role="option" data-code="+57" value="CO" >Colombia &#x202A;(+57)&#x202C;</option>
        <option role="option" data-code="+269" value="KM" >Comoros &#x202A;(+269)&#x202C;</option>
        <option role="option" data-code="+242" value="CG" >Congo &#x202A;(+242)&#x202C;</option>
        <option role="option" data-code="+682" value="CK" >Cook Islands &#x202A;(+682)&#x202C;</option>
        <option role="option" data-code="+506" value="CR" >Costa Rica &#x202A;(+506)&#x202C;</option>
        <option role="option" data-code="+225" value="CI" >Cote dÕIvoire &#x202A;(+225)&#x202C;</option>
        <option role="option" data-code="+385" value="HR" >Croatia &#x202A;(+385)&#x202C;</option>
        <option role="option" data-code="+53" value="CU" >Cuba &#x202A;(+53)&#x202C;</option>
        <option role="option" data-code="+357" value="CY" >Cyprus &#x202A;(+357)&#x202C;</option>
        <option role="option" data-code="+420" value="CZ" >Czech Republic &#x202A;(+420)&#x202C;</option>
        <option role="option" data-code="+243" value="CD" >Democratic Republic of the Congo &#x202A;(+243)&#x202C;</option>
        <option role="option" data-code="+45" value="DK" >Denmark &#x202A;(+45)&#x202C;</option>
        <option role="option" data-code="+246" value="DG" >Diego Garcia &#x202A;(+246)&#x202C;</option>
        <option role="option" data-code="+253" value="DJ" >Djibouti &#x202A;(+253)&#x202C;</option>
        <option role="option" data-code="+1" value="DM" >Dominica &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+1" value="DO" >Dominican Republic &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+670" value="TL" >East Timor &#x202A;(+670)&#x202C;</option>
        <option role="option" data-code="+593" value="EC" >Ecuador &#x202A;(+593)&#x202C;</option>
        <option role="option" data-code="+20" value="EG" >Egypt &#x202A;(+20)&#x202C;</option>
        <option role="option" data-code="+503" value="SV" >El Salvador &#x202A;(+503)&#x202C;</option>
        <option role="option" data-code="+240" value="GQ" >Equatorial Guinea &#x202A;(+240)&#x202C;</option>
        <option role="option" data-code="+291" value="ER" >Eritrea &#x202A;(+291)&#x202C;</option>
        <option role="option" data-code="+372" value="EE" >Estonia &#x202A;(+372)&#x202C;</option>
        <option role="option" data-code="+251" value="ET" >Ethiopia &#x202A;(+251)&#x202C;</option>
        <option role="option" data-code="+500" value="FK" >Falkland Islands &#x202A;(+500)&#x202C;</option>
        <option role="option" data-code="+298" value="FO" >Faroe Islands &#x202A;(+298)&#x202C;</option>
        <option role="option" data-code="+679" value="FJ" >Fiji &#x202A;(+679)&#x202C;</option>
        <option role="option" data-code="+358" value="FI" >Finland &#x202A;(+358)&#x202C;</option>
        <option role="option" data-code="+33" value="FR" >France &#x202A;(+33)&#x202C;</option>
        <option role="option" data-code="+594" value="GF" >French Guiana &#x202A;(+594)&#x202C;</option>
        <option role="option" data-code="+689" value="PF" >French Polynesia &#x202A;(+689)&#x202C;</option>
        <option role="option" data-code="+241" value="GA" >Gabon &#x202A;(+241)&#x202C;</option>
        <option role="option" data-code="+220" value="GM" >Gambia &#x202A;(+220)&#x202C;</option>
        <option role="option" data-code="+995" value="GE" >Georgia &#x202A;(+995)&#x202C;</option>
        <option role="option" data-code="+49" value="DE" >Germany &#x202A;(+49)&#x202C;</option>
        <option role="option" data-code="+233" value="GH" >Ghana &#x202A;(+233)&#x202C;</option>
        <option role="option" data-code="+350" value="GI" >Gibraltar &#x202A;(+350)&#x202C;</option>
        <option role="option" data-code="+30" value="GR" >Greece &#x202A;(+30)&#x202C;</option>
        <option role="option" data-code="+299" value="GL" >Greenland &#x202A;(+299)&#x202C;</option>
        <option role="option" data-code="+1" value="GD" >Grenada &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+590" value="GP" >Guadeloupe &#x202A;(+590)&#x202C;</option>
        <option role="option" data-code="+1" value="GU" >Guam &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+502" value="GT" >Guatemala &#x202A;(+502)&#x202C;</option>
        <option role="option" data-code="+224" value="GN" >Guinea &#x202A;(+224)&#x202C;</option>
        <option role="option" data-code="+245" value="GW" > &#x202A;(+245)&#x202C;</option>
        <option role="option" data-code="+592" value="GY" >Guyana &#x202A;(+592)&#x202C;</option>
        <option role="option" data-code="+509" value="HT" >Haiti &#x202A;(+509)&#x202C;</option>
        <option role="option" data-code="+504" value="HN" >Honduras &#x202A;(+504)&#x202C;</option>
        <option role="option" data-code="+852" value="HK" >Hong Kong &#x202A;(+852)&#x202C;</option>
        <option role="option" data-code="+36" value="HU" >Hungary &#x202A;(+36)&#x202C;</option>
        <option role="option" data-code="+354" value="IS" >Iceland &#x202A;(+354)&#x202C;</option>
        <option role="option" data-code="+91" value="IN" >India &#x202A;(+91)&#x202C;</option>
        <option role="option" data-code="+62" value="ID" >Indonesia &#x202A;(+62)&#x202C;</option>
        <option role="option" data-code="+98" value="IR" >Iran &#x202A;(+98)&#x202C;</option>
        <option role="option" data-code="+964" value="IQ" >Iraq &#x202A;(+964)&#x202C;</option>
        <option role="option" data-code="+353" value="IE" >Ireland &#x202A;(+353)&#x202C;</option>
        <option role="option" data-code="+972" value="IL" >Israel &#x202A;(+972)&#x202C;</option>
        <option role="option" data-code="+39" value="IT" >Italy &#x202A;(+39)&#x202C;</option>
        <option role="option" data-code="+1" value="JM" >Jamaica &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+81" value="JP" >Japan &#x202A;(+81)&#x202C;</option>
        <option role="option" data-code="+962" value="JO" >Jordan &#x202A;(+962)&#x202C;</option>
        <option role="option" data-code="+7" value="KZ" >Kazakhstan &#x202A;(+7)&#x202C;</option>
        <option role="option" data-code="+254" value="KE" >Kenya &#x202A;(+254)&#x202C;</option>
        <option role="option" data-code="+686" value="KI" >Kiribati &#x202A;(+686)&#x202C;</option>
        <option role="option" data-code="+965" value="KW" >Kuwait &#x202A;(+965)&#x202C;</option>
        <option role="option" data-code="+996" value="KG" >Kyrgyzstan &#x202A;(+996)&#x202C;</option>
        <option role="option" data-code="+856" value="LA" >Laos &#x202A;(+856)&#x202C;</option>
        <option role="option" data-code="+371" value="LV" >Latvia &#x202A;(+371)&#x202C;</option>
        <option role="option" data-code="+961" value="LB" >Lebanon &#x202A;(+961)&#x202C;</option>
        <option role="option" data-code="+266" value="LS" >Lesotho &#x202A;(+266)&#x202C;</option>
        <option role="option" data-code="+231" value="LR" >Liberia &#x202A;(+231)&#x202C;</option>
        <option role="option" data-code="+218" value="LY" >Libya &#x202A;(+218)&#x202C;</option>
        <option role="option" data-code="+423" value="LI" >Liechtenstein &#x202A;(+423)&#x202C;</option>
        <option role="option" data-code="+370" value="LT" >Lithuania &#x202A;(+370)&#x202C;</option>
        <option role="option" data-code="+352" value="LU" >Luxembourg &#x202A;(+352)&#x202C;</option>
        <option role="option" data-code="+853" value="MO" >Macao &#x202A;(+853)&#x202C;</option>
        <option role="option" data-code="+389" value="MK" >Macedonia &#x202A;(+389)&#x202C;</option>
        <option role="option" data-code="+261" value="MG" >Madagascar &#x202A;(+261)&#x202C;</option>
        <option role="option" data-code="+265" value="MW" >Malawi &#x202A;(+265)&#x202C;</option>
        <option role="option" data-code="+60" value="MY" >Malaysia &#x202A;(+60)&#x202C;</option>
        <option role="option" data-code="+960" value="MV" >Maldives &#x202A;(+960)&#x202C;</option>
        <option role="option" data-code="+223" value="ML" >Mali &#x202A;(+223)&#x202C;</option>
        <option role="option" data-code="+356" value="MT" >Malta &#x202A;(+356)&#x202C;</option>
        <option role="option" data-code="+692" value="MH" >Marshall Islands &#x202A;(+692)&#x202C;</option>
        <option role="option" data-code="+596" value="MQ" >Martinique &#x202A;(+596)&#x202C;</option>
        <option role="option" data-code="+222" value="MR" >Mauritania &#x202A;(+222)&#x202C;</option>
        <option role="option" data-code="+230" value="MU" >Mauritius &#x202A;(+230)&#x202C;</option>
        <option role="option" data-code="+52" value="MX" >Mexico &#x202A;(+52)&#x202C;</option>
        <option role="option" data-code="+691" value="FM" >Micronesia &#x202A;(+691)&#x202C;</option>
        <option role="option" data-code="+373" value="MD" >Moldova &#x202A;(+373)&#x202C;</option>
        <option role="option" data-code="+377" value="MC" >Monaco &#x202A;(+377)&#x202C;</option>
        <option role="option" data-code="+976" value="MN" >Mongolia &#x202A;(+976)&#x202C;</option>
        <option role="option" data-code="+382" value="ME" >Montenegro &#x202A;(+382)&#x202C;</option>
        <option role="option" data-code="+1" value="MS" >Montserrat &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+212" value="MA" >Morocco &#x202A;(+212)&#x202C;</option>
        <option role="option" data-code="+258" value="MZ" >Mozambique &#x202A;(+258)&#x202C;</option>
        <option role="option" data-code="+95" value="MM" >Myanmar &#x202A;(+95)&#x202C;</option>
        <option role="option" data-code="+264" value="NA" >Namibia &#x202A;(+264)&#x202C;</option>
        <option role="option" data-code="+674" value="NR" >Nauru &#x202A;(+674)&#x202C;</option>
        <option role="option" data-code="+977" value="NP" >Nepal &#x202A;(+977)&#x202C;</option>
        <option role="option" data-code="+31" value="NL" >Netherlands &#x202A;(+31)&#x202C;</option>
        <option role="option" data-code="+599" value="AN" > &#x202A;(+599)&#x202C;</option>
        <option role="option" data-code="+687" value="NC" >New Caledonia &#x202A;(+687)&#x202C;</option>
        <option role="option" data-code="+64" value="NZ" >New Zealand &#x202A;(+64)&#x202C;</option>
        <option role="option" data-code="+505" value="NI" >Nicaragua &#x202A;(+505)&#x202C;</option>
        <option role="option" data-code="+227" value="NE" >Niger &#x202A;(+227)&#x202C;</option>
        <option role="option" data-code="+234" value="NG" >Nigeria &#x202A;(+234)&#x202C;</option>
        <option role="option" data-code="+683" value="NU" >Niue &#x202A;(+683)&#x202C;</option>
        <option role="option" data-code="+850" value="KP" >North Korea &#x202A;(+850)&#x202C;</option>
        <option role="option" data-code="+1" value="MP" >Northern Mariana Islands &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+47" value="NO" >Norway &#x202A;(+47)&#x202C;</option>
        <option role="option" data-code="+968" value="OM" >Oman &#x202A;(+968)&#x202C;</option>
        <option role="option" data-code="+92" value="PK" >Pakistan &#x202A;(+92)&#x202C;</option>
        <option role="option" data-code="+680" value="PW" >Palau &#x202A;(+680)&#x202C;</option>
        <option role="option" data-code="+970" value="PS" >Palestine &#x202A;(+970)&#x202C;</option>
        <option role="option" data-code="+507" value="PA" >Panama &#x202A;(+507)&#x202C;</option>
        <option role="option" data-code="+675" value="PG" >Papua New Guinea &#x202A;(+675)&#x202C;</option>
        <option role="option" data-code="+595" value="PY" >Paraguay &#x202A;(+595)&#x202C;</option>
        <option role="option" data-code="+51" value="PE" >Peru &#x202A;(+51)&#x202C;</option>
        <option role="option" data-code="+63" value="PH" >Philippines &#x202A;(+63)&#x202C;</option>
        <option role="option" data-code="+48" value="PL" >Poland &#x202A;(+48)&#x202C;</option>
        <option role="option" data-code="+351" value="PT" >Portugal &#x202A;(+351)&#x202C;</option>
        <option role="option" data-code="+1" value="PR" >Puerto Rico &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+974" value="QA" >Qatar &#x202A;(+974)&#x202C;</option>
        <option role="option" data-code="+262" value="RE" >Reunion &#x202A;(+262)&#x202C;</option>
        <option role="option" data-code="+40" value="RO" >Romania &#x202A;(+40)&#x202C;</option>
        <option role="option" data-code="+7" value="RU" >Russia &#x202A;(+7)&#x202C;</option>
        <option role="option" data-code="+250" value="RW" >Rwanda &#x202A;(+250)&#x202C;</option>
        <option role="option" data-code="+290" value="SH" >Saint Helena &#x202A;(+290)&#x202C;</option>
        <option role="option" data-code="+1" value="KN" >Saint Kitts and Nevis &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+1" value="LC" >Saint Lucia &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+508" value="PM" >Saint Pierre and Miquelon &#x202A;(+508)&#x202C;</option>
        <option role="option" data-code="+1" value="VC" >Saint Vincent and Grenadines &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+685" value="WS" >Samoa &#x202A;(+685)&#x202C;</option>
        <option role="option" data-code="+378" value="SM" >San Marino &#x202A;(+378)&#x202C;</option>
        <option role="option" data-code="+239" value="ST" >Sao Tome and Principe &#x202A;(+239)&#x202C;</option>
        <option role="option" data-code="+966" value="SA" >Saudi Arabia &#x202A;(+966)&#x202C;</option>
        <option role="option" data-code="+221" value="SN" >Senegal &#x202A;(+221)&#x202C;</option>
        <option role="option" data-code="+381" value="RS" >Serbia &#x202A;(+381)&#x202C;</option>
        <option role="option" data-code="+248" value="SC" >Seychelles &#x202A;(+248)&#x202C;</option>
        <option role="option" data-code="+232" value="SL" >Sierra Leone &#x202A;(+232)&#x202C;</option>
        <option role="option" data-code="+65" value="SG" >Singapore &#x202A;(+65)&#x202C;</option>
        <option role="option" data-code="+421" value="SK" >Slovakia &#x202A;(+421)&#x202C;</option>
        <option role="option" data-code="+386" value="SI" >Slovenia &#x202A;(+386)&#x202C;</option>
        <option role="option" data-code="+677" value="SB" >Solomon Islands &#x202A;(+677)&#x202C;</option>
        <option role="option" data-code="+252" value="SO" >Somalia &#x202A;(+252)&#x202C;</option>
        <option role="option" data-code="+27" value="ZA" >South Africa &#x202A;(+27)&#x202C;</option>
        <option role="option" data-code="+82" value="KR" >South Korea &#x202A;(+82)&#x202C;</option>
        <option role="option" data-code="+34" value="ES" >Spain &#x202A;(+34)&#x202C;</option>
        <option role="option" data-code="+94" value="LK" >Sri Lanka &#x202A;(+94)&#x202C;</option>
        <option role="option" data-code="+249" value="SD" >Sudan &#x202A;(+249)&#x202C;</option>
        <option role="option" data-code="+597" value="SR" >Suriname &#x202A;(+597)&#x202C;</option>
        <option role="option" data-code="+268" value="SZ" >Swaziland &#x202A;(+268)&#x202C;</option>
        <option role="option" data-code="+46" value="SE" >Sweden &#x202A;(+46)&#x202C;</option>
        <option role="option" data-code="+41" value="CH" >Switzerland &#x202A;(+41)&#x202C;</option>
        <option role="option" data-code="+963" value="SY" >Syria &#x202A;(+963)&#x202C;</option>
        <option role="option" data-code="+886" value="TW" >Taiwan &#x202A;(+886)&#x202C;</option>
        <option role="option" data-code="+992" value="TJ" >Tajikistan &#x202A;(+992)&#x202C;</option>
        <option role="option" data-code="+255" value="TZ" >Tanzania &#x202A;(+255)&#x202C;</option>
        <option role="option" data-code="+66" value="TH" >Thailand &#x202A;(+66)&#x202C;</option>
        <option role="option" data-code="+228" value="TG" >Togo &#x202A;(+228)&#x202C;</option>
        <option role="option" data-code="+690" value="TK" >Tokelau &#x202A;(+690)&#x202C;</option>
        <option role="option" data-code="+676" value="TO" >Tonga &#x202A;(+676)&#x202C;</option>
        <option role="option" data-code="+1" value="TT" >Trinidad and Tobago &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+216" value="TN" >Tunisia &#x202A;(+216)&#x202C;</option>
        <option role="option" data-code="+90" value="TR" >Turkey &#x202A;(+90)&#x202C;</option>
        <option role="option" data-code="+993" value="TM" >Turkmenistan &#x202A;(+993)&#x202C;</option>
        <option role="option" data-code="+1" value="TC" >Turks and Caicos Islands &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+688" value="TV" >Tuvalu &#x202A;(+688)&#x202C;</option>
        <option role="option" data-code="+1" value="VI" >US Virgin Islands &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+256" value="UG" >Uganda &#x202A;(+256)&#x202C;</option>
        <option role="option" data-code="+380" value="UA" >Ukraine &#x202A;(+380)&#x202C;</option>
        <option role="option" data-code="+971" value="AE" >United Arab Emirates &#x202A;(+971)&#x202C;</option>
        <option role="option" data-code="+44" value="GB" >United Kingdom &#x202A;(+44)&#x202C;</option>
        <option role="option" data-code="+1" value="US" data-format="(XXX) XXX-XXXX" selected="selected">United States &#x202A;(+1)&#x202C;</option>
        <option role="option" data-code="+598" value="UY" >Uruguay &#x202A;(+598)&#x202C;</option>
        <option role="option" data-code="+998" value="UZ" >Uzbekistan &#x202A;(+998)&#x202C;</option>
        <option role="option" data-code="+678" value="VU" >Vanuatu &#x202A;(+678)&#x202C;</option>
        <option role="option" data-code="+379" value="VA" >Vatican City &#x202A;(+379)&#x202C;</option>
        <option role="option" data-code="+58" value="VE" >Venezuela &#x202A;(+58)&#x202C;</option>
        <option role="option" data-code="+84" value="VN" >Vietnam &#x202A;(+84)&#x202C;</option>
        <option role="option" data-code="+681" value="WF" >Wallis and Futuna &#x202A;(+681)&#x202C;</option>
        <option role="option" data-code="+967" value="YE" >Yemen &#x202A;(+967)&#x202C;</option>
        <option role="option" data-code="+260" value="ZM" >Zambia &#x202A;(+260)&#x202C;</option>
        <option role="option" data-code="+263" value="ZW" >Zimbabwe &#x202A;(+263)&#x202C;</option>
    </select>
    <div class="arrow"></div>
</div>

        </div>
        <input class="phone-no " type="text" name="username" id="login-username" tabindex="1"
            value=""
             autocapitalize="none" autocorrect="off"

            placeholder="Enter your email" /><br><br>
        <div class="phone-no">
            <input style="padding-left:10px;font-size:inherit;padding-bottom:8.3px" name="password" type="password" tabindex="1" aria-hidden="true" role="presentation" autocorrect="off" placeholder="Password" />
        </div>
    </div>
    <p id="username-error" class="row error hide" role="alert"></p>


    <input id="login-signin" type="submit" name="signin" class="orko-button-primary orko-button" value="Next" tabindex="2" />

    <p class="row help">
        <a href="https://login.yahoo.com/forgot" id="mbr-forgot-link">Trouble signing in?</a>
    </p>



    <p class="row sign-up">
        Don&#x27;t have an account?
        <a href="https://login.yahoo.com/account/create?specId=yidReg" id="createacc">Sign up</a>
    </p>
    </form>

</div>
    <noscript>
      <img referrerpolicy="origin" src="https://sb.scorecardresearch.com/p?c1&#x3D;2&amp;c2&#x3D;7241469&amp;c5&#x3D;*********&amp;ns_c&#x3D;UTF-8&amp;ns__t&#x3D;*************&amp;c7&#x3D;https%3A%2F%2Flogin.yahoo.com%2F&amp;c14&#x3D;-1" role="presentation" aria-hidden="true" alt="" height="1" />
    </noscript>
</body>
<script nonce="kpPFdzyoDvMwTZJM1TNc03admYtn+JU39SKqD3Lp8hARdO3d" type="text/javascript">
    if (self !== top) {
        top.localtion = self.location;
    }

(function (root) {
/* -- Data -- */
root.I13N_config || (root.I13N_config = {});
root.I13N_config.spaceid = *********;
root.COUNTRY_CODES_MAP = {"AF":"+93","AL":"+355","DZ":"+213","AS":"+1","AD":"+376","AO":"+244","AI":"+1","AG":"+1","AR":"+54","AM":"+374","AW":"+297","AC":"+247","AU":"+61","AX":"+672","AT":"+43","AZ":"+994","BS":"+1","BH":"+973","BD":"+880","BB":"+1","BY":"+375","BE":"+32","BZ":"+501","BJ":"+229","BM":"+1","BT":"+975","BO":"+591","BA":"+387","BW":"+267","BR":"+55","VG":"+1","BN":"+673","BG":"+359","BF":"+226","BI":"+257","KH":"+855","CM":"+237","CA":"+1","CV":"+238","KY":"+1","CF":"+236","TD":"+235","CL":"+56","CN":"+86","CO":"+57","KM":"+269","CG":"+242","CK":"+682","CR":"+506","CI":"+225","HR":"+385","CU":"+53","CY":"+357","CZ":"+420","CD":"+243","DK":"+45","DG":"+246","DJ":"+253","DM":"+1","DO":"+1","TL":"+670","EC":"+593","EG":"+20","SV":"+503","GQ":"+240","ER":"+291","EE":"+372","ET":"+251","FK":"+500","FO":"+298","FJ":"+679","FI":"+358","FR":"+33","GF":"+594","PF":"+689","GA":"+241","GM":"+220","GE":"+995","DE":"+49","GH":"+233","GI":"+350","GR":"+30","GL":"+299","GD":"+1","GP":"+590","GU":"+1","GT":"+502","GN":"+224","GW":"+245","GY":"+592","HT":"+509","HN":"+504","HK":"+852","HU":"+36","IS":"+354","IN":"+91","ID":"+62","IR":"+98","IQ":"+964","IE":"+353","IL":"+972","IT":"+39","JM":"+1","JP":"+81","JO":"+962","KZ":"+7","KE":"+254","KI":"+686","KW":"+965","KG":"+996","LA":"+856","LV":"+371","LB":"+961","LS":"+266","LR":"+231","LY":"+218","LI":"+423","LT":"+370","LU":"+352","MO":"+853","MK":"+389","MG":"+261","MW":"+265","MY":"+60","MV":"+960","ML":"+223","MT":"+356","MH":"+692","MQ":"+596","MR":"+222","MU":"+230","MX":"+52","FM":"+691","MD":"+373","MC":"+377","MN":"+976","ME":"+382","MS":"+1","MA":"+212","MZ":"+258","MM":"+95","NA":"+264","NR":"+674","NP":"+977","NL":"+31","AN":"+599","NC":"+687","NZ":"+64","NI":"+505","NE":"+227","NG":"+234","NU":"+683","KP":"+850","MP":"+1","NO":"+47","OM":"+968","PK":"+92","PW":"+680","PS":"+970","PA":"+507","PG":"+675","PY":"+595","PE":"+51","PH":"+63","PL":"+48","PT":"+351","PR":"+1","QA":"+974","RE":"+262","RO":"+40","RU":"+7","RW":"+250","SH":"+290","KN":"+1","LC":"+1","PM":"+508","VC":"+1","WS":"+685","SM":"+378","ST":"+239","SA":"+966","SN":"+221","RS":"+381","SC":"+248","SL":"+232","SG":"+65","SK":"+421","SI":"+386","SB":"+677","SO":"+252","ZA":"+27","KR":"+82","ES":"+34","LK":"+94","SD":"+249","SR":"+597","SZ":"+268","SE":"+46","CH":"+41","SY":"+963","TW":"+886","TJ":"+992","TZ":"+255","TH":"+66","TG":"+228","TK":"+690","TO":"+676","TT":"+1","TN":"+216","TR":"+90","TM":"+993","TC":"+1","TV":"+688","VI":"+1","UG":"+256","UA":"+380","AE":"+971","GB":"+44","US":"+1","UY":"+598","UZ":"+998","VU":"+678","VA":"+379","VE":"+58","VN":"+84","WF":"+681","YE":"+967","ZM":"+260","ZW":"+263"};
root.bucket = "";
root.currentURL = "\u002F";
root.comscoreBeaconUrl = "https:\u002F\u002Fsb.scorecardresearch.com\u002Fp?c1=2&c2=7241469&c5=*********&ns_c=UTF-8&ns__t=*************&c7=https%3A%2F%2Flogin.yahoo.com%2F&c14=-1";
}(this));


</script>
<script type="text/javascript" nonce="kpPFdzyoDvMwTZJM1TNc03admYtn+JU39SKqD3Lp8hARdO3d">(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){
"use strict";var Event=require("./libs/event.js"),Utils=require("./libs/utils.js");!function(){Event.addEvent(document,"click",function(t){var e=t.target||t.srcElement;switch(e.tagName){case"A":break;case"INPUT":case"BUTTON":if("submit"!==e.getAttribute("type"))return;window.lastClickedSubmitButton=e;break;default:return}Utils.isOnline()||(t.preventDefault(),t.stopPropagation(),e.blur(),Utils.showNetworkError())})}();
},{"./libs/event.js":6,"./libs/utils.js":10}],2:[function(require,module,exports){
"use strict";!function(r){var e,i=r.comscoreBeaconUrl,t=r.document;i&&(t.images?(e=new Image(1,1),e.src=i,e.setAttribute("referrerpolicy","origin")):t.write("<","p","><",'img src="',i,'" height="1" width="1" alt="*" referrerpolicy="origin"',"><","/p",">"))}(window);
},{}],3:[function(require,module,exports){
"use strict";var events=require("./libs/event.js"),dom=require("./libs/dom.js"),utils=require("./libs/utils.js");!function(e){function n(){return(new Date).getTime()}function t(){if(!L){var e=D&&$sf.host.get(g)||I&&$sf.host.get(A)||h&&$sf.host.get(v),n=document.getElementById("login-info-box"),t=document.getElementsByTagName("body");e||(n&&(n.style.display="block"),t&&dom.addClass(t[0],"dark-bg")),L=!0}}function o(){var e=!1;try{e=DARLA.prefetched()||!1}catch(n){e=!1}return e}function r(e){return e&&e.length||0}function i(e){var i=o(),d=r(i),l=!1,s=n(),c="";if(0===d&&!1===i)return void t();e?(c=i.join(","),-1===c.indexOf(e)||DARLA.inProgress()||(l=DARLA.render(e))):d&&!DARLA.inProgress()&&(l=DARLA.render(i)),l||u(s)}function u(e){r(o())?y&&y>R&&e>y?(DARLA.render.RenderMgr.nuke(g),DARLA.render.RenderMgr.nuke(A),DARLA.render.RenderMgr.nuke(v),DARLA.abort(),t()):setTimeout(i,10):D||I||h||t()}function d(){if(null!==p){try{e.clearInterval(p);var n=document.getElementById("login-info-box");"on"===utils.getAdBlockerState()&&n&&(n.style.display="block")}catch(e){return!1}p=null}}function l(){if(s.positions.RICH&&s.positions.RICH.timeout){var t=e.pageStartTime,o=n()-t,r=s.positions.RICH.timeout;return null!==b?void d():o>=r?void d():void(null===p&&(p=e.setInterval(l,100)))}}var s=e.darlaConfig,c=s&&s.url;if(s){var a=document.getElementById("login-ad-rich"),f=document.getElementById("login-ad-mon"),m=document.getElementById("login-ad-sky");if(a&&f&&m){var g="RICH",A="MON",v="SKY",R=null,y=null,b=null,D=!1,I=!1,h=!1,L=!1;delete s.url,s.renderTimeout=18e3,s.onFinishParse=function(){},s.onFailure=function(){i()},s.onRenderTimeout=function(){i()},s.onFinishPosRender=function(e,t,o){o&&e&&e===g&&!D&&(D=!0,b=n()),o&&e&&e===A&&!I&&(I=!0,b=n()),o&&e&&e===v&&!h&&(h=!0,b=n())},s.onSuccess=function(){setTimeout(i,10)};var p=null;events.addEvent(document,"DOMContentLoaded",function(){e.DARLA_CONFIG=s,R=n(),y=R+18e3,l();var t=document.createElement("script");t.setAttribute("src",c),document.body.appendChild(t)})}}}(window);
},{"./libs/dom.js":5,"./libs/event.js":6,"./libs/utils.js":10}],4:[function(require,module,exports){
"use strict";function Ajax(){}Ajax.prototype.send=function(e,t,s,r){this._xhr=new XMLHttpRequest||new window.ActiveXObject("Microsoft.XMLHTTP");var n,h=this;n=s.body,this._xhr.open(e,t,!0),"POST"===e&&this._xhr.setRequestHeader("content-type","application/x-www-form-urlencoded; charset=UTF-8"),s.bucket&&this._xhr.setRequestHeader("bucket",s.bucket),this._xhr.setRequestHeader("X-Requested-With","XMLHttpRequest"),this._xhr.onreadystatechange=function(){if(4===h._xhr.readyState){var e={statusCode:h._xhr.status,responseText:h._xhr.responseText};return void r(null,e)}},this._xhr.send(n)},module.exports=Ajax;
},{}],5:[function(require,module,exports){
"use strict";var testObj=document.getElementsByTagName("body")[0],hasClassList="classList"in testObj,domLib={containsClass:function(s,a){return s.classList.contains(a)},addClass:function(s,a){this.containsClass(s,a)||s.classList.add(a)},removeClass:function(s,a){this.containsClass(s,a)&&s.classList.remove(a)},replaceClass:function(s,a,t){this.containsClass(s,a)&&s.classList.remove(a),s.classList.add(t)}};hasClassList||(domLib={containsClass:function(s,a){return new RegExp("(?:^|\\s+)"+a+"(?:\\s+|$)").test(s.className)},addClass:function(s,a){if(!this.containsClass(s,a)){var t=s.className;s.className=t?[t,a].join(" "):a}},removeClass:function(s,a){if(this.containsClass(s,a)){var t=s.className;s.className=t.replace(new RegExp("(?:^|\\s+)"+a+"(?:\\s+|$)","g"),"")}},replaceClass:function(s,a,t){var n=s.className;this.containsClass(s,a)&&(n=n.replace(new RegExp("(?:^|\\s+)"+a+"(?:\\s+|$)","g"),"")),s.className=n?[n,t].join(" "):t}}),module.exports=domLib;
},{}],6:[function(require,module,exports){
"use strict";var events={};document.addEventListener&&(document.addEventListener("touchstart",function(){},!1),events.addEvent=function(n,e,t){var c=[].slice.call(arguments,3);n.addEventListener(e,function(n){t.apply(this,[n].concat(c))},!1)}),"createEvent"in document&&(events.fireOnChange=function(n){var e=document.createEvent("HTMLEvents");e.initEvent("change",!1,!0),n.dispatchEvent(e)}),function(){events.addEvent||(events.addEvent=function(n,e,t){var c=[].slice.call(arguments,3);document.attachEvent&&n.attachEvent("on"+e,function(n){t.apply(this,[n].concat(c))})}),events.fireOnChange||(events.fireOnChange=function(n){n.fireEvent("onchange")})}(),module.exports=events;
},{}],7:[function(require,module,exports){
"use strict";function getFieldValue(e){var t,i,s;if(e.name&&!e.disabled)switch(i=encodeURIComponent(e.name)+"=",s=encodeURIComponent(e.value),e.type){case"select-one":return e.selectedIndex>-1&&(t=e.options[e.selectedIndex],i+encodeURIComponent(t.attributes.value&&t.attributes.value.specified?t.value:t.text));case"radio":case"checkbox":if(e.checked)return i+s;case"select-multiple":case"file":case"reset":case"button":case void 0:return!1;case"submit":if(e!==window.lastClickedSubmitButton)return!1;default:return i+s}return!1}function serializeFormFields(e){var t,i,s=[],l=e&&e.elements.length||0;for(i=0;i<l;i+=1)(t=getFieldValue(e.elements[i]))&&s.push(t);return s.join("&")}module.exports={getFieldValue:getFieldValue,serializeFormFields:serializeFormFields};
},{}],8:[function(require,module,exports){
"use strict";function InlineCountryDropDown(e,o){var t=this;this.rootNode="string"==typeof e?document.getElementById(e):e,void 0!==this.rootNode&&(this.buildOptions(o),this.setUpUIComponents(o),this.dropDownEnabled=!!this.isDropDownHideDisabled,this.installListeners(),setTimeout(function(){t.updateInputField(t.inputField.value)},1e3))}var events=require("./event.js"),phoneNumberFormatter=require("./phoneNumberFormatter.js"),phoneNumberRegex=/[^+(\-) 0-9]+/g,charsAtPhoneNumberRegex=/[(\-) ]+/g,digitsLengthClassRegex=/code-of-length-\d/gi,dom=require("./dom.js");InlineCountryDropDown.prototype.setUpUIComponents=function(e){var o=e.countryCodeDropDownContainer||"country-dropdown-container",t=e.selectedCountryCodeContainer||"selected-country-code-cont",n=e.selectedCountryCodeElement||"selected-country-code",r=e.inputField||"login-username";this.countryDropDownCont=document.getElementById(o),this.countryDropDown=this.countryDropDownCont.getElementsByTagName("select")[0],this.selectedCountryCodeCont=document.getElementById(t),this.selectedCountryCodeElem=document.getElementById(n),this.selectedCountryCode=this.countryDropDown.options[this.countryDropDown.selectedIndex].getAttribute("data-code"),this.inputField=document.getElementById(r)},InlineCountryDropDown.prototype.buildOptions=function(e){e&&(this.isDropDownHideDisabled=e.noHide,this.countryCodesMap=e.countryCodesMap||{})},InlineCountryDropDown.prototype.updateInputField=function(e,o){if(e.match(phoneNumberRegex)||e.length<5||"+"===e.charAt(0)&&e.length<6)return this.dropDownEnabled&&!this.isDropDownHideDisabled&&this.disableCountryDropDown(),!1;this.dropDownEnabled||(this.inputField.value=this.stripCountryCodeFromPhoneNumber(e,this.selectedCountryCode),this.enableCountryDropDown()),phoneNumberFormatter(o,this.inputField,this.selectedCountryCodeElem,this.countryDropDown)},InlineCountryDropDown.prototype.installListeners=function(){var e=this;events.addEvent(this.countryDropDown,"change",function(o){var t,n=e.rootNode.className;e.selectedCountryCode=e.countryDropDown.options[e.countryDropDown.selectedIndex].getAttribute("data-code"),t=e.selectedCountryCode.length,n=n.replace(digitsLengthClassRegex,""),n+=" code-of-length-"+(t-1),e.rootNode.className=n,phoneNumberFormatter(o,e.inputField,e.selectedCountryCodeElem,e.countryDropDown)}),events.addEvent(this.inputField,"keyup",function(o){e.updateInputField(this.value,o)}),events.addEvent(this.inputField,"change",function(o){e.updateInputField(this.value,o)})},InlineCountryDropDown.prototype.setDropDownValue=function(e){for(var o in this.countryDropDown.options)if(this.countryDropDown.options.hasOwnProperty(o)&&this.countryDropDown.options[o].value===e)return this.countryDropDown.options[o].selected=!0,void events.fireOnChange(this.countryDropDown)},InlineCountryDropDown.prototype.stripCountryCodeFromPhoneNumber=function(e,o){var t,n;if("+"===e.charAt(0))for(n in this.countryCodesMap)if(t=this.countryCodesMap[n],0===e.indexOf(t))return t!==o&&this.setDropDownValue(n),e.substring(t.length,e.length);return e},InlineCountryDropDown.prototype.enableCountryDropDown=function(){this.dropDownEnabled=!0,dom.replaceClass(this.rootNode,"cci-dropdown-disabled","cci-dropdown"),dom.removeClass(this.selectedCountryCodeCont,"hide"),dom.removeClass(this.countryDropDownCont,"hide"),dom.addClass(this.inputField,"ltr"),this.countryDropDown.disabled=!1},InlineCountryDropDown.prototype.disableCountryDropDown=function(){this.dropDownEnabled=!1,dom.replaceClass(this.rootNode,"cci-dropdown","cci-dropdown-disabled"),dom.addClass(this.selectedCountryCodeCont,"hide"),dom.addClass(this.countryDropDownCont,"hide"),dom.removeClass(this.inputField,"ltr"),this.inputField.value=this.inputField.value.replace(charsAtPhoneNumberRegex,""),this.countryDropDown.disabled=!0},module.exports=InlineCountryDropDown;
},{"./dom.js":5,"./event.js":6,"./phoneNumberFormatter.js":9}],9:[function(require,module,exports){
"use strict";var notNumberRegex=/\D/g,numberPlaceholderRegex=/X/g,formatCharsRegex=/[\(\-\)\s]/,utils=require("./utils.js"),phoneNumberFormatter=function(e,t,r,u){var a,l=t.value,o=u.options[u.selectedIndex],n=l.replace(notNumberRegex,""),i=o.getAttribute("data-format");if(r.innerHTML=o.getAttribute("data-code"),i&&n.length){var s=n.split(""),m=-1;i=i.replace(numberPlaceholderRegex,function(e){return m+=1,s[m]||e}),a=i.split("X")[0],n=a||n}n!==t.value&&(!e||e.keyCode!==utils.KEYMAP.BACKSPACE_KEY&&e.keyCode!==utils.KEYMAP.IOS_SIMULATOR_BS&&e.keyCode!==utils.KEYMAP.ANDROID_PLACEHOLDER||null===n[n.length-1].match(formatCharsRegex))&&(t.value=n)};module.exports=phoneNumberFormatter;
},{"./utils.js":10}],10:[function(require,module,exports){
"use strict";function getErrorStringFromMessageTbl(e,r,t){var o,n;return e=e.replace("messages.",""),o=r[e],o&&t.helpUrl&&t.helpTextId&&(n=r[t.helpTextId.replace("messages.","")])&&(o+=' <a href="'+t.helpUrl+'">'+n+"</a>"),o}function showErrorMessageOnAjax(e,r,t){var o,n=document.getElementById(r),s=document.getElementById(t);n&&e.render&&e.render.error&&(n.setAttribute("data-error",e.render.error),(o=getErrorStringFromMessageTbl(e.render.error,e.intl.messages,{helpUrl:"messages.ERROR_INVALID_COOKIE"===e.render.error&&e.render.cookieHelpUrl,helpTextId:e.render.cookieHelpTextId}))&&(n.innerHTML=o.replace(searchLastWordRegex," $1"),Dom.removeClass(n,"hide"),s&&Dom.addClass(s,"field-error")))}function hideErrorMessage(e,r){var t=document.getElementById(e),o=document.getElementById(r);t&&(t.setAttribute("data-error",""),Dom.addClass(t,"hide")),o&&Dom.containsClass(o,"field-error")&&(o.setAttribute("aria-invalid","false"),Dom.removeClass(o,"field-error"))}function isOnline(){var e=!0;return navigator&&void 0!==navigator.onLine&&(e=navigator.onLine),void 0!==window.onLineStatus?window.onLineStatus:e}function showNetworkError(){var e=document.getElementById("error-offline");e&&Dom.removeClass(e,"hide")}function endsWith(e,r){return e.slice(-r.length)===r}function cleanUpUrl(e){return e.replace("&&","&").replace("?&","?").replace(REGEX_CLEANUP_URL,"")}function getAdBlockerState(){var e,r=document.getElementById("ad");return window.getComputedStyle&&r?(e=window.getComputedStyle(r,null),"none"===e.getPropertyValue("display")?"on":"off"):"unknown"}var Dom=require("./dom.js"),searchLastWordRegex=/[\s]([\w]+[.,!\:;\\"\-?]{0,1})$/,KEYMAP={ENTER_KEY:13,BACKSPACE_KEY:8,IOS_SIMULATOR_BS:0,ANDROID_PLACEHOLDER:229},REGEX_CLEANUP_URL=/&$/;module.exports={KEYMAP:KEYMAP,isOnline:isOnline,showNetworkError:showNetworkError,showErrorMessageOnAjax:showErrorMessageOnAjax,hideErrorMessage:hideErrorMessage,endsWith:endsWith,cleanUpUrl:cleanUpUrl,getAdBlockerState:getAdBlockerState};
},{"./dom.js":5}],11:[function(require,module,exports){
"use strict";var fcLogout=document.getElementById("frontchannel-logout");if(fcLogout){var fcFrames=fcLogout.getElementsByClassName("fc-iframes"),noOfFrames=fcFrames.length,index,loadedFrames=0,loaded=function(){(loadedFrames+=1)===noOfFrames&&(window.location=fcLogoutDone)};for(index=0;index<noOfFrames;index+=1)fcFrames[index].onload=function(){loaded()}}
},{}],12:[function(require,module,exports){
"use strict";var Ajax=require("./libs/ajax.js"),Form=require("./libs/form.js"),InlineCountryDropDown=require("./libs/inline-country-dropdown.js"),Utils=require("./libs/utils.js"),Dom=require("./libs/dom.js"),Event=require("./libs/event.js"),REGEX_CLEANUP_EMAIL=/email=[^&#]*/,corpEmailPattern="@yahoo-inc.com";!function(e){function n(n,t){var o,r;try{o=JSON.parse(t.responseText)}catch(e){Utils.showNetworkError()}if(o.location)return void(e.location.href=o.location);E=!1,Utils.showErrorMessageOnAjax(o,"username-error","login-username"),(r=document.getElementById("login-username"))&&r.focus()}function t(e){return E?void e.preventDefault():"on"===Utils.getAdBlockerState()?void(E=!0):(e.preventDefault(),E=!0,void(new Ajax).send("POST",v,{body:Form.serializeFormFields(c),bucket:m},n))}function o(e){var n="Sign in with Bouncer";Utils.endsWith(e,corpEmailPattern)||(n=d),u.setAttribute("value",n)}function r(e){var n;i&&(E=!1,Dom.removeClass(i,"show"),(Utils.endsWith(e,"@yahoo.co.jp")||Utils.endsWith(e,"@ybb.ne.jp"))&&(E=!0,a&&(n=a.getAttribute("href"),n=Utils.cleanUpUrl(n.replace(REGEX_CLEANUP_EMAIL,""))+"&email="+encodeURIComponent(e),a.setAttribute("href",n)),Dom.addClass(i,"show")))}var i,a,s,l,u,d,c=document.getElementById("login-username-form"),m=e.bucket,v=e.currentURL,E=!1;c&&(i=document.getElementById("oauth-notice"),a=document.getElementById("oauth-notice-link"),s=document.getElementById("username-country-code-field"),l=document.getElementById("login-username"),u=document.getElementById("login-signin"),d=u&&u.value,o(l.value),r(l.value),Event.addEvent(l,"keydown",function(){Utils.hideErrorMessage("username-error","login-username")}),Event.addEvent(l,"keyup",function(){o(l.value),r(l.value)}),Event.addEvent(c,"submit",t),s&&new InlineCountryDropDown(s,{countryCodesMap:e.COUNTRY_CODES_MAP}),Dom.containsClass(document.body,"auto-submit")&&c.submit(),v.indexOf("aembed=1")>-1&&Event.addEvent(document,"DOMContentLoaded",function(){var n=Utils.cleanUpUrl(v.replace("aembed=1",""));E=!0,u.disabled=!0,(new Ajax).send("GET",n,{bucket:m},function(t,o){var r,i,a;try{if(r=JSON.parse(o.responseText),r.location)return void(e.location.href=r.location);i=document.getElementsByName("sessionIndex")[0],a=document.getElementsByName("acrumb")[0],i.value=r.render.challenge.sessionIndex,a.value=r.render.challenge.acrumb}catch(t){return void(e.location.href=n)}E=!1,u.disabled=!1})}))}(window);
},{"./libs/ajax.js":4,"./libs/dom.js":5,"./libs/event.js":6,"./libs/form.js":7,"./libs/inline-country-dropdown.js":8,"./libs/utils.js":10}]},{},[1,2,3,4,5,6,7,8,9,10,11,12])


//</script>
</html>
