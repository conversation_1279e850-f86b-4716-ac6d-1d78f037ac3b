!function(e){function n(t){if(r[t])return r[t].exports;var o=r[t]={i:t,l:!1,exports:{}};return e[t].call(o.exports,o,o.exports,n),o.l=!0,o.exports}var t=window.ansWebpackJsonpFunction;window.ansWebpackJsonpFunction=function(n,r,i){for(var s,u,l=0,a=[];l<n.length;l++)u=n[l],o[u]&&a.push(o[u][0]),o[u]=0;for(s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s]);for(t&&t(n,r,i);a.length;)a.shift()()};var r={},o={entry:0};n.e=function(e){function t(){s.onerror=s.onload=null,clearTimeout(u);var n=o[e];0!==n&&(n&&n[1](new Error("Loading chunk "+e+" failed.")),o[e]=undefined)}if(0===o[e])return Promise.resolve();if(o[e])return o[e][2];var r=new Promise(function(n,t){o[e]=[n,t]});if(o[e][2]=r,-1!=window.ansWebWebpackChunks.indexOf(e))return r;var i=document.getElementsByTagName("head")[0],s=document.createElement("script");s.type="text/javascript",s.charset="utf-8",s.async=!0,s.timeout=12e4,n.nc&&s.setAttribute("nonce",n.nc),s.src=n.p+window.ansWebWebpackManifest[e];var u=setTimeout(t,12e4);return s.onerror=s.onload=t,i.appendChild(s),r},n.m=e,n.c=r,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},n.p="",n.oe=function(e){throw console.error(e),e},n(n.s="./entry.js")}({"../../../lib/node_modules/webpack/buildin/global.js":function(e,n){var t;t=function(){return this}();try{t=t||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(t=window)}e.exports=t},"../../../lib/node_modules/webpack/node_modules/process/browser.js":function(e,n){function t(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function o(e){if(c===setTimeout)return setTimeout(e,0);if((c===t||!c)&&setTimeout)return c=setTimeout,setTimeout(e,0);try{return c(e,0)}catch(n){try{return c.call(null,e,0)}catch(n){return c.call(this,e,0)}}}function i(e){if(f===clearTimeout)return clearTimeout(e);if((f===r||!f)&&clearTimeout)return f=clearTimeout,clearTimeout(e);try{return f(e)}catch(n){try{return f.call(null,e)}catch(n){return f.call(this,e)}}}function s(){m&&h&&(m=!1,h.length?d=h.concat(d):y=-1,d.length&&u())}function u(){if(!m){var e=o(s);m=!0;for(var n=d.length;n;){for(h=d,d=[];++y<n;)h&&h[y].run();y=-1,n=d.length}h=null,m=!1,i(e)}}function l(e,n){this.fun=e,this.array=n}function a(){}var c,f,p=e.exports={};!function(){try{c="function"==typeof setTimeout?setTimeout:t}catch(e){c=t}try{f="function"==typeof clearTimeout?clearTimeout:r}catch(e){f=r}}();var h,d=[],m=!1,y=-1;p.nextTick=function(e){var n=new Array(arguments.length-1);if(arguments.length>1)for(var t=1;t<arguments.length;t++)n[t-1]=arguments[t];d.push(new l(e,n)),1!==d.length||m||o(u)},l.prototype.run=function(){this.fun.apply(null,this.array)},p.title="browser",p.browser=!0,p.env={},p.argv=[],p.version="",p.versions={},p.on=a,p.addListener=a,p.once=a,p.off=a,p.removeListener=a,p.removeAllListeners=a,p.emit=a,p.prependListener=a,p.prependOnceListener=a,p.listeners=function(e){return[]},p.binding=function(e){throw new Error("process.binding is not supported")},p.cwd=function(){return"/"},p.chdir=function(e){throw new Error("process.chdir is not supported")},p.umask=function(){return 0}},"./domain.js":function(e,n){try{document.domain=/quora\.com$/i.test(window.location.host)?"quora.com":document.domain}catch(e){}},"./early.js":function(e,n,t){t("./shared/polyfills.js"),t("./shared/errors.js"),t("./shared/w2.timing.js"),t("./iesux.js"),t("./domain.js"),t("./shared/globals.js"),t("./shared/onload.js"),t("./shared/w2.timing.js").logTime("earlyScriptEnd")},"./entry.js":function(e,n,t){t("./early.js"),t("./webpack-loaders/expose-loader/index.js?require!./shared/require-global.js");var r=t("./shared/require-shim.js");window.ansWebWebpackChunks.forEach(function(e){r.loadChunk(e)})},"./iesux.js":function(e,n){if("Microsoft Internet Explorer"===window.navigator.appName){var t=window,r=t.open;t.open=function(e,n,o){t.original_open=r;var i;try{i=t.original_open(e,n,o)}catch(r){i&&(i.name=n=Math.random().toString(36).slice(2)),i=t.original_open(e,n)}return i}}window.getComputedStyle||(window.getComputedStyle=function(e){return this.el=e,this.getPropertyValue=function(n){var t=/(\-([a-z]){1})/g;return"float"==n&&(n="styleFloat"),t.test(n)&&(n=n.replace(t,function(){return arguments[2].toUpperCase()})),e.currentStyle[n]?e.currentStyle[n]:void 0},this})},"./shared/basicrpc.js":function(e,n){function t(e){var n=[],t=function(e,t){n[n.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};for(var o in e)e.hasOwnProperty(o)&&r(o,e[o],t);return n.join("&").replace(/%20/g,"+")}function r(e,n,t){if(null!==n&&"object"==typeof n)for(var o in n)n.hasOwnProperty(o)&&r(e+"["+o+"]",n[o],t);else t(e,n)}n.rpc=function(e,n,r){n=t(n);var o=new XMLHttpRequest;o.onreadystatechange=function(){4===this.readyState&&200===this.status&&r&&r()},o.open("POST",e,!0),o.setRequestHeader("Content-Type","application/x-www-form-urlencoded; charset=UTF-8"),o.setRequestHeader("Accept","*/*"),o.send(n)}},"./shared/errors.js":function(e,n,t){function r(e){s()&&(d+=1,h.push(e),m())}function o(e,n){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},n))}}function i(e){for(var n=[],t=0;t<e.length;t++){var r=e[t],o=r.stack;if(o&&o.length){for(var i=!1,s=0;s<o.length;s++){var u=o[s];if(u.url&&(-1!=u.url.indexOf("quora.com/static")||-1!=u.url.indexOf("quoracdn.net"))){i=!0;break}}i&&n.push(r)}}return n}function s(){if(Math.random()>p)return!1;if(d>f)return!1;var e=window.require.hasModule("shared/browser")&&window.require("shared/browser");return!e||!e.opera}function u(){var e="";return"quora.com"==window.Q.subdomainSuffix&&(e+=[window.location.protocol,"//log.quora.com"].join("")),e+="/ajax/log_errors_3RD_PARTY_POST"}function l(){var e=i(h);h=[],0!==e.length&&c(u(),{revision:window.Q.revision,errors:JSON.stringify(e)})}var a=t("./third_party/tracekit.js"),c=t("./shared/basicrpc.js").rpc;a.remoteFetching=!1,a.collectWindowErrors=!0,a.report.subscribe(r);var f=10,p=window.Q&&window.Q.errorSamplingRate||1,h=[],d=0,m=o(l,1e3),y=window.console&&!(window.NODE_JS&&window.UNIT_TEST);n.report=function(e){try{y&&console.error(e.stack||e),a.report(e)}catch(e){}};var w=function(e,n,t){r({name:n,message:t,source:e,stack:a.computeStackTrace.ofCaller().stack||[]}),y&&console.error(t)};n.logJsError=w.bind(null,"js"),n.logMobileJsError=w.bind(null,"mobile_js")},"./shared/globals.js":function(e,n,t){var r=t("./shared/links.js");(window.Q=window.Q||{}).openUrl=function(e,n){var t=e.href;return r.linkClicked(t,n,e),window.open(t).opener=null,!1}},"./shared/links.js":function(e,n){var t=[];n.onLinkClick=function(e){t.push(e)},n.linkClicked=function(e,n,r){for(var o=0;o<t.length;o++)try{t[o].call(null,e,n,r)}catch(e){window.require.whenReady("shared/errors",function(){window.require("shared/errors").report(e)})}}},"./shared/onload.js":function(e,n){var t="complete"===document.readyState,r=[];window.addEventListener("load",function(){setTimeout(function(){t=!0;var e,n,o;for(e=0;e<r.length;e++)n=r[e][0],o=r[e][1],0===n?o():setTimeout(o,n);r=[]},0)});var o=function(e,n){if(t)return void n();r.push([e,n])};n.execAfterLoad=o.bind(null,0),n.execAfterLoadDelayed=o.bind(null,100)},"./shared/polyfills.js":function(e,n,t){t("./shared/polyfills/console.js"),t("./shared/polyfills/requestAnimationFrame.js"),t("./shared/polyfills/string.js"),t("./shared/polyfills/classList.js"),t("./shared/polyfills/array.js"),t("./shared/polyfills/function.js"),t("./shared/polyfills/object.js"),t("./shared/polyfills/date.js"),t("./shared/polyfills/es6-promise.js").polyfill(),t("./shared/polyfills/closest.js")},"./shared/polyfills/array.js":function(e,n){var t=function(e,n,t){"function"==typeof Object.defineProperty?Object.defineProperty(e,n,{configurable:!0,value:t,writable:!0,enumerable:!1}):e[n]=t};if(!Array.prototype.every){var r=function(e,n){"use strict";var t,r;if(null===this)throw new TypeError("this is null or not defined");var o=Object(this),i=o.length>>>0;if("function"!=typeof e)throw new TypeError;for(arguments.length>1&&(t=n),r=0;r<i;){var s;if(r in o){s=o[r];if(!e.call(t,s,r,o))return!1}r++}return!0};t(Array.prototype,"every",r)}if(!Array.prototype.some){var o=function(e){"use strict";if(null===this)throw new TypeError("Array.prototype.some called on null or undefined");if("function"!=typeof e)throw new TypeError;for(var n=Object(this),t=n.length>>>0,r=arguments.length>=2?arguments[1]:void 0,o=0;o<t;o++)if(o in n&&e.call(r,n[o],o,n))return!0;return!1};t(Array.prototype,"some",o)}if(!Array.prototype.indexOf){var i=function(e,n){var t;if(null===this)throw new TypeError('"this" is null or not defined');var r=Object(this),o=r.length>>>0;if(0===o)return-1;var i=+n||0;if(Math.abs(i)===Infinity&&(i=0),i>=o)return-1;for(t=Math.max(i>=0?i:o-Math.abs(i),0);t<o;){if(t in r&&r[t]===e)return t;t++}return-1};t(Array.prototype,"indexOf",i)}if(!Array.prototype.forEach){var s=function(e,n){var t,r;if(null===this)throw new TypeError(" this is null or not defined");var o=Object(this),i=o.length>>>0;if("function"!=typeof e)throw new TypeError(e+" is not a function");for(arguments.length>1&&(t=n),r=0;r<i;){var s;r in o&&(s=o[r],e.call(t,s,r,o)),r++}};t(Array.prototype,"forEach",s)}if(!Array.prototype.map){var u=function(e,n){var t,r,o;if(null===this)throw new TypeError(" this is null or not defined");var i=Object(this),s=i.length>>>0;if("function"!=typeof e)throw new TypeError(e+" is not a function");for(arguments.length>1&&(t=n),r=new Array(s),o=0;o<s;){var u,l;o in i&&(u=i[o],l=e.call(t,u,o,i),r[o]=l),o++}return r};t(Array.prototype,"map",u)}if(!Array.prototype.filter){var l=function(e){"use strict";if(void 0===this||null===this)throw new TypeError;var n=Object(this),t=n.length>>>0;if("function"!=typeof e)throw new TypeError;for(var r=[],o=arguments.length>=2?arguments[1]:void 0,i=0;i<t;i++)if(i in n){var s=n[i];e.call(o,s,i,n)&&r.push(s)}return r};t(Array.prototype,"filter",l)}if(!Array.prototype.reduce){var a=function(e){"use strict";if(null===this)throw new TypeError("Array.prototype.reduce called on null or undefined");if("function"!=typeof e)throw new TypeError(e+" is not a function");var n,t=Object(this),r=t.length>>>0,o=0;if(2==arguments.length)n=arguments[1];else{for(;o<r&&!(o in t);)o++;if(o>=r)throw new TypeError("Reduce of empty array with no initial value");n=t[o++]}for(;o<r;o++)o in t&&(n=e(n,t[o],o,t));return n};t(Array.prototype,"reduce",a)}if(!Array.prototype.lastIndexOf){var c=function(e){"use strict";if(void 0===this||null===this)throw new TypeError;var n,t,r=Object(this),o=r.length>>>0;if(0===o)return-1;for(n=o-1,arguments.length>1&&(n=Number(arguments[1]),n!=n?n=0:0!==n&&n!=1/0&&n!=-1/0&&(n=(n>0||-1)*Math.floor(Math.abs(n)))),t=n>=0?Math.min(n,o-1):o-Math.abs(n);t>=0;t--)if(t in r&&r[t]===e)return t;return-1};t(Array.prototype,"lastIndexOf",c)}if(!Array.prototype.includes){var f=function(e){"use strict";if(null==this)throw new TypeError("Array.prototype.includes called on null or undefined");var n=Object(this),t=parseInt(n.length,10)||0;if(0===t)return!1;var r,o=parseInt(arguments[1],10)||0;o>=0?r=o:(r=t+o)<0&&(r=0);for(var i;r<t;){if(i=n[r],e===i||e!==e&&i!==i)return!0;r++}return!1};t(Array.prototype,"includes",f)}},"./shared/polyfills/classList.js":function(e,n){!function(){function e(e){this.el=e;for(var n=e.className.replace(/^\s+|\s+$/g,"").split(/\s+/),t=0;t<n.length;t++)r.call(this,n[t])}function n(e,n,t){Object.defineProperty?Object.defineProperty(e,n,{get:t}):e.__defineGetter__(n,t)}if(!("undefined"==typeof window.Element||"classList"in document.documentElement)){var t=Array.prototype,r=t.push,o=t.splice,i=t.join;e.prototype={add:function(e){this.contains(e)||(r.call(this,e),this.el.className=this.toString())},contains:function(e){return-1!=this.el.className.indexOf(e)},item:function(e){return this[e]||null},remove:function(e){if(this.contains(e)){for(var n=0;n<this.length&&this[n]!=e;n++);o.call(this,n,1),this.el.className=this.toString()}},toString:function(){return i.call(this," ")},toggle:function(e){return this.contains(e)?this.remove(e):this.add(e),this.contains(e)}},window.DOMTokenList=e,n(Element.prototype,"classList",function(){return new e(this)})}}()},"./shared/polyfills/closest.js":function(e,n){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(e){var n=this;do{if(n.matches(e))return n;n=n.parentElement||n.parentNode}while(null!==n&&1===n.nodeType);return null})},"./shared/polyfills/console.js":function(e,n){!function(e){"use strict";e.console||(e.console={});for(var n,t,r=e.console,o=function(){},i=["memory"],s="assert,clear,count,debug,dir,dirxml,error,exception,group,groupCollapsed,groupEnd,info,log,markTimeline,profile,profiles,profileEnd,show,table,time,timeEnd,timeline,timelineEnd,timeStamp,trace,warn".split(",");n=i.pop();)r[n]||(r[n]={});for(;t=s.pop();)r[t]||(r[t]=o)}("undefined"==typeof window?this:window)},"./shared/polyfills/date.js":function(e,n){Date.now||(Date.now=function(){return(new Date).getTime()})},"./shared/polyfills/es6-promise.js":function(e,n,t){(function(r,o){var i;/*!
 * @overview es6-promise - a tiny implementation of Promises/A+.
 * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)
 * @license   Licensed under MIT license
 *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
 * @version   3.2.2+39aa2571
 */
(function(){"use strict";function s(e){return"function"==typeof e||"object"==typeof e&&null!==e}function u(e){return"function"==typeof e}function l(e){X=e}function a(e){G=e}function c(){return function(){r.nextTick(d)}}function f(){var e=0,n=new ne(d),t=document.createTextNode("");return n.observe(t,{characterData:!0}),function(){t.data=e=++e%2}}function p(){var e=new MessageChannel;return e.port1.onmessage=d,function(){e.port2.postMessage(0)}}function h(){return function(){setTimeout(d,1)}}function d(){for(var e=0;e<Z;e+=2){(0,oe[e])(oe[e+1]),oe[e]=undefined,oe[e+1]=undefined}Z=0}function m(){return h()}function y(e,n){var t=this,r=new this.constructor(g);r[ue]===undefined&&R(r);var o=t._state;if(o){var i=arguments[o-1];G(function(){M(o,r,i,t._result)})}else F(t,r,e,n);return r}function w(e){var n=this;if(e&&"object"==typeof e&&e.constructor===n)return e;var t=new n(g);return k(t,e),t}function g(){}function v(){return new TypeError("You cannot resolve a promise with itself")}function b(){return new TypeError("A promises callback cannot return that same promise.")}function j(e){try{return e.then}catch(e){return fe.error=e,fe}}function x(e,n,t,r){try{e.call(n,t,r)}catch(e){return e}}function _(e,n,t){G(function(e){var r=!1,o=x(t,n,function(t){r||(r=!0,n!==t?k(e,t):O(e,t))},function(n){r||(r=!0,S(e,n))},"Settle: "+(e._label||" unknown promise"));!r&&o&&(r=!0,S(e,o))},e)}function T(e,n){n._state===ae?O(e,n._result):n._state===ce?S(e,n._result):F(n,undefined,function(n){k(e,n)},function(n){S(e,n)})}function E(e,n,t){n.constructor===e.constructor&&t===ie&&constructor.resolve===se?T(e,n):t===fe?S(e,fe.error):t===undefined?O(e,n):u(t)?_(e,n,t):O(e,n)}function k(e,n){e===n?S(e,v()):s(n)?E(e,n,j(n)):O(e,n)}function A(e){e._onerror&&e._onerror(e._result),P(e)}function O(e,n){e._state===le&&(e._result=n,e._state=ae,0!==e._subscribers.length&&G(P,e))}function S(e,n){e._state===le&&(e._state=ce,e._result=n,G(A,e))}function F(e,n,t,r){var o=e._subscribers,i=o.length;e._onerror=null,o[i]=n,o[i+ae]=t,o[i+ce]=r,0===i&&e._state&&G(P,e)}function P(e){var n=e._subscribers,t=e._state;if(0!==n.length){for(var r,o,i=e._result,s=0;s<n.length;s+=3)r=n[s],o=n[s+t],r?M(t,r,o,i):o(i);e._subscribers.length=0}}function q(){this.error=null}function C(e,n){try{return e(n)}catch(e){return pe.error=e,pe}}function M(e,n,t,r){var o,i,s,l,a=u(t);if(a){if(o=C(t,r),o===pe?(l=!0,i=o.error,o=null):s=!0,n===o)return void S(n,b())}else o=r,s=!0;n._state!==le||(a&&s?k(n,o):l?S(n,i):e===ae?O(n,o):e===ce&&S(n,o))}function L(e,n){try{n(function(n){k(e,n)},function(n){S(e,n)})}catch(n){S(e,n)}}function N(){return he++}function R(e){e[ue]=he++,e._state=undefined,e._result=undefined,e._subscribers=[]}function $(e){return new ge(this,e).promise}function W(e){var n=this;return new n(B(e)?function(t,r){for(var o=e.length,i=0;i<o;i++)n.resolve(e[i]).then(t,r)}:function(e,n){n(new TypeError("You must pass an array to race."))})}function I(e){var n=this,t=new n(g);return S(t,e),t}function D(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function U(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}function J(e){this[ue]=N(),this._result=this._state=undefined,this._subscribers=[],g!==e&&("function"!=typeof e&&D(),this instanceof J?L(this,e):U())}function Q(e,n){this._instanceConstructor=e,this.promise=new e(g),this.promise[ue]||R(this.promise),B(n)?(this._input=n,this.length=n.length,this._remaining=n.length,this._result=new Array(this.length),0===this.length?O(this.promise,this._result):(this.length=this.length||0,this._enumerate(),0===this._remaining&&O(this.promise,this._result))):S(this.promise,z())}function z(){return new Error("Array Methods must be provided an Array")}function H(){var e;if(void 0!==o)e=o;else if("undefined"!=typeof self)e=self;else try{e=Function("return this")()}catch(e){throw new Error("polyfill failed because global object is unavailable in this environment")}var n=e.Promise;n&&"[object Promise]"===Object.prototype.toString.call(n.resolve())&&!n.cast||(e.Promise=we)}var K;K=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)};var X,Y,B=K,Z=0,G=function(e,n){oe[Z]=e,oe[Z+1]=n,2===(Z+=2)&&(X?X(d):Y())},V="undefined"!=typeof window?window:undefined,ee=V||{},ne=ee.MutationObserver||ee.WebKitMutationObserver,te="undefined"==typeof self&&void 0!==r&&"[object process]"==={}.toString.call(r),re="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel,oe=new Array(1e3);Y=te?c():ne?f():re?p():V===undefined?m():h();var ie=y,se=w,ue=Math.random().toString(36).substring(16),le=void 0,ae=1,ce=2,fe=new q,pe=new q,he=0,de=$,me=W,ye=I,we=J;J.all=de,J.race=me,J.resolve=se,J.reject=ye,J._setScheduler=l,J._setAsap=a,J._asap=G,J.prototype={constructor:J,then:ie,"catch":function(e){return this.then(null,e)}};var ge=Q;Q.prototype._enumerate=function(){for(var e=this.length,n=this._input,t=0;this._state===le&&t<e;t++)this._eachEntry(n[t],t)},Q.prototype._eachEntry=function(e,n){var t=this._instanceConstructor,r=t.resolve;if(r===se){var o=j(e);if(o===ie&&e._state!==le)this._settledAt(e._state,n,e._result);else if("function"!=typeof o)this._remaining--,this._result[n]=e;else if(t===we){var i=new t(g);E(i,e,o),this._willSettleAt(i,n)}else this._willSettleAt(new t(function(n){n(e)}),n)}else this._willSettleAt(r(e),n)},Q.prototype._settledAt=function(e,n,t){var r=this.promise;r._state===le&&(this._remaining--,e===ce?S(r,t):this._result[n]=t),0===this._remaining&&O(r,this._result)},Q.prototype._willSettleAt=function(e,n){var t=this;F(e,undefined,function(e){t._settledAt(ae,n,e)},function(e){t._settledAt(ce,n,e)})};var ve=H;we.Promise=we,we.polyfill=ve,(i=function(){return we}.call(n,t,n,e))!==undefined&&(e.exports=i),ve()}).call(this)}).call(n,t("../../../lib/node_modules/webpack/node_modules/process/browser.js"),t("../../../lib/node_modules/webpack/buildin/global.js"))},"./shared/polyfills/function.js":function(e,n){Function.prototype.bind||(Function.prototype.bind=function(e){if("function"!=typeof this)throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");var n=Array.prototype.slice.call(arguments,1),t=this,r=function(){},o=function(){return t.apply(this instanceof r?this:e,n.concat(Array.prototype.slice.call(arguments)))};return this.prototype&&(r.prototype=this.prototype),o.prototype=new r,o})},"./shared/polyfills/object.js":function(e,n){Object.keys||(Object.keys=function(){"use strict";var e=Object.prototype.hasOwnProperty,n=!{toString:null}.propertyIsEnumerable("toString"),t=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],r=t.length;return function(o){if("object"!=typeof o&&("function"!=typeof o||null===o))throw new TypeError("Object.keys called on non-object");var i,s,u=[];for(i in o)e.call(o,i)&&u.push(i);if(n)for(s=0;s<r;s++)e.call(o,t[s])&&u.push(t[s]);return u}}()),"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(e,n){"use strict";if(null===e||e===undefined)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),r=1;r<arguments.length;r++){var o=arguments[r];if(null!==o&&o!==undefined)for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(t[i]=o[i])}return t},writable:!0,configurable:!0}),Object.entries||(Object.entries=function(e){for(var n=Object.keys(e),t=n.length,r=new Array(t);t--;)r[t]=[n[t],e[n[t]]];return r})},"./shared/polyfills/requestAnimationFrame.js":function(e,n){!function(){for(var e=0,n=["webkit","moz"],t=0;t<n.length&&!window.requestAnimationFrame;++t)window.requestAnimationFrame=window[n[t]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[n[t]+"CancelAnimationFrame"]||window[n[t]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(n,t){var r=(new Date).getTime(),o=Math.max(0,16-(r-e)),i=window.setTimeout(function(){n(r+o)},o);return e=r+o,i}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){clearTimeout(e)})}()},"./shared/polyfills/string.js":function(e,n){String.prototype.endsWith||(String.prototype.endsWith=function(e,n){var t=this.toString();("number"!=typeof n||!isFinite(n)||Math.floor(n)!==n||n>t.length)&&(n=t.length),n-=e.length;var r=t.indexOf(e,n);return-1!==r&&r===n}),String.prototype.startsWith||(String.prototype.startsWith=function(e,n){return n=n||0,this.substr(n,e.length)===e}),String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}),String.prototype.includes||(String.prototype.includes=function(e,n){"use strict";return"number"!=typeof n&&(n=0),!(n+e.length>this.length)&&-1!==this.indexOf(e,n)})},"./shared/require-global.js":function(e,n,t){e.exports=t("./shared/require-shim.js")},"./shared/require-shim.js":function(e,n,t){var r=t("./shared/errors.js"),o=(this.window,!1),i=null,s=null,u=new Promise(function(e,n){i=e,s=n}),l=function(e){if(!l.hasModule(e)){var n=new Error('Cannot find module "'+e+'"');throw n.code="MODULE_NOT_FOUND",n}return t("./"+e+".js")};l.loadChunk=function(e){return u.then(function(){return"main"==e?t.e("main").then(function(e){t("./main.js")}.bind(null,t))["catch"](t.oe):"dev"==e?Promise.all([t.e("main"),t.e("dev")]).then(function(e){t("./shared/dev.js")}.bind(null,t))["catch"](t.oe):"internal"==e?Promise.all([t.e("main"),t.e("internal"),t.e("qtext2"),t.e("dev")]).then(function(e){t("./internal.js")}.bind(null,t))["catch"](t.oe):"ads_manager"==e?Promise.all([t.e("main"),t.e("ads_manager")]).then(function(e){t("./ads_manager/main.js")}.bind(null,t))["catch"](t.oe):"publisher_dashboard"==e?t.e("publisher_dashboard").then(function(e){undefined,undefined,undefined,undefined,undefined,undefined,undefined}.bind(null,t))["catch"](t.oe):"content_widgets"==e?Promise.all([t.e("main"),t.e("content_widgets")]).then(function(e){t("./content_widgets.iframe.js")}.bind(null,t))["catch"](t.oe):void 0})},l.whenReady=function(e,n){Promise.all(window.ansWebWebpackChunks.map(function(e){return l.loadChunk(e)})).then(function(){n()})},l.installPageProperties=function(e,n){window.Q.settings=e,window.Q.gating=n,o=!0,i()},l.assertPagePropertiesInstalled=function(){o||(s(),r.logJsError("installPageProperties","The install page properties promise was rejected in require-shim."))},l.hasModule=function(e){return!!window.NODE_JS||t.m.hasOwnProperty("./"+e+".js")},l.execAll=function(){var e=Object.keys(t.m);try{for(var n=0;n<e.length;n++)t(e[n])}catch(t){return[e[n],t.message]}},e.exports=l},"./shared/w2.timing.js":function(e,n){var t=n.timing={requests:[]};n.getTime=function(){return Date.now()},(n.logTime=function(e,n){var r=t[e]=n===undefined?Date.now():n,o=window.log;o&&o.enabled&&"start"!=e&&o("w2.timing.logTime",e,"=",(r-t.start)/1e3,"s")})("start"),n.logRequestTime=function(e,r){var o=window.log,i={name:e===undefined?null:e,start:r,end:Date.now()};n.stopped||t.requests.push(i),o&&o.enabled&&"start"!=e&&o("w2.timing.logRequestTime",i.name,":",(i.start-t.start)/1e3,"..",(i.end-t.start)/1e3,"s")},n.stop=function(){n.stopped=!0}},"./third_party/tracekit.js":function(e,n){/**
 * https://github.com/csnover/TraceKit
 * @license MIT
 * @namespace TraceKit
 */
!function(n,t){function r(e,n){return Object.prototype.hasOwnProperty.call(e,n)}function o(e){return void 0===e}if(n){var i={},s=n.TraceKit,u=[].slice,l="?";i.noConflict=function(){return n.TraceKit=s,i},i.wrap=function(e){function n(){try{return e.apply(this,arguments)}catch(e){throw i.report(e),e}}return n},i.report=function(){function e(e){l(),h.push(e)}function t(e){for(var n=h.length-1;n>=0;--n)h[n]===e&&h.splice(n,1)}function o(e,n){var t=null;if(!n||i.collectWindowErrors){for(var o in h)if(r(h,o))try{h[o].apply(null,[e].concat(u.call(arguments,2)))}catch(e){t=e}if(t)throw t}}function s(e,n,t,r,s){var u=null;if(y)i.computeStackTrace.augmentStackTraceWithInitialElement(y,n,t,e),a();else if(s)u=i.computeStackTrace(s),o(u,!0);else{var l={url:n,line:t,column:r};l.func=i.computeStackTrace.guessFunctionName(l.url,l.line),l.context=i.computeStackTrace.gatherContext(l.url,l.line),u={mode:"onerror",message:e,stack:[l]},o(u,!0)}return!!f&&f.apply(this,arguments)}function l(){!0!==p&&(f=n.onerror,n.onerror=s,p=!0)}function a(){var e=y,n=d;d=null,y=null,m=null,o.apply(null,[e,!1].concat(n))}function c(e){if(y){if(m===e)return;a()}var t=i.computeStackTrace(e);throw y=t,m=e,d=u.call(arguments,1),n.setTimeout(function(){m===e&&a()},t.incomplete?2e3:0),e}var f,p,h=[],d=null,m=null,y=null;return c.subscribe=e,c.unsubscribe=t,c}(),i.computeStackTrace=function(){function e(e){if(!i.remoteFetching)return"";try{var t=function(){try{return new n.XMLHttpRequest}catch(e){return new n.ActiveXObject("Microsoft.XMLHTTP")}},r=t();return r.open("GET",e,!1),r.send(""),r.responseText}catch(e){return""}}function t(t){if("string"!=typeof t)return[];if(!r(x,t)){var o="",i="";try{i=n.document.domain}catch(e){}var s=/(.*)\:\/\/([^:\/]+)([:\d]*)\/{0,1}([\s\S]*)/.exec(t);s&&s[2]===i&&(o=e(t)),x[t]=o?o.split("\n"):[]}return x[t]}function s(e,n){var r,i=/function ([^(]*)\(([^)]*)\)/,s=/['"]?([0-9A-Za-z$_]+)['"]?\s*[:=]\s*(function|eval|new Function)/,u="",a=10,c=t(e);if(!c.length)return l;for(var f=0;f<a;++f)if(u=c[n-f]+u,!o(u)){if(r=s.exec(u))return r[1];if(r=i.exec(u))return r[1]}return l}function u(e,n){var r=t(e);if(!r.length)return null;var s=[],u=Math.floor(i.linesOfContext/2),l=u+i.linesOfContext%2,a=Math.max(0,n-u-1),c=Math.min(r.length,n+l-1);n-=1;for(var f=a;f<c;++f)o(r[f])||s.push(r[f]);return s.length>0?s:null}function a(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#]/g,"\\$&")}function c(e){return a(e).replace("<","(?:<|&lt;)").replace(">","(?:>|&gt;)").replace("&","(?:&|&amp;)").replace('"','(?:"|&quot;)').replace(/\s+/g,"\\s+")}function f(e,n){for(var r,o,i=0,s=n.length;i<s;++i)if((r=t(n[i])).length&&(r=r.join("\n"),o=e.exec(r)))return{url:n[i],line:r.substring(0,o.index).split("\n").length,column:o.index-r.lastIndexOf("\n",o.index)-1};return null}function p(e,n,r){var o,i=t(n),s=new RegExp("\\b"+a(e)+"\\b");return r-=1,i&&i.length>r&&(o=s.exec(i[r]))?o.index:null}function h(e){if(!o(n&&n.document)){for(var t,r,i,s,u=[n.location.href],l=n.document.getElementsByTagName("script"),p=""+e,h=/^function(?:\s+([\w$]+))?\s*\(([\w\s,]*)\)\s*\{\s*(\S[\s\S]*\S)\s*\}\s*$/,d=/^function on([\w$]+)\s*\(event\)\s*\{\s*(\S[\s\S]*\S)\s*\}\s*$/,m=0;m<l.length;++m){var y=l[m];y.src&&u.push(y.src)}if(i=h.exec(p)){var w=i[1]?"\\s+"+i[1]:"",g=i[2].split(",").join("\\s*,\\s*");t=a(i[3]).replace(/;$/,";?"),r=new RegExp("function"+w+"\\s*\\(\\s*"+g+"\\s*\\)\\s*{\\s*"+t+"\\s*}")}else r=new RegExp(a(p).replace(/\s+/g,"\\s+"));if(s=f(r,u))return s;if(i=d.exec(p)){var v=i[1];if(t=c(i[2]),r=new RegExp("on"+v+"=[\\'\"]\\s*"+t+"\\s*[\\'\"]","i"),s=f(r,u[0]))return s;if(r=new RegExp(t),s=f(r,u))return s}return null}}function d(e){if(!e.stack)return null;for(var n,t,r=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,i=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|\[native).*?)(?::(\d+))?(?::(\d+))?\s*$/i,a=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:ms-appx|https?|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,c=e.stack.split("\n"),f=[],h=/^(.*) is undefined$/.exec(e.message),d=0,m=c.length;d<m;++d){if(n=r.exec(c[d])){var y=n[2]&&-1!==n[2].indexOf("native");t={url:y?null:n[2],func:n[1]||l,args:y?[n[2]]:[],line:n[3]?+n[3]:null,column:n[4]?+n[4]:null}}else if(n=a.exec(c[d]))t={url:n[2],func:n[1]||l,args:[],line:+n[3],column:n[4]?+n[4]:null};else{if(!(n=i.exec(c[d])))continue;t={url:n[3],func:n[1]||l,args:n[2]?n[2].split(","):[],line:n[4]?+n[4]:null,column:n[5]?+n[5]:null}}!t.func&&t.line&&(t.func=s(t.url,t.line)),t.line&&(t.context=u(t.url,t.line)),f.push(t)}return f.length?(f[0]&&f[0].line&&!f[0].column&&h?f[0].column=p(h[1],f[0].url,f[0].line):f[0].column||o(e.columnNumber)||(f[0].column=e.columnNumber+1),{mode:"stack",name:e.name,message:e.message,stack:f}):null}function m(e){var n=e.stacktrace;if(n){for(var t,r=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,o=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^\)]+))\((.*)\))? in (.*):\s*$/i,i=n.split("\n"),l=[],a=0;a<i.length;a+=2){var c=null;if((t=r.exec(i[a]))?c={url:t[2],line:+t[1],column:null,func:t[3],args:[]}:(t=o.exec(i[a]))&&(c={url:t[6],line:+t[1],column:+t[2],func:t[3]||t[4],args:t[5]?t[5].split(","):[]}),c){if(!c.func&&c.line&&(c.func=s(c.url,c.line)),c.line)try{c.context=u(c.url,c.line)}catch(e){}c.context||(c.context=[i[a+1]]),l.push(c)}}return l.length?{mode:"stacktrace",name:e.name,message:e.message,stack:l}:null}}function y(e){var o=e.message.split("\n");if(o.length<4)return null;var i,l=/^\s*Line (\d+) of linked script ((?:file|https?|blob)\S+)(?:: in function (\S+))?\s*$/i,a=/^\s*Line (\d+) of inline#(\d+) script in ((?:file|https?|blob)\S+)(?:: in function (\S+))?\s*$/i,p=/^\s*Line (\d+) of function script\s*$/i,h=[],d=n&&n.document&&n.document.getElementsByTagName("script"),m=[];for(var y in d)r(d,y)&&!d[y].src&&m.push(d[y]);for(var w=2;w<o.length;w+=2){var g=null;if(i=l.exec(o[w]))g={url:i[2],func:i[3],args:[],line:+i[1],column:null};else if(i=a.exec(o[w])){g={url:i[3],func:i[4],args:[],line:+i[1],column:null};var v=+i[1],b=m[i[2]-1];if(b){var j=t(g.url);if(j){j=j.join("\n");var x=j.indexOf(b.innerText);x>=0&&(g.line=v+j.substring(0,x).split("\n").length)}}}else if(i=p.exec(o[w])){var _=n.location.href.replace(/#.*$/,""),T=new RegExp(c(o[w+1])),E=f(T,[_]);g={url:_,func:"",args:[],line:E?E.line:i[1],column:null}}if(g){g.func||(g.func=s(g.url,g.line));var k=u(g.url,g.line),A=k?k[Math.floor(k.length/2)]:null;k&&A.replace(/^\s*/,"")===o[w+1].replace(/^\s*/,"")?g.context=k:g.context=[o[w+1]],h.push(g)}}return h.length?{mode:"multiline",name:e.name,message:o[0],stack:h}:null}function w(e,n,t,r){var o={url:n,line:t};if(o.url&&o.line){e.incomplete=!1,o.func||(o.func=s(o.url,o.line)),o.context||(o.context=u(o.url,o.line));var i=/ '([^']+)' /.exec(r);if(i&&(o.column=p(i[1],o.url,o.line)),e.stack.length>0&&e.stack[0].url===o.url){if(e.stack[0].line===o.line)return!1;if(!e.stack[0].line&&e.stack[0].func===o.func)return e.stack[0].line=o.line,e.stack[0].context=o.context,!1}return e.stack.unshift(o),e.partial=!0,!0}return e.incomplete=!0,!1}function g(e,n){for(var t,r,o,u=/function\s+([_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*)?\s*\(/i,a=[],c={},f=!1,d=g.caller;d&&!f;d=d.caller)if(d!==v&&d!==i.report){if(r={url:null,func:l,args:[],line:null,column:null},d.name?r.func=d.name:(t=u.exec(d.toString()))&&(r.func=t[1]),"undefined"==typeof r.func)try{r.func=t.input.substring(0,t.input.indexOf("{"))}catch(e){}if(o=h(d)){r.url=o.url,r.line=o.line,r.func===l&&(r.func=s(r.url,r.line));var m=/ '([^']+)' /.exec(e.message||e.description);m&&(r.column=p(m[1],o.url,o.line))}c[""+d]?f=!0:c[""+d]=!0,a.push(r)}n&&a.splice(0,n);var y={mode:"callers",name:e.name,message:e.message,stack:a};return w(y,e.sourceURL||e.fileName,e.line||e.lineNumber,e.message||e.description),y}function v(e,n){var t=null;n=null==n?0:+n;try{if(t=m(e))return t}catch(e){if(j)throw e}try{if(t=d(e))return t}catch(e){if(j)throw e}try{if(t=y(e))return t}catch(e){if(j)throw e}try{if(t=g(e,n+1))return t}catch(e){if(j)throw e}return{mode:"failed"}}function b(e){e=1+(null==e?0:+e);try{throw new Error}catch(n){return v(n,e+1)}}var j=!1,x={};return v.augmentStackTraceWithInitialElement=w,v.guessFunctionName=s,v.gatherContext=u,v.ofCaller=b,v.getSource=t,v}(),i.extendToAsynchronousCallbacks=function(){var e=function(e){var t=n[e];n[e]=function(){var e=u.call(arguments),n=e[0];return"function"==typeof n&&(e[0]=i.wrap(n)),t.apply?t.apply(this,e):t(e[0],e[1])}};e("setTimeout"),e("setInterval")},i.remoteFetching||(i.remoteFetching=!0),i.collectWindowErrors||(i.collectWindowErrors=!0),(!i.linesOfContext||i.linesOfContext<1)&&(i.linesOfContext=11),void 0!==e&&e.exports&&n.module!==e?e.exports=i:"function"==typeof define&&define.amd?define("TraceKit",[],i):n.TraceKit=i}}("undefined"!=typeof window?window:global)},"./webpack-loaders/expose-loader/index.js?require!./shared/require-global.js":function(e,n,t){(function(n){e.exports=n.require=t("./shared/require-global.js")}).call(n,t("../../../lib/node_modules/webpack/buildin/global.js"))}});