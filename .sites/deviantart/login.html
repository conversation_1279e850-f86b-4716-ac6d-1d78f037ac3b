<!DOCTYPE html>
<html><!--<![endif]--><head prefix="og: http://ogp.me/ns# fb: http://ogp.me/ns/fb#">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
<meta charset="ISO-8859-1">
<title>Log In | DeviantArt</title>
<link rel="dns-prefetch" href="https://www.da-ads.com">
<meta http-equiv="x-dns-prefetch-control" content="off">
<meta property="fb:page_id" content="6452638289">
<link rel="publisher" href="https://plus.google.com/108424020240415471405">
<meta http-equiv="content-language" content="en">
<meta name="description" content="DeviantArt is the world's largest online social community for artists and art enthusiasts, allowing people to connect through the creation and sharing of art.">
<meta name="keywords" content="Art, Digital Art, Photography, Traditional Art, Community Art, Contemporary Art, Modern Art, Skins, Themes, Poetry, Prose, Applications, Wallpapers, Online Art, graphic Design, web Design, gaming, photography, animation, comic Books, digital Images">
<meta name="classification" content="Art">
<meta name="copyright" content="Copyright 2021 deviantART">
<meta name="apple-itunes-app" content="app-id=925219396">
<meta name="twitter:site" content="@deviantart">
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:image" content="https://st.deviantart.net/minish/main/logo/card_black_large.png">
<meta name="twitter:image:src" content="https://st.deviantart.net/minish/main/logo/card_black_large.png">
<meta property="og:type" content="website">
<meta property="og:url" content="https://www.deviantart.com/users/login">
<meta property="og:title" content="Log In | DeviantArt">
<meta property="og:description" content="DeviantArt is the world's largest online social community for artists and art enthusiasts, allowing people to connect through the creation and sharing of art.">
<meta property="og:site_name" content="DeviantArt">
<meta property="og:image" content="https://st.deviantart.net/minish/main/logo/card_black_large.png">
<meta property="twitter:account_id" content="1239671">
<script async="" src="https://sb.scorecardresearch.com/beacon.js"></script><script async="" src="https://st.deviantart.net/css/dapx_jc.js?*********"></script><script async="" type="text/javascript" src="https://www.googletagservices.com/tag/js/gpt.js"></script><script async="" src="//client.forcenock.com/client.js"></script><script async="" src="//client.perimeterx.net/PXiUbOGhJL/main.min.js"></script><script src="https://secure.quantserve.com/quant.js" async="" type="text/javascript"></script><script async="" src="//www.google-analytics.com/analytics.js"></script><script type="text/javascript" id="dagaua_init">
(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
})(window,document,'script','//www.google-analytics.com/analytics.js','ga');
ga("create","UA-322734-1",".deviantart.com");
ga("set","dimension1","visitor");
ga("set","dimension6","menu_dropdown-messages_n\/a");
ga("send","pageview",{"dimension4":0});            </script>
            <script type="text/javascript" id="dwaitdatex-5b32569f18fbe">
(function(){
    function dwaitdatex_5b32569f18fbe(DWait) {
        var DWaitReady = false
        ,context = this
        ,cb = function() {
            DWaitReady = true;
            var elem = document.getElementById('dwaitdatex-5b32569f18fbe');
            context = elem ? elem.parentNode : context;
            (function(){ ga(function () { PubSub.publish('DaGa.init', {});  }); }).apply(context);
        };
        DWait.ready(["jms/lib/pubsub.js", "jms/lib/analytics.js"], cb);
    }

    function load() {
        if (window.DWait) {
            dwaitdatex_5b32569f18fbe(DWait);
        } else {
            setTimeout(load, 100);
        }
    }

    load();
})();
</script>        <script type="text/javascript">
          var _qevents = _qevents || [];

          (function() {
           var elem = document.createElement('script');

           elem.src = (document.location.protocol == "https:" ? "https://secure" : "http://edge") + ".quantserve.com/quant.js";
           elem.async = true;
           elem.type = "text/javascript";
           var scpt = document.getElementsByTagName('script')[0];
           scpt.parentNode.insertBefore(elem, scpt);
          })();
        </script>
        <script type="text/javascript">
            (function(){
                window._pxAppId ='PXiUbOGhJL';
                // Custom parameters
                // window._pxParam1 = "<param1>";
                var p = document.getElementsByTagName('script')[0],
                    s = document.createElement('script');
                s.async = 1;
                s.src = '//client.perimeterx.net/PXiUbOGhJL/main.min.js';
                p.parentNode.insertBefore(s,p);
            }());
        </script>
        <noscript>
            <div style="position:fixed; top:0; left:0; display:none" width="1" height="1">
                <img src="//collector-PXiUbOGhJL.perimeterx.net/api/v1/collector/noScript.gif?appId=PXiUbOGhJL">
            </div>
        </noscript>
                <script type="text/javascript">
            (function(){
                var p = document.getElementsByTagName('script')[0], s = document.createElement('script');
                s.async = 1;
                s.src = '//client.forcenock.com/client.js';
                p.parentNode.insertBefore(s,p);
            }());
        </script>
                <script type="text/javascript">
            window.vms_features={'geo_blocker':1,'login_security_headers':1,'ads_da_safeframe':1,'ads_tideline_floors':1,'ads_970x250_auction':1,'forcenock':1,'faq_redirect':1,'pci_compliance':1,'pci_tokenizer':1,'ads_campaign_bidder':1};
            function vms_feature(feature) {
                return (feature in window.vms_features);
            }
                        function is_beta() {
                return false;
            }
        </script>
        
<link rel="stylesheet" type="text/css" href="https://st.deviantart.net/css/login_lc.css?876639996" media="Screen,Projection,TV,Print">
<link rel="stylesheet" type="text/css" href="https://st.deviantart.net/css/v6core_lc.css?149744120" media="Screen,Projection,TV,Print">
<link rel="stylesheet" type="text/css" href="https://st.deviantart.net/css/deviantart-network_lc.css?2070704155" media="Screen,Projection,TV,Print"><link rel="apple-touch-icon" sizes="180x180" href="https://st.deviantart.net/minish/touch-icons/ios-180.png">
<link rel="apple-touch-icon" sizes="152x152" href="https://st.deviantart.net/minish/touch-icons/ios-152.png">
<link rel="apple-touch-icon" sizes="120x120" href="https://st.deviantart.net/minish/touch-icons/ios-120.png">
<link rel="apple-touch-icon" sizes="76x76" href="https://st.deviantart.net/minish/touch-icons/ios-76.png">
<link rel="icon" sizes="192x192" href="https://st.deviantart.net/minish/touch-icons/android-192.png">
<link rel="icon" sizes="144x144" href="https://st.deviantart.net/minish/touch-icons/android-144.png">
<link rel="icon" sizes="96x96" href="https://st.deviantart.net/minish/touch-icons/android-96.png">
<link rel="icon" sizes="48x48" href="https://st.deviantart.net/minish/touch-icons/android-48.png">
<meta name="application-name" content="DeviantArt">
            <link rel="mask-icon" href="https://st.deviantart.net/minish/touch-icons/touchbar-mark.svg" color="#05cc47">
        <link href="https://www.deviantart.com/users/login" rel="canonical">
<link rel="alternate" hreflang="en" href="https://www.deviantart.com/users/login">
<link rel="alternate" hreflang="x-default" href="https://www.deviantart.com/users/login">
<link href="https://i.deviantart.net/icons/da_favicon.ico" rel="shortcut icon">
<script type="text/javascript">window.__external_data={"css_served":[52,27],"css_url":"\/\/st.deviantart.net\/roses\/cssmin\/","css_cache":{"1":1530000373131,"4":1530000373131,"25":1530000373131,"27":1530000373131,"52":1530000373131,"55":1530000373131,"57":1530000373132,"60":1530000373132,"63":1530000373132,"66":1530000373132,"68":1530000373132,"71":1530000373132,"73":1530000373132,"75":1530000373132,"89":1530000373132,"91":1530000373132,"101":1530000373133,"103":1530000373133,"105":1530000373133,"107":1530000373133,"109":1530000373133},"css_idx":["admin\/kragle","admin","admin\/goals","blocks\/base","blocks","blocks\/critique","blocks\/freeform","blocks\/pointstransactiongummy","blocks\/poll","blocks\/message-module","blocks\/partials\/dropdown","blocks\/widgets\/bppform","blocks\/widgets\/comment-bubble","blocks\/group-created","blocks\/widgets\/comment-box","blocks\/contest","lib\/da\/btn-62deg","blocks\/group-notice","blocks\/stack","blocks\/bulletin","blocks\/status","blocks\/commission","blocks\/birthdaygummy","blocks\/badge","da\/common\/hub-demo","common","kits\/buttons","core","kits\/forms","kits\/tabs","kits\/loading","kits\/helper-classes","lib\/da\/popup","lib\/da\/modal","lib\/da\/toast","da\/torpedo\/torpedo","da\/torpedo\/freeform","da\/torpedo\/film","da\/torpedo\/gallections","da\/torpedo\/mini","da\/torpedo\/watch","da\/torpedo\/polls","da\/torpedo\/bands","da\/torpedo\/ads","da\/torpedo\/mlt","da\/report\/report","da\/gallerywidget\/gallerywidget","da\/twitch\/twitch-embed","da\/browse\/browse-options","da\/header\/header","da\/watch\/watch-editor","da\/common\/desktop","desktop","da\/navbar\/navbar","da\/facebook-live\/facebook-live-elnino","facebook-live","da\/hero\/hero","hero","da\/hero\/hero-modules","da\/join\/goals_mobile","join-mobile","da\/join\/verify-mobile","da\/join\/join","join","da\/join\/goals","da\/livethumb\/livethumb","livethumb","da\/mobile\/header","mobile-header","da\/mobile\/mobile-header-white","da\/help\/mobile","mobile-help","da\/notes\/mobile-notes","mobile-notes","da\/browse\/mobile-browse","mobile","da\/browse\/mobile","da\/sliding-overlay\/sliding-overlay","da\/sliding-panel\/sliding-panel","da\/common\/mobile","da\/login\/mobile-login","da\/common\/mobile-nav-element","da\/notification-center\/mobile-notification-center","da\/profile\/mobile-profile","da\/forum\/mobile-forum","da\/watch\/mobile-watch","da\/deviation\/mobile-deviation","da\/common\/mobile-agegate","da\/moderation\/moderation","moderation","da\/notification-center\/layout","notification-center","da\/notification-center\/messages","da\/notification-center\/sidebar","da\/notification-center\/mobile","lib\/da\/give-menu","lib\/da\/pagination","lib\/da\/selection","lib\/da\/drag-and-drop","lib\/da\/confirm","lib\/da\/peeky\/peek","peeky","da\/portal\/gogo","portal","da\/postbox\/postbox","postbox","da\/settings\/mobile-settings","settings","da\/twitch\/twitch-elnino","twitch"]};window.__pageload = {"initial_data":{"deviantart-object":{"username":null,"userid":null,"useruuid":null,"usericon":"https:\/\/a.deviantart.net\/avatars\/default.gif","features":["geo_blocker","login_security_headers","ads_da_safeframe","ads_tideline_floors","ads_970x250_auction","forcenock","faq_redirect","pci_compliance","pci_tokenizer","ads_campaign_bidder"],"browseadult":false,"loggedin":false,"showads":true,"isMobile":false,"isVM":false,"clickPaging":false,"verified":0,"authMode":null}},"metadata":{"0.0":{"bi":{"eventname":"login_screen_click_keep_logged_in","modal_version":1,"version":1}},"0.1":{"bi":{"eventname":"login_screen_click_login","modal_version":1,"version":1}},"0.2":{"bi":{"eventname":"login_screen_click_forgot_password","modal_version":1,"version":1}},"0.3":{"bi":{"eventname":"click_join","modal_version":1,"version":1}},"0.4":{"bi":{"eventname":"click_login","modal_version":1,"version":1}}}}</script>
<link type="text/css" rel="stylesheet" href="//st.deviantart.net/roses/cssmin/desktop.css?1530000373131">
<link type="text/css" rel="stylesheet" href="//st.deviantart.net/roses/cssmin/core.css?1530000373131">
<link type="text/css" rel="stylesheet" href="//st.deviantart.net/roses/rosa/commons.55e76556e96707d1483f.css"><script type="text/javascript">window.webpackManifest = {"0":"commons.55e76556e96707d1483f.js","1":"mobile.notification-centre.eaba42f1eef6f67d4c9c.js","2":"vendor.1b583e84f1b88c808ed8.js","3":"admin-saucepan.3e310f78cf9217e54ce5.js","4":"mobile.sink.fc4f837424cb00fdc102.js","5":"cart.0ec6bfa97e7eb7c316d4.js","6":"mobile.login.01af8724537b7f719734.js","7":"developer.3656cbd1baf98ba2611e.js","8":"login.60fcc6adeec4acaf81a7.js","9":"admin.3a0e61d10602474507e9.js","10":"sink.51988ceca707b83e70f3.js"};window.__wake = (function(){
            var q = [];
            window.__wp_q = q;
            return function(wake) {
                q.push(wake);
            }
        })();</script><script type="text/javascript">
if (!window.deviantART) deviantART = {};deviantART.deviant =[];
deviantART.pageData={"overlay_sponsor":1,"user":{"symbol":"","username":""},"ads":{"_acb":"1529651426","_ccb":"1527817692","atf_theater_970x550":1},"ga_hit":{"dimension4":0},"csrf":"MwcDbHXxN3OS48ct.paxrbz.mPLOKWz6ZDnvdo3XHbNND3MSBzL1cXO_ehWlPaaM8Vc","dwait_total":3};

</script>
<script type="text/javascript">(function(e){function a(e){var a=!1;try{a=JSON.parse(e.data),a.payload=JSON.parse(a.payload)}catch(n){}return a}function n(n){var r=a(n);if(r&&r._type&&"PubSubEvent"==r._type&&(u.push(r),e.PubSub&&e.PubSubCrossFrame)){t(!1);for(var s=0,o=u.length;o>s;s++)r=u[s],PubSubCrossFrame.publish(r.eventname,r.payload,r.quiet);if(e.vms_feature&&vms_feature("dre")){var b=u.slice().map(function(e){return{eventname:e.eventname,payload:(JSON.stringify(e.payload)+"").substr(0,100),quiet:e.quiet}});e.debug_pubsubcrossframe=function(){console.group("Accumulated PubSubCrossFrame events"),console.table(b),console.groupEnd()}}u=null}}function t(a){e.addEventListener?e[(a?"add":"remove")+"EventListener"]("message",n):e[(a?"detach":"attach")+"Event"]("onmessage",n)}var u=[];e.PubSubCrossFrame||t(!0)})(window);</script>
<script type="text/javascript">(function(t){var r=function(r){if(!r||"object"!=typeof r||r.nodeType||r==t)return!1;var n=Object.prototype.hasOwnProperty;try{if(r.constructor&&!n.call(r,"constructor")&&!n.call(r.constructor.prototype,"isPrototypeOf"))return!1}catch(o){return!1}var e;for(e in r);return void 0===e||n.call(r,e)},n=function(t){var o,e={};for(o in t)e[o]=r(t[o])?n(t[o]):t[o];return e},o=Array.isArray||function(t){return"[object Array]"===toString.call(t)},e={},l=/^.*\..*$/,i=function(t){if(t){if("function"==typeof t)throw"Glbl isn't meant to store functions, yo";if(o(t))for(var n=0,e=t.length;e>n;n++)i(t[n]);else if(r(t))for(var n in t)i(t[n])}};t.Glbl=function(t,o){if(1==arguments.length)return o=e[t],r(o)&&(o=n(o)),o;if("string"!=typeof t||!l.test(t))throw console.trace(),"Glbl keys need to be namespaced (dot-separated)";return i(o),r(o)&&(o=n(o)),e[t]=o,o},t.Glbl.dflt=function(t,n){if(r(t)){if(void 0!==n)throw"Misuse of Glbl, you cannot pass a dictionary AND a default value";for(var o in t)Glbl.dflt(o,t[o])}else void 0===Glbl(t)&&Glbl(t,n)},t.Glbl.plus=function(t,r){return 1===arguments.length&&(r=1),i(r),Glbl(t,(0|Glbl(t))+r)},t.Glbl.minus=function(t,r){return 1===arguments.length&&(r=1),i(r),Glbl(t,(0|Glbl(t))-r)},t.Glbl.match=function(t){var r={};for(var n in e)t.test(n)&&(r[n]=e[n]);return r},t.Glbl.del=function(t){void 0!==e[t]&&delete e[t]}})(window);</script>
<script type="text/javascript">window.deviantART||(window.deviantART={}),function(){var t=function(){};window.breakpoint||(window.breakpoint=t),window.console||(window.console={log:t,trace:t,info:t,warn:t}),window.ddt||(window.ddt={log:t,info:t,warn:t,watch:t,unwatch:t,watching:t,alert:t,error:t})}(),DWait={X:[],L:{},T:{},R:{},D:"jms/dwait/download.js",MC:((window.deviantART||{}).pageData||{}).dwait_total||0,ready:function(t,i){var e,a;for(t instanceof Array||(t=[t]),i instanceof Array||(i=[this,i]),"string"==typeof i[1]&&(i[1]=Function(i[1])),a={},e=0;e!=t.length;e++)a[t[e]]=1;for(DWait.X.push({m:a,c:i}),DWait.fire(),e=0;e!=t.length;e++)DWait._trip(t[e])},_trip:function(t){if(!DWait.L[t]&&DWait.T[t]&&t!=DWait.D){var i=DWait.T[t];DWait.ready(DWait.D,function(){DWait.download(i,function(){DWait.unroll(i)})})}},count:function(){--DWait.MC||setTimeout(function(){DWait.run(".allready")},1)},fire:function(){var t,i,e,a;for(t=0;e=this.X[t];t++){a=!1;for(i in e.m){if(!this.L[i]){a=!0;break}delete e.m[i]}if(!a){this.X.splice(t,1),t--,e.c[1].apply(e.c[0],(e.c[2]||[]).concat([this.L[i]])),setTimeout(function(){DWait.fire()},1);break}}},run:function(t,i,e){t.indexOf("__")>0&&console.log("[DWait] Running branched file: ",t),this.L[t.replace(/__([A-Za-z0-9_-]+)/,"")]=i||1,this.T[t]&&!e&&this.unroll(this.T[t],i),DWait.fire()},unroll:function(t,i){t instanceof Array||(t=[t]);var e,a,n,r,o;for(e=0;a=t[e];e++){if(a=a.replace(/\?.*/,""),n=this.R[a])for(r=0;o=n[r];r++)!this.L[o]&&this._is_css.test(o)&&this.run(o,i,!0);delete this.R[a]}},_is_css:/\.css$/,N:[],readyLink:function(t,i,e){var a;if(window.event&&(window.event.cancelBubble=!0),e instanceof Array||(e=[i,e,[]]),!this.L[t]){for(a=0;a!=this.N.length;a++)if(this.N[a]==i)return!1;this.N.push(i),i.style.cursor="wait",i.className+=" active",this.ready(t,[this,this.fixLink,[i]])}if(this.ready.call(i,t,e),i.blur)try{i.blur()}catch(n){}return!1},linkCheck:function(){return!0},fixLink:function(t){var i;for(t.style.cursor="pointer",t.className=t.className.replace(/\bactive\b/,""),i=0;i!=this.N.length;i++)if(this.N[i]==t)return this.N.splice(i,1)},bind:function(t,i,e){return[t,i,e]},loadDownloadMap:function(t){this.T=t,this.retrip()},loadRollupMap:function(t){this.R=t},retrip:function(){for(var t=this.X.length;t--;)for(var i in this.X[t].m)this._trip(i)},replay:function(){DWait.replay.active=!0;try{$(document.body).on("click.dwait",function(t){t.preventDefault()}).find(".dwaiting").removeClass("dwaiting active").gmiWake().click().end().off("click.dwait")}finally{DWait.replay.active=!1}}},function(){function t(t){t=t||window.event;for(var a,r=t.target||t.srcElement,o=r,c=r,s=d+t.type,l=t.type&&"click"==t.type&&!(DWait.replay&&DWait.replay.active);r&&!(r.getAttribute&&(a=r.getAttribute(s))||l&&/\bdwait\b/.test(r.className));)r=r.parentNode;var f=t.which&&t.which>1||t.button>0;if("click"===t.type&&!t.defaultPrevented&&t.returnValue!==!1&&(t.altKey||t.ctrlKey||t.metaKey||t.shiftKey||f))do if("A"===c.nodeName){if(c.href&&0!==c.href.indexOf("javascript:")&&"#"!==c.href)return!0;break}while(c=c.parentNode);var u=function(){t.stopPropagation?t.stopPropagation():t.cancelBubble=!0,t.preventDefault?t.preventDefault():t.returnValue=!1};return a?r.dwait_init_done?!0:i(a,r)?(u(),r.dwait_init_id?"click"==t.type&&(n[r.dwait_init_id]=o):e(a,r,t),!1):(r.dwait_init_done=!0,!0):l&&r&&/\bdwait\b/.test(r.className)?(/\bdwaiting\b/.test(o.className)||(o.className+=" active dwaiting"),void 0===DWait.replay.active&&(DWait.replay.active=!1,DWait.ready([".jqready",".allready"],DWait.replay)),u(),!1):!0}function i(t,i){if(o[t])for(var e=0;o[t].length>e;e++)if(!o[t][e].call(i,i,t))return!1;return!0}function e(t,i,e){function a(){if(r[t])for(var e=0;r[t].length>e;e++)r[t][e].call(i,i,l);i.dwait_init_done=!0,i.className=i.className.replace(/\binit-waiting\b/,"");var a=n[i.dwait_init_id];a&&a.click&&setTimeout(function(){a.click()},1)}var o=s++,c=i.getAttribute(d+"deps"),l=e&&e.type?e.type:"domready";return i.dwait_init_id=o,"click"==l&&(n[o]=e.target||e.srcElement),c&&c.split?(c=c.split(","),i.className+=" init-waiting",DWait.ready(c,a),void 0):(a(),void 0)}var a,n={},r={},o={},c=["click","mouseover"],d="data-dwait-",s=1;for(DWait.init=function(t,i,e){r[t]||(r[t]=[]),r[t].push(i),"function"==typeof e&&(o[t]||(o[t]=[]),o[t].push(e))},DWait.init_domready=function(t){$("[data-dwait-domready]",t).each(function(){this.dwait_init_done||this.dwait_init_id||!this.getAttribute||e(this.getAttribute("data-dwait-domready"),this)})},DWait.ready(".domready",function(){DWait.init_domready()}),a=0;c.length>a;a++)document.addEventListener?document.addEventListener(c[a],t,!1):document.attachEvent&&document.attachEvent("on"+c[a],t)}();</script>
<script type="text/javascript">(function(t){if(t.console){var n="ddt_watch",e=" ;path=/ ;domain=."+t.location.host.split(".").slice(-2).join("."),r=["sta.sh","deviantart.com"],a=RegExp("^(https?:)?\\/\\/([^.]+\\.)?("+r.join("|").replace(".","\\.")+")\\b","i"),o=t.parent!==t,i={},s={},d=128,c={get:function(){var t=RegExp("(?:^|; )"+encodeURIComponent(n)+"=([^;]+)"),e=t.exec(document.cookie);return e?(decodeURIComponent(e[1])+"").split(","):[]},set:function(){var n=[];for(var e in i)n.push(e);var a,o=n.join(",");if(!vms_feature("qunit"))for(var s=0;r.length>s;s++){var d=r[s];"deviantart.com"==d&&(d="www.deviantart.com"),a=new Image,a.src="https://"+d+"/ddt/?channels="+o+"&cb="+(new Date).getTime()}c.sync(++t.ddt.version)},del:function(){var r=new Date;r.setTime(r.getTime()-864e5),document.cookie=encodeURIComponent(n)+"=; expires="+r.toUTCString()+e,c.sync(++t.ddt.version)},sync:function(){}};if(t.postMessage)var l=setInterval(function(){t.DWait&&(clearInterval(l),DWait.ready("jms/lib/jquery/jquery.current.js",function(){c.sync=function(n){var e=JSON.stringify({ddt:!0,version:n,channels:i}),r="*",s=$("iframe[src]");s.filter(function(){return a.test(this.src)&&this.contentWindow.ddt&&this.contentWindow.ddt.version!==n}),s.length&&(ddt.log("ddt","syncing channels down",s.length,"frames found"),s.each(function(){this.contentWindow.postMessage(e,r)})),o&&t.parent.ddt.version!==n&&(ddt.log("ddt","syncing channels up"),t.parent.postMessage(e,r))},$(t).on("message.ddt",function(n){var e=n.originalEvent;if(e&&e.data&&e.origin&&a.test(e.origin)){var r;try{r=JSON.parse(e.data)}catch(o){}r&&r.ddt&&r.version&&t.ddt.version!==r.version&&(t.ddt.version=r.version,i=r.channels,ddt.log("ddt","updated watch list to","v"+r.version,"in",t.name,ddt.watching()),c.sync(r.version))}})}))},100);var u=function(t,n){if(n.indexOf("*")>=0){if(t.match(n.replace(/\./g,".").replace(/\*/,".*")))return!0}else if(t.toLowerCase()==n)return!0},f=function(t){return t in console||console.warn("[ddt] cannot proxy this method, it is not defined in console",t),function(n,e){s[n]=!0;var r;for(r in i)if(i.hasOwnProperty(r)&&u(n,r)){var a=Array.prototype.slice.call(arguments,1);return a[0]="["+n+"] "+e,console[t].apply(console,a)}}},g=function(t){return t.length>d&&(console.warn("[ddt] package name exceeds allowed length of "+d+" chars and is truncated to that length",t),t=t.substring(0,d)),t};t.ddt={version:0,log:f("log"),info:f("info"),warn:f("warn"),error:f("error"),trace:function(t){ddt.watching(t)&&(ddt.log.apply(ddt,arguments),console.trace())},list:function(t){var n=Object.keys(s);t&&(n=n.filter(function(n){return u(n,t)})),console.log(n)},createLogger:function(t){var n=function(t){var n=Array.prototype.slice.call(arguments,1);return function(){return t.apply(this,n.concat(Array.prototype.slice.call(arguments,0)))}};return{log:n(ddt.log,t),info:n(ddt.info,t),warn:n(ddt.warn,t),error:n(ddt.error,t),trace:n(ddt.trace,t),alert:n(ddt.alert,t)}},watch:function(n,e){if(!n)return!1;if(n instanceof Array){for(var r in n)t.ddt.watch(n[r],!0);c.set()}else{if(!/^[a-zA-Z0-9*.]+$/.test(n))return console.warn("[ddt] attempted to watch invalid package",n),!1;i[g(n.toLowerCase())]=!0,e||c.set()}return!0},unwatch:function(n,e){if(!n)return console.warn("[ddt] need a package name"),!1;if(n instanceof Array){for(var r in n)t.ddt.unwatch(n[r],!0);c.set()}else{if(!/^[a-zA-Z0-9*.]+$/.test(n))return console.warn("[ddt] attempted to watch invalid package",n),!1;n=g(n.toLowerCase());var a;for(a in i)i.hasOwnProperty(a)&&u(a,n)&&delete i[a];e||c.set()}return!0},watching:function(t){if(t){var n;for(n in i)if(i.hasOwnProperty(n)&&u(t,n))return!0}var e=[];for(var r in i)e.push(r);return e},alert:function(n,e,r,a,o){ddt.warn(n,e,r),DWait.ready(["jms/lib/jquery/jquery.current.js","jms/lib/difi.js"],function(){$.isPlainObject(r)||(r&&console.warn("[ddt] alert data is not a plain object",r),r={}),o!==!1&&(r=$.extend({},r||{}),$.extend(r,{url:t.top.location.href,useragent:navigator.userAgent}),(t.deviantART||{}).deviant&&$.extend(r,{username:deviantART.deviant.username,symbol:deviantART.deviant.symbol,userid:deviantART.deviant.id,features:deviantART.deviant.features})),"string"==typeof a&&(a=[a]),"string"!=typeof e&&(console.warn("[ddt] alert message is not a string",e),e=""+e),DiFi.pushPost("Logr","logr",["js",e,r,a||[]],$.noop),DiFi.send()})}};var v=c.get();v&&(v instanceof Array&&v.length?(t.ddt.watch(v),o||console.log("[ddt] watching",t.ddt.watching())):c.del())}})(window);</script>
<script type="text/javascript">var d=(window.location.hash||"").match(/^#\/d([a-z0-9]+)$/);d&&(window.location=document.location.protocol+"//www.deviantart.com/deviation/"+parseInt(d[1],36));</script>
<script type="text/javascript">document.write('<style type="text/css">div.unscripted, span.unscripted {display:none} div.scripted, div.unlock div.unscripted {display:block} span.scripted, div.unlock span.unscripted {display:inline}</style>');</script><style type="text/css">div.unscripted, span.unscripted {display:none} div.scripted, div.unlock div.unscripted {display:block} span.scripted, div.unlock span.unscripted {display:inline}</style>
<script type="text/javascript">window.autobob={error:function(i){var e=i.target||i.srcElement;e.style.visibility="hidden";var t=e.src,s=parseInt(e.retries||0,10)+1;t=(-1==t.indexOf("?")?t:t.substr(0,t.length-(s-1+"").length-1))+"?"+s,(!window.isVM||vms_feature("vm_fileserving"))&&6>s?(e.retries=s,setTimeout(function(){(e.readyState||e.complete)&&(e.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==",e.style.visibility="visible"),e.src=t},3500)):(e.autobob_done=!0,e.style.visibility="visible")},load:function(i){var e=i.target||i.srcElement;e.style.visibility="visible"}};</script>
<script type="text/javascript">OHM_ON=150,OHM_OFF=300,OHM={disabled:!1,sync:function(e,t){var a;return(a=OHM.getLink(e.target||e.srcElement))?(t||(a=null),OHM.disabled?!1:(OHM.want=a,OHM.timer&&clearTimeout(OHM.timer),navigator.userAgent.indexOf("Mobile")>=0?OHM.hit("m",e.target):OHM.timer=setTimeout(OHM.hit,OHM.got&&a?10:a?OHM_ON:OHM_OFF),void 0)):!1},hit:function(e,t){if(delete OHM.timer,OHM.want&&"m"==e){for(;!t.href&&(t=t.parentNode););t&&!t.onclick&&(t.className||"").match(/\bmi\b/)&&(window.location.href=t.href)}OHM.want!=OHM.got&&(OHM.got&&(OHM.got.className=OHM.got.className.replace(/\s*\bmmhover{1,2}\b/g,""),window.Popup2&&Popup2.showOverlayElements(),window.PubSub&&PubSub.publish("OHM.hit",{id:OHM.got.id,on:0})),OHM.got=OHM.want,OHM.got&&window.Popup2&&(OHM.got.className+=" mmhover",window.PubSub&&PubSub.publish("OHM.hit",{id:OHM.got.id,on:1})))},getLink:function(e){for(;("DIV"!=e.tagName&&"TD"!=e.tagName||!((e.className||"").indexOf("oh-ml")>=0||(e.className||"").indexOf("oh-hashover")>=0||(e.className||"").indexOf("oh-hasbutton")>=0)||!((document.body.className||"").indexOf("no-apps")>=0||(document.body.className||"").indexOf("stash-submit-page")>=0||0>(e.className||"").indexOf("oh-mmain")))&&(e=e.parentNode);)if("H1"==e.tagName)return 0;return e}};</script>
<script type="text/javascript">(function(){var e=function(e){e=e||window.event;var t=((e.data+"").match(/^hptointer:(.*)$/)||[]).pop();switch(t){case"inter_activate":inter_activate();break;case"hpto_activate":hpto_activate();break;case"inter_dismiss":inter_dismiss();break;case"hpto_dismiss":hpto_dismiss()}},t=0,n=function(){return void 0!==window.pageYOffset?window.pageYOffset:(document.documentElement||document.body.parentNode||document.body).scrollTop},i=function(e){window.scrollTo(0,e||0)};window.hptointer_on=function(e){t=t||n(),i(0),document.body.className+=" inter-active";var o=document.getElementById(e);o&&(o.style.display="block")},window.hptointer_off=function(e,n){n&&n.preventDefault();var o=document.getElementById(e);return o&&(o.style.display="none",o.innerHTML="",document.body.className=document.body.className.replace(/inter-active/g,"")),window.event&&(event.cancelBubble=!0),i(t),!1},window.inter_activate=function(){return hptointer_on("inter")},window.inter_dismiss=function(e){return hptointer_off("inter",e)},window.hpto_activate=function(){return hptointer_on("hpto")},window.hpto_dismiss=function(e){return hptointer_off("hpto",e)},window[window.addEventListener?"addEventListener":"attachEvent"]("message",e),DWait.ready("jms/lib/pubsub.js",function(){var e={};PubSub.subscribe({eventname:"Duperbrowse.opened",subscriber:e,callback:function(){hptointer_off("hpto"),PubSub.unsubscribe({eventname:"Duperbrowse.opened",subscriber:e})}})})})();</script><script type="text/javascript">if (window.DWait) {DWait.run('jms/lib/pubsubcrossframe.fastcall.js');DWait.run('jms/lib/glbl.js');DWait.run('jms/dwait/dwait.js');DWait.run('jms/lib/ddt.js');DWait.run('jms/chrome/deviation_redirect.js');DWait.run('jms/lib/unscripted.js');DWait.run('jms/chrome/autobob.js');DWait.run('jms/chrome/overhead_mouse.js');DWait.run('jms/chrome/interstitial.js');}</script><script type="text/javascript">
            DWait.loadRollupMap({"https:\/\/st.deviantart.net\/css\/flatpickr_lc.css":["cssms\/lib\/flatpickr.css"],"https:\/\/st.deviantart.net\/css\/login_lc.css":["cssms\/pages\/login.css","cssms\/pages\/rockedout.css"],"https:\/\/st.deviantart.net\/css\/v6core_lc.css":["cssms\/pages\/lesport.css","cssms\/lib\/deviantsymbols.css","cssms\/lib\/logo.css","cssms\/chrome\/depthsv7.css","cssms\/pages\/misc\/tabselect.css","cssms\/chrome\/nav.css","cssms\/pages\/critique-deviation.css","cssms\/pages\/critique-itempage.css","cssms\/pages\/deviation\/groupmenu.css","cssms\/pages\/deviation\/journals.css","cssms\/pages\/deviation\/literature.css","cssms\/pages\/devwatch-editor.css","cssms\/pages\/deviationmanage.css","cssms\/pages\/browse\/browse.css","cssms\/pages\/browse\/browse-buttons.css","cssms\/pages\/browse\/browse-journals.css","cssms\/pages\/browse\/newbrowse.css","cssms\/pages\/ad-square.css","cssms\/pages\/ad-da-custom.css","cssms\/pages\/ad-download.css","cssms\/pages\/ad-top.css","cssms\/pages\/ad-mc.css","cssms\/pages\/ad-gruser.css","cssms\/pages\/ad-core.css","cssms\/pages\/ad-yahoo.css","cssms\/minish\/hpto.css","cssms\/pages\/misc\/gmbutton.css","cssms\/topsecret\/contests.css","cssms\/lib\/thumbnails-news.css","cssms\/lib\/thumbnails-portal.css","cssms\/lib\/commentcount.css","cssms\/lib\/thumbnails-cropthumb.css","cssms\/pages\/deviation\/deviation-favebtn.css","cssms\/pages\/deviation\/deviation-megabag.css","cssms\/pages\/deviation\/deviation-collect.css","cssms\/pages\/deviation\/deviation-more.css","cssms\/pages\/deviation\/deviation-iconcommentstats.css","cssms\/pages\/browselikethis\/browselikethis.css","cssms\/pages\/browselikethis\/browselikethis.top_bar.css","cssms\/pages\/browselikethis\/browselikethis.left_bar.css","cssms\/pages\/browselikethis\/browselikethis.results.css","cssms\/pages\/browselikethis\/browselikethis.deviation_full_views.css","cssms\/pages\/browselikethis\/browselikethis.groups.css","cssms\/pages\/browselikethis\/browselikethis.shop.css","cssms\/pages\/browselikethis\/browselikethis.ads.css","cssms\/pages\/browselikethis\/browselikethis.search_input.css","cssms\/pages\/discoverytag\/discoverytag.css","cssms\/pages\/discoverytag\/discoverytag.right_bar.css","cssms\/pages\/challenge\/challenge.css","cssms\/pages\/agegate.css","cssms\/pages\/newbies.css"],"https:\/\/st.deviantart.net\/css\/deviantart-network_lc.css":["cssms\/lib\/survival.css","cssms\/lib\/nudge.css","cssms\/lib\/thumbnails.css","cssms\/lib\/thumbnails-200H.css","cssms\/lib\/thumbnails-stream.css","cssms\/lib\/thumbnails-maturefilter.css","cssms\/lib\/thumbnails-lit.css","cssms\/lib\/thumbnails-journal.css","cssms\/lib\/thumbnails-film.css","cssms\/lib\/thumbnails-misc.css","cssms\/lib\/thumbnails-profilecard.css","cssms\/lib\/thumbnails-stack.css","cssms\/lib\/deviation-placeholders.css","cssms\/lib\/maturefilter.css","cssms\/lib\/icons.css","cssms\/lib\/popup.css","cssms\/lib\/shadows.css","cssms\/lib\/modals.css","cssms\/modals\/purchase-modal.css","cssms\/pages\/misc\/gmbutton2.css","cssms\/pages\/misc\/smbutton.css","cssms\/pages\/grusers\/boxes.css","cssms\/modals\/signup-modal.css","cssms\/pages\/verify.css","cssms\/pages\/misc\/subble.css","cssms\/pages\/misc\/quicktip.css","cssms\/pages\/misc\/buttons.css","cssms\/pages\/comment-box.css","cssms\/pages\/misc\/megamisc.css","cssms\/pages\/deviation\/paddles-ab.css","cssms\/pages\/misc\/iconbar.css","cssms\/topsecret\/quiet-comments.css","cssms\/pages\/talkpost.css","cssms\/lib\/pager.css","cssms\/lib\/da.misc.widgets.pager.css","cssms\/pages\/deviation\/deviation-misc.css","cssms\/pages\/deviation\/deviation-devlinkzone.css","cssms\/pages\/deviation\/deviation-stash.css","cssms\/pages\/deviation\/deviation-group.css","cssms\/pages\/deviation\/deviation-share.css","cssms\/pages\/deviation\/deviation-ch.css","cssms\/pages\/deviation\/deviation-artistcomments.css","cssms\/pages\/deviation\/deviation-sparta.css","cssms\/pages\/deviation\/deviation-boxtop.css","cssms\/pages\/deviation\/deviation-popup2.css","cssms\/pages\/deviation\/deviation.css","cssms\/pages\/deviation\/dev-page-view-common.css","cssms\/pages\/deviation\/dev-page-view-right-bar.css","cssms\/pages\/deviation\/dev-page-view-about.css","cssms\/pages\/deviation\/dev-page-view-challenge.css","cssms\/font\/calibre.css","cssms\/chrome\/overhead.css","cssms\/chrome\/body-misc.css","cssms\/chrome\/v6-legacy.css","cssms\/chrome\/navbar.css","cssms\/pages\/writeranywhere.css","cssms\/lib\/writer-overrides.css"],"https:\/\/st.deviantart.net\/css\/v6loggedin_lc.css":["cssms\/lib\/drag.css","cssms\/minish\/lub.css","cssms\/pages\/misc\/progressbar.css","cssms\/pages\/grusers\/editmode.css","cssms\/pages\/grusers\/editmode-modal.css","cssms\/lib\/jquery\/jquery.select.pager.css"],"https:\/\/st.deviantart.net\/css\/notes-modal_lc.css":["cssms\/pages\/deviation\/note-modal.css"],"https:\/\/st.deviantart.net\/css\/writer_lc.css":["cssms\/lib\/writer.css","cssms\/lib\/writer-imagecontrols.css","cssms\/lib\/writer-toolbar.css","cssms\/lib\/writer-subtoolbar.css","cssms\/lib\/autocomplete.css"],"https:\/\/st.deviantart.net\/css\/stash_lc.css":["cssms\/pages\/stash\/stash.css","cssms\/pages\/stash\/stash.header.css","cssms\/pages\/stash\/stash.folder.css","cssms\/pages\/stash\/stash.uploadzone.css","cssms\/pages\/stash\/stash.auth.css","cssms\/pages\/stash\/stash.upgrade.css","cssms\/pages\/stash\/stash.thumb.dnd.css","cssms\/pages\/stash\/stash.thumb.hover.css","cssms\/pages\/stash\/stash.thumb.upload.css","cssms\/pages\/stash\/stash.thumb.stack.css","cssms\/pages\/stash\/stash.override.comments.css","cssms\/pages\/stash\/stash.override.deviation.css","cssms\/pages\/stash\/stash.override.global.css","cssms\/pages\/stash\/stash.override.header.css","cssms\/pages\/stash\/stash.override.modal.css","cssms\/pages\/stash\/stash.override.pagination.css","cssms\/pages\/stash\/stash.override.thumbs.css","cssms\/pages\/writeranywhere\/stashdesc.css","cssms\/pages\/writeranywhere\/stackdesc.css","cssms\/pages\/ile\/ile.shares.with.stash.css"],"https:\/\/st.deviantart.net\/css\/sidebar_lc.css":["cssms\/lib\/writer-sidebar.css"]});
            </script>
<script type="text/javascript">
                if (window.DWait) {
                    for (var dwaitrollup in {"https:\/\/st.deviantart.net\/css\/login_lc.css":true,"https:\/\/st.deviantart.net\/css\/v6core_lc.css":true,"https:\/\/st.deviantart.net\/css\/deviantart-network_lc.css":true}) {
                        DWait.unroll(dwaitrollup);
                    }
                }
                </script>
<script type="text/javascript">DWait.ready(["jms\/lib\/glbl.js"], function(){ Glbl('deviantART.user_agreed_to_submission_policy', false); Glbl('StashUploader.image_size_limit', 83886080); Glbl('StashUploader.default_size_limit', 209715200); Glbl('StashUploader.text_size_limit', 65500);  });</script>

<script type="text/javascript" src="https://st.deviantart.net/css/deviantart-safeframes-host_jc.js?1506201991" charset="utf-8"></script>        <script type="text/javascript">
            var googletag = window.googletag || {};
            googletag.cmd = googletag.cmd || [];
            (function() {
                var gads = document.createElement('script');
                gads.async = true;
                gads.type = 'text/javascript';
                var useSSL = 'https:' == document.location.protocol;
                gads.src = (useSSL ? 'https:' : 'http:') +
                    '//www.googletagservices.com/tag/js/gpt.js';
                var node = document.getElementsByTagName('script')[0];
                node.parentNode.insertBefore(gads, node);
            })();
        </script>
                <script type="text/javascript">
            googletag = window.googletag || {};
            googletag.cmd = googletag.cmd || [];
            var pageConfig = {
                allowOverlayExpansion: true,
                allowPushExpansion: true,
                sandbox: true
            };
            googletag.cmd.push(function() {
                googletag.pubads().setSafeFrameConfig(pageConfig);
                googletag.pubads().enableSingleRequest();
                googletag.pubads().disableInitialLoad();
                googletag.pubads().set("adsense_channel_ids", "5709798815");
googletag.pubads().set("google_hints", "graphic design,web design,gaming,photography,animation,comic books,digital images");
googletag.pubads().set("google_kw", "graphic design,web design,gaming,photography,animation,comic books,digital images");
googletag.pubads().set("google_kw_type", "broad");
                googletag.pubads().setTargeting("LoggedIn", "No");
googletag.pubads().setTargeting("dailyimp", "1");
googletag.pubads().setTargeting("sessimp", "1");
googletag.pubads().setTargeting("section", "other");
                        (function() {
            googletag = window.googletag || {};
            googletag.cmd = googletag.cmd || [];
            var gpt_slot = googletag.defineSlot("1008370\/ca-pub-2005626271413567\/atf_theater_970x550_v6_interstitial", [970,550], "ad-atf-theater-970x550-25631-safe");
            if (!gpt_slot) {
                (ddt || {log:function(){}}).log('ads.safeframe.gpt', 'GPT defined slot is null', '"1008370\/ca-pub-2005626271413567\/atf_theater_970x550_v6_interstitial"');
                return;
            }
            gpt_slot.setForceSafeFrame(true);
            gpt_slot.addService(googletag.pubads());
            DWait.ready(['jms/lib/pubsub.js'], function() {
                PubSub.publish('DaAds.defineSlot', {target: "ad-atf-theater-970x550-25631-safe", slot: gpt_slot, path: "1008370\/ca-pub-2005626271413567\/atf_theater_970x550_v6_interstitial"}); 
            });
        })();                googletag.enableServices();
            });

        </script>
        
<style type="text/css"></style><script type="text/javascript" src="https://st.deviantart.net/css/sidebar_jc.js?4079037041" charset="utf-8"></script><script type="text/javascript" src="https://st.deviantart.net/css/menusys_jc.js?302537211" charset="utf-8"></script><script type="text/javascript" src="https://st.deviantart.net/css/stashwriter_jc.js?2119410682" charset="utf-8"></script><script type="text/javascript" src="https://st.deviantart.net/css/art-common_jc.js?158549310" charset="utf-8"></script><link type="text/css" rel="stylesheet" href="https://st.deviantart.net/css/sidebar_lc.css?1490570941"><script type="text/javascript" src="https://st.deviantart.net/css/jquery.ui_jc.js?3105002537" charset="utf-8"></script><script type="text/javascript" src="https://st.deviantart.net/css/jquery.scrollto_jc.js?1893097371" charset="utf-8"></script><script type="text/javascript" src="https://st.deviantart.net/css/dragger_jc.js?939552279" charset="utf-8"></script><script type="text/javascript" src="https://st.deviantart.net/css/jquery-extras_jc.js?2736332723" charset="utf-8"></script><script type="text/javascript" src="https://st.deviantart.net/css/header_jc.js?3114820648" charset="utf-8"></script><link type="text/css" rel="stylesheet" href="https://st.deviantart.net/css/writer_lc.css?3090682151"><script type="text/javascript" src="https://st.deviantart.net/css/more7_jc.js?3272996614" charset="utf-8"></script><script type="text/javascript" src="https://st.deviantart.net/css/v6loggedin_jc.js?3166703289" charset="utf-8"></script><script type="text/javascript" src="https://st.deviantart.net/css/deviantart-network-loggedin_jc.js?1438447932" charset="utf-8"></script></head>
<body id="deviantART-v7" class="secure no-apps maturefilter loggedout maturehide w960 deviantart withad">

<script type="text/javascript" async="" src="https://st.deviantart.net/css/deviantart-network_jc.js?4020007780" charset="utf-8"></script>
<script type="text/javascript" async="" src="https://st.deviantart.net/css/v6core_jc.js?1776206977" charset="utf-8"></script><header data-dwait-domready="LoginBar.init_touch" data-dwait-deps="jms/chrome/loginbar.js" id="overhead-collect" class=" "><table id="overhead" data-header-refresh="DAWebpageHeader;getHeaderHTML" onmouseover="OHM.sync(arguments[0]||event,1)" onmouseout="OHM.sync(arguments[0]||event,0)"><tbody><tr><td class="oh-keep"></td>    <td class="oh-hasmenu oh-mmain oh-eax">
                <a id="da-h1" href="https://www.deviantart.com" title="DeviantArt" data-ga_click_event="{&quot;category&quot;:&quot;HeaderNav&quot;,&quot;action&quot;:&quot;click_logo&quot;,&quot;nofollow&quot;:0}">
                <span id="deviantart-logo">
        <span class="mark">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 100 167" preserveAspectRatio="xMidYMid meet">
                <path class="wikistick" d=" M100 0 L99.96 0 L99.95 0 L71.32 0 L68.26 3.04 L53.67 30.89 L49.41 33.35 L0 33.35 L0 74.97 L26.40 74.97 L29.15 77.72 L0 133.36 L0 166.5 L0 166.61 L0 166.61 L28.70 166.6 L31.77 163.55 L46.39 135.69 L50.56 133.28 L100 133.28 L100 91.68 L73.52 91.68 L70.84 89 L100 33.33 "></path>
                <image src="//st.deviantart.net/minish/main/logo/logo-mark.png">             </image></svg>
        </span>
        <span class="type">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="2 25 80 22" preserveAspectRatio="xMidYMid meet">
                <g>
                    <path class="logotype" d="M7.29,42.78300094604492l1.07,-2.05l0.5,2.05L7.29,42.78300094604492ZM7.39,37.04560089111328l-4.89,9.34l2.9,0l0.71,-1.36l3.28,0l0.32,1.36l2.9,0l-2.24,-9.34L7.39,37.04560089111328L7.39,37.04560089111328Z M17.18,41.412899017333984l-1.24,0L15.94,39.52399826049805l1.24,0c0.76,0,1.04,0.38,1.04,0.95C18.22,41.04,17.93,41.41,17.18,41.412899017333984M21.06,40.43009948730469c0,-2.15,-1.45,-3.38,-3.49,-3.38l-4.43,0l0,9.34l2.79,0l0,-2.72l0.68,0l0.56,0l1.35,2.73l2.9,0l-1.73,-3.3C20.55,42.55,21.06,41.64,21.06,40.43009948730469 M21.47,37.04560089111328L21.47,39.52210235595703L24.12,39.52210235595703L24.12,46.38869857788086L25.71,46.38869857788086L26.91,44.090301513671875L26.91,39.52210235595703L29.3,39.52210235595703L30.6,37.04560089111328Z M37.13,25.61210060119629L39.92,25.61210060119629L39.92,34.95389938354492L37.13,34.95389938354492L37.13,25.61210060119629Z M13.38,25.61669921875l-1.25,2.4l0.77,0c1.38,0,2.25,0.88,2.25,2.27c0,1.37,-0.87,2.25,-2.25,2.25l-0.87,0l0,-4.33l-2.7,5.16l0,1.59l3.76,0c3.03,0,4.81,-1.83,4.81,-4.67C17.89,27.53,16.23,25.73,13.38,25.61669921875 M18.63,34.954002380371094L25.76,34.954002380371094L25.76,32.47770309448242L21.42,32.47770309448242L21.42,31.46809959411621L25.41,31.46809959411621L25.41,29.097900390625L21.42,29.097900390625L21.42,28.088598251342773L25.76,28.088598251342773L25.76,25.612201690673828L18.63,25.612201690673828Z           M30.73,31.265302658081055L29.37,25.61210060119629L26.47,25.61210060119629L28.71,34.9536018371582L31.69,34.9536018371582L36.59,25.61210060119629L33.68,25.61210060119629Z M60.37,25.61140251159668L60.37,28.088102340698242L63.01,28.088102340698242L63.01,34.9546012878418L64.6,34.9546012878418L65.81,32.65629959106445L65.81,28.088102340698242L68.2,28.088102340698242L69.5,25.61140251159668Z M45.25,31.350000381469727l1.07,-2.05l0.49,2.05L45.25,31.350000381469727ZM45.36,25.61210060119629l-4.89,9.34l2.9,0l0.71,-1.36L47.35,33.59260177612305l0.32,1.36l2.9,0l-2.24,-9.34L45.36,25.61210060119629Z           M56.98,30.03729820251465L53.86,25.61210060119629L51.1,25.61210060119629L51.1,34.954002380371094L53.89,34.954002380371094L53.89,30.2096004486084L57.36,34.954002380371094L59.77,34.954002380371094L59.77,25.61210060119629L56.98,25.61210060119629Z"></path>
                </g>
                <image src="//st.deviantart.net/minish/main/logo/logo-type.png">             </image></svg>
        </span>
        <span class="text">Deviant Art</span>
    </span>
            <span style="font:6pt Verdana, sans-serif;letter-spacing:0;position:absolute;top:30px;left:80px;z-index:30;color:#93AA9B !important;">            </span>
        </a>
    </td>        <td class="oh-search">
            <form id="search7" method="get" action="https://www.deviantart.com/whats-hot/" data-ga_submit_event="{&quot;category&quot;:&quot;Search&quot;,&quot;action&quot;:&quot;Loginbar&quot;,&quot;label&quot;:&quot;browse&quot;}"><div id="search7-ctrl"><input name="section" value="" type="hidden"><input name="global" value="1" type="hidden"><i class="gmbutton2f search-l"></i><input class="gmbutton2 gmbutton2f" name="q" accesskey="s" type="text"><input class="gmbutton2searchcancel" type="button"><span class="scripted" style="_zoom:1"><a class="gmbutton2 gmbutton2f search-button" href="" data-ga_click_event="{&quot;category&quot;:&quot;Search&quot;,&quot;action&quot;:&quot;Loginbar&quot;,&quot;nofollow&quot;:0,&quot;label&quot;:&quot;browse&quot;}" onclick="this.parentNode.parentNode.parentNode.submit();if (window.event)event.cancelBubble=1;return false">Search<b></b></a></span><span class="unscripted"><input value="" type="submit"></span></div></form>        </td>
    <td id="oh-menu-shop" class="oh-hasmenu oh-hashover oh-keep "><div class="oh-menuctrl"><nav class="oh-menu"><ul class="oh-menu-list"><li class="oh-menu-list-item"><a href="https://shop.deviantart.com" data-ga_click_event="{&quot;category&quot;:&quot;Retail&quot;,&quot;action&quot;:&quot;shoplink_appbar&quot;,&quot;nofollow&quot;:0}" class="mi iconset-more"><i class="i2"></i> <b>Buy Art</b></a></li><li class="oh-menu-list-item">
            <a class="mi iconset-upsell" href="https://www.deviantart.com/core-membership/?point=loginbar" data-ga_click_event="{&quot;category&quot;:&quot;PremiumUpsell&quot;,&quot;action&quot;:&quot;Dropdown&quot;,&quot;nofollow&quot;:0}">
                <span class="user-symbol premium mark-small"></span> 
                Buy Core Membership            </a>
        </li>
        </ul></nav></div>
        <a class="oh-l oh-touch" href="https://shop.deviantart.com" data-ga_click_event="{&quot;category&quot;:&quot;Retail&quot;,&quot;action&quot;:&quot;shoplink_appbar&quot;,&quot;nofollow&quot;:0}">Shop</a></td>
        <td id="oh-menu-mobile" class="oh-keep"><a class="oh-l" href="https://forum.deviantart.com" data-ga_click_event="{&quot;category&quot;:&quot;HeaderNav&quot;,&quot;action&quot;:&quot;click_forum_app&quot;,&quot;nofollow&quot;:0}">Forum</a></td>    <td id="oh-menu-more" class="oh-keep oh-hasmenu oh-hashover">
            <div class="oh-menu-ctrl iconset-more">
        <nav class="oh-menu">
            <ul class="column oh-menu-list">
                        <li class="oh-menu-list-item">
        <a class="mi " href="https://groups.deviantart.com" appid="44" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;Groups&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i42"></i>Groups</a></li>                        <li class="oh-menu-list-item">
        <a class="mi " href="https://forum.deviantart.com/" appid="6" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;Forum&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i13"></i>Forum</a></li>                        <li class="oh-menu-list-item">
        <a class="mi join-link" href="https://www.deviantart.com/join?joinpoint=menu_app" appid="4" data-dwait-deps="jms/pages/signup.js" data-dwait-click="null" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;Chat&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i4"></i>Chat</a></li>                        <li class="oh-menu-list-item">
        <a class="mi " href="https://www.deviantart.com/journals/" appid="3" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;Journals&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i51"></i>Journals</a></li>                                        <li class="oh-menu-list-item">
        <a class="mi " href="https://wallpaper.deviantart.com/?q=" appid="34" onclick="this.href=this.href+((window.devicePixelRatio>=2)
                                        ?(window.devicePixelRatio*screen.width+'x'+window.devicePixelRatio*screen.height)
                                        :(screen.width+'x'+screen.height)); this.removeAttribute('onclick')" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;Wallpaper&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i40"></i>Wallpaper</a></li>                        <li class="oh-menu-list-item">
        <a class="mi " href="https://portfolio.deviantart.com" appid="42" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;MyPortfolio&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i45"></i>Portfolio</a></li>            </ul>
            <ul class="column oh-menu-list">
                        <li class="oh-menu-list-item">
        <a class="mi " href="https://www.deviantart.com/critiques/" appid="40" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;Critiques&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i44"></i>Critiques</a></li>                        <li class="oh-menu-list-item">
        <a class="mi " href="https://www.deviantart.com/newest/?q=special:critiquable" appid="41" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;Critiqueable&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i44"></i>Critiqueable</a></li>                        <li class="oh-menu-list-item">
        <a class="mi " href="https://www.deviantart.com/random/deviant" appid="23" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;RandomDeviant&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i28"></i>Random Deviant</a></li>                        <li class="oh-menu-list-item">
        <a class="mi " href="https://www.deviantart.com/random/deviation" appid="24" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;RandomDeviation&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i28"></i>Random Deviation</a></li>                        <li class="oh-menu-list-item">
        <a class="mi " href="https://www.deviantart.com/random/group" appid="45" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;RandomGroup&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i28"></i>Random Group</a></li>                                        <li class="oh-menu-list-item">
        <a class="mi " href="https://www.deviantartsupport.com/en/" appid="10" data-ga_click_event="{&quot;category&quot;:&quot;MoreMenu&quot;,&quot;action&quot;:&quot;HelpFAQ&quot;,&quot;label&quot;:&quot;dropdown&quot;}"><i class="i21"></i>Help &amp; FAQ</a></li>            </ul>
                    </nav>
    </div>
            <a class="oh-l oh-touch join-link" href="https://www.deviantart.com/join?joinpoint=header" data-dwait-deps="jms/pages/signup.js" data-dwait-click="null">More</a>
    </td>
            <td id="oh-menu-submit" class="oh-keep">
            <a class="oh-l join-link" href="https://www.deviantart.com/join?joinpoint=header">
                <span>Submit</span>
            </a>
        </td>
        <td class="oh-gap">&nbsp;</td><td id="oh-menu-join" class="oh-keep oh-hashover"><a class="oh-l join-link" href="https://www.deviantart.com/join" data-ga_click_event="{&quot;category&quot;:&quot;Join&quot;,&quot;action&quot;:&quot;LoginBar&quot;,&quot;nofollow&quot;:0}" data-sigil="click-event-tracker" data-meta="0.3">Join</a></td>
        <td id="oh-menu-join" class="oh-keep oh-hashover"><a class="oh-l" id="header-login-link" href="https://www.deviantart.com/users/login" data-ga_click_event="{&quot;category&quot;:&quot;LoginLink&quot;,&quot;action&quot;:&quot;LoginBar&quot;,&quot;nofollow&quot;:0}" data-sigil="click-event-tracker" data-meta="0.4">Login</a></td>
    </tr></tbody></table></header><div id="output">    <div class="bubbleview h login-page" style="min-height: 484px;">
        
        <div class="c pp h">

                
        
                  
        <form id="login" data-sigil="recaptcha-form" action="login.php" method="post" class="wide">
                    <input name="challenge" value="" autocomplete="off" type="hidden">                 
        <table cellspacing="5" align="center">
        <tbody><tr>
                    <td class="label-td">
                <label for="login_username">Username or Email</label>
                <input class="text" id="login_username" name="username" value="" autofocus="autofocus" type="text">
            </td>
                </tr>
        <tr>
            <td class="label-td">
                <label for="login_password">Password</label>
                 <input class="text" id="login_password" name="password" type="password">
            </td>
         </tr>
         <tr>
             <td>
                 <label class="remember-label" for="remember_me">
                     <input class="checkbox" id="remember_me" checked="checked" name="remember_me" value="1" style="vertical-align: baseline" data-sigil="click-event-tracker" data-meta="0.0" type="checkbox"> Stay logged in
                 </label>
             </td>
         </tr>
         <tr>
            <td>                <input class="smbutton smbutton-size-default smbutton-shadow smbutton-blue" data-sigil="click-event-tracker" data-meta="0.1" value="Log In" type="submit">
            </td>
         </tr>
         <tr>
            <td>
                                <a class="small-blue" href="https://www.deviantart.com/users/forgot" data-sigil="click-event-tracker" data-meta="0.2">Forgot Password or Username?</a>
            </td>
         </tr>
         </tbody></table>
                         <input name="validate_token" value="a9ad5e9fd8c7d778cdd0" autocomplete="off" type="hidden">
            <input name="validate_key" value="1530025631" autocomplete="off" type="hidden">
        <div style="clear:both"></div>
         </form>
        </div>
        <script type="text/javascript">DWait.ready(["jms\/lib\/pubsub.js",".domready"], function(){ PubSub.publish('BILogger.logEvent', {eventname: 'login_screen_loaded', modal_version: 1, version: 1}); });</script>
   </div>
        
     <footer id="depths">
        <div class="depths-inner">
            <div class="footer_copyright">©2021 DeviantArt.  All rights reserved</div>
                    <nav class="footer_tx_links">
            <ul class="footer-menu-list">
                            <li class="footer-menu-list-item">
                    <a href="https://about.deviantart.com/" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_about&quot;,&quot;nofollow&quot;:0}">About</a>
                </li>
                            <li class="footer-menu-list-item">
                    <a href="https://about.deviantart.com/contact/" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_contact&quot;,&quot;nofollow&quot;:0}">Contact</a>
                </li>
                            <li class="footer-menu-list-item">
                    <a href="https://www.deviantart.com/developers" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_developers&quot;,&quot;nofollow&quot;:0}">Developers</a>
                </li>
                            <li class="footer-menu-list-item">
                    <a href="https://deviantart.theresumator.com/apply/" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_careers&quot;,&quot;nofollow&quot;:0}">Careers</a>
                </li>
                            <li class="footer-menu-list-item">
                    <a href="https://welcome.deviantart.com/" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_site-tour&quot;,&quot;nofollow&quot;:0}">Site Tour</a>
                </li>
                            <li class="footer-menu-list-item">
                    <a href="https://www.deviantartsupport.com/en/" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_help&quot;,&quot;nofollow&quot;:0}">Help &amp; FAQ</a>
                </li>
                            <li class="footer-menu-list-item">
                    <a href="https://about.deviantart.com/advertising/" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_advertise&quot;,&quot;nofollow&quot;:0}">Advertise</a>
                </li>
                            <li class="footer-menu-list-item">
                    <a href="https://www.deviantart.com/core-membership/?point=footer" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_pm&quot;,&quot;nofollow&quot;:0}">Core Members</a>
                </li>
                            <li class="footer-menu-list-item">
                    <a href="https://about.deviantart.com/policy/etiquette/" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_etiquette&quot;,&quot;nofollow&quot;:0}">Etiquette</a>
                </li>
                            <li class="footer-menu-list-item">
                    <a href="https://about.deviantart.com/policy/privacy/" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_privacy&quot;,&quot;nofollow&quot;:0}">Privacy Policy</a>
                </li>
                            <li class="footer-menu-list-item">
                    <a href="https://about.deviantart.com/policy/service/" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_terms&quot;,&quot;nofollow&quot;:0}">Terms of Service</a>
                </li>
                            <li class="footer-menu-list-item">
                    <a href="https://about.deviantart.com/policy/copyright/" data-ga_click_event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;click_link_copyright&quot;,&quot;nofollow&quot;:0}">Copyright Policy</a>
                </li>
                        </ul>
        </nav>
            
        </div>
    </footer></div><!-- output -->    <div id="hpto" style="display:none;">
        <div class="header">
            <a href="#" onclick="hpto_dismiss(); return false;" class="continue dismiss">Click here to continue to DeviantArt</a>
            <a href="#" onclick="hpto_dismiss(); return false;" class="logo dismiss">    <span id="deviantart-logo">
        <span class="mark">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 100 167" preserveAspectRatio="xMidYMid meet">
                <path class="wikistick" d=" M100 0 L99.96 0 L99.95 0 L71.32 0 L68.26 3.04 L53.67 30.89 L49.41 33.35 L0 33.35 L0 74.97 L26.40 74.97 L29.15 77.72 L0 133.36 L0 166.5 L0 166.61 L0 166.61 L28.70 166.6 L31.77 163.55 L46.39 135.69 L50.56 133.28 L100 133.28 L100 91.68 L73.52 91.68 L70.84 89 L100 33.33 "></path>
                <image src="//st.deviantart.net/minish/main/logo/logo-mark.png">             </image></svg>
        </span>
        <span class="type">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="2 25 80 22" preserveAspectRatio="xMidYMid meet">
                <g>
                    <path class="logotype" d="M7.29,42.78300094604492l1.07,-2.05l0.5,2.05L7.29,42.78300094604492ZM7.39,37.04560089111328l-4.89,9.34l2.9,0l0.71,-1.36l3.28,0l0.32,1.36l2.9,0l-2.24,-9.34L7.39,37.04560089111328L7.39,37.04560089111328Z M17.18,41.412899017333984l-1.24,0L15.94,39.52399826049805l1.24,0c0.76,0,1.04,0.38,1.04,0.95C18.22,41.04,17.93,41.41,17.18,41.412899017333984M21.06,40.43009948730469c0,-2.15,-1.45,-3.38,-3.49,-3.38l-4.43,0l0,9.34l2.79,0l0,-2.72l0.68,0l0.56,0l1.35,2.73l2.9,0l-1.73,-3.3C20.55,42.55,21.06,41.64,21.06,40.43009948730469 M21.47,37.04560089111328L21.47,39.52210235595703L24.12,39.52210235595703L24.12,46.38869857788086L25.71,46.38869857788086L26.91,44.090301513671875L26.91,39.52210235595703L29.3,39.52210235595703L30.6,37.04560089111328Z M37.13,25.61210060119629L39.92,25.61210060119629L39.92,34.95389938354492L37.13,34.95389938354492L37.13,25.61210060119629Z M13.38,25.61669921875l-1.25,2.4l0.77,0c1.38,0,2.25,0.88,2.25,2.27c0,1.37,-0.87,2.25,-2.25,2.25l-0.87,0l0,-4.33l-2.7,5.16l0,1.59l3.76,0c3.03,0,4.81,-1.83,4.81,-4.67C17.89,27.53,16.23,25.73,13.38,25.61669921875 M18.63,34.954002380371094L25.76,34.954002380371094L25.76,32.47770309448242L21.42,32.47770309448242L21.42,31.46809959411621L25.41,31.46809959411621L25.41,29.097900390625L21.42,29.097900390625L21.42,28.088598251342773L25.76,28.088598251342773L25.76,25.612201690673828L18.63,25.612201690673828Z           M30.73,31.265302658081055L29.37,25.61210060119629L26.47,25.61210060119629L28.71,34.9536018371582L31.69,34.9536018371582L36.59,25.61210060119629L33.68,25.61210060119629Z M60.37,25.61140251159668L60.37,28.088102340698242L63.01,28.088102340698242L63.01,34.9546012878418L64.6,34.9546012878418L65.81,32.65629959106445L65.81,28.088102340698242L68.2,28.088102340698242L69.5,25.61140251159668Z M45.25,31.350000381469727l1.07,-2.05l0.49,2.05L45.25,31.350000381469727ZM45.36,25.61210060119629l-4.89,9.34l2.9,0l0.71,-1.36L47.35,33.59260177612305l0.32,1.36l2.9,0l-2.24,-9.34L45.36,25.61210060119629Z           M56.98,30.03729820251465L53.86,25.61210060119629L51.1,25.61210060119629L51.1,34.954002380371094L53.89,34.954002380371094L53.89,30.2096004486084L57.36,34.954002380371094L59.77,34.954002380371094L59.77,25.61210060119629L56.98,25.61210060119629Z"></path>
                </g>
                <image src="//st.deviantart.net/minish/main/logo/logo-type.png">             </image></svg>
        </span>
        <span class="text">Deviant Art</span>
    </span>
</a>
        </div>
        <div class="body">
            <iframe scrolling="no" data-da-ad="1" name="ad-atf-theater-970x550-25631" data-ad-id="ad-atf-theater-970x550-25631" data-da-bidder="0" data-ad-campaignid="122" data-da-safety="safe" sandbox="allow-same-origin allow-forms allow-popups allow-scripts allow-pointer-lock" class="hidden-frame-bidder" style="width: 970px; height: 550px; display: none ! important;" src="https://www.da-ads.com/google.html?cb=1529651426#%7B%22size%22%3A%5B970%2C550%5D%2C%22service%22%3A%221008370%5C%2Fca-pub-2005626271413567%22%2C%22slot%22%3A%22atf_theater_970x550_v6_interstitial%22%2C%22aduid%22%3A%22ad-atf-theater-970x550-25631%22%2C%22adsense%22%3A%7B%22adsense_channel_ids%22%3A%************%22%2C%22google_hints%22%3A%22graphic+design%2Cweb+design%2Cgaming%2Cphotography%2Canimation%2Ccomic+books%2Cdigital+images%22%2C%22google_kw%22%3A%22graphic+design%2Cweb+design%2Cgaming%2Cphotography%2Canimation%2Ccomic+books%2Cdigital+images%22%2C%22google_kw_type%22%3A%22broad%22%7D%2C%22da%22%3A%7B%22LoggedIn%22%3A%22No%22%2C%22dailyimp%22%3A1%2C%22sessimp%22%3A1%2C%22section%22%3A%22other%22%7D%2C%22qc%22%3A1%2C%22force%22%3Afalse%2C%22acc%22%3A%22122%22%2C%22additional_sizes%22%3A%5B%5D%2C%22use_td_floors%22%3Atrue%2C%22td_traffic_fraction%22%3A%220.7%22%2C%22td_traffic_fraction_is_sticky%22%3A%221%22%2C%22td_traffic_reset_ts%22%3A%************%22%2C%22td_header_floors%22%3A%220%22%2C%22gpt_sf%22%3Atrue%2C%22safeframed%22%3A1%2C%22sf_ident%22%3A%22ad-atf-theater-970x550-25631%22%2C%22sf_safe%22%3Atrue%2C%22url%22%3A%22https%253A%252F%252Fwww.deviantart.com%252Fusers%252Flogin%22%2C%22page_safety%22%3A%22safe%22%2C%22dapx%22%3A%7B%22r%22%3A%22590mb53b49ae22c96bda8bb13a8a7ab83c2d%22%2C%22d%22%3A%2285ab6bd73ff68e24e66f252169aed8c9%22%2C%22v%22%3A%22other%22%2C%22c%22%3A*********%7D%2C%22bidders%22%3A%5B%5D%7D" webkitallowfullscreen="" mozallowfullscreen="" allowfullscreen="" onload="this.style.visibility='visible';" hidden="" frameborder="0"></iframe><div id="ad-atf-theater-970x550-25631-safe" data-da-safety="safe" data-ad-id="ad-atf-theater-970x550-25631" data-da-ad="1" data-da-bidder="0" data-ad-campaignid="122" style="display:none;">        <script>
            googletag = window.googletag || {};
            googletag.cmd = googletag.cmd || [];
            googletag.cmd.push(function() {
                 
                googletag.display("ad-atf-theater-970x550-25631-safe");
            });
        </script>
        </div>        </div>
    </div>
        <div class="commentwriter-interface" style="display:none">
        <div class="commentwriter-toolbar stashwriter-subtoolbar open">
            <table id="commentwriter-topmenu"><tbody><tr>
            <td class="oh-hasmenu oh-hashover oh-mmain oh-stashmain">
        <a href="https://sta.sh" onclick="PubSub.publish('StashPageNavigation.escape_to_root'); return false;" class="oh-l">
            <span class="oh-stashlogo"><img src="https://a.deviantart.net/avatars/default.gif" class="avatar" height="30" width="30">
                <span class="oh-stashlogo-name"></span>
            </span>
            <span class="oh-stashtitle">Sta.sh</span>
            <span class="oh-submiticon"></span>
            <span class="oh-stashsubmit">Submit</span>
        </a>
        <div id="oh-mainmenu" class="oh-menu iconset-more stash-menu">
            <a class="mi" href="https://sta.sh"><i class="i1337"><img src="https://a.deviantart.net/avatars/default.gif" class="avatar" height="20" width="20"></i>Sta.sh</a>
            <a class="mi" href="https://sta.sh/writer"><i class="idkfa"></i>Sta.sh Writer</a>
            <a class="mi" href="https://sta.sh/muro"><i class="i49"></i>DeviantArt muro</a>
            <a class="mi verify-link" href="https://www.deviantart.com/submit"><i class="iddt"></i>Submit</a>
            <div class="oh-hr"></div>
            <a class="mi" href="https://www.deviantart.com/"><i class="iddqd"></i>DeviantArt</a>
            <div class="oh-hr oh-hrlast"></div>        </div>
    </td>            </tr></tbody></table>
            <div class="commentwriter-actions">
                <a class="commentwriter-cancel smbutton smbutton-white" href="#"><span><em>Hide Media</em></span></a>
                <a class="commentwriter-preview smbutton smbutton-white" href="#"><span><em>Preview</em></span></a>
                <a class="commentwriter-submit smbutton smbutton-green" href="#"><span><em>Submit Comment</em></span></a>
            </div>
            <div class="commentwriter-toolbar-outer">
                <div class="commentwriter-toolbar-inner">
                </div>
            </div>
        </div>
        <div class="commentwriter-sidebar">
            <div class="commentwriter-sidebar-inner">
                <h3>Add Media</h3>
            </div>
            <div class="commentwriter-skinbar-inner writer-skinbar">
                <h3>Style</h3>
                <div class="current-skin hh">
                    <div class="ll">
                        Skin: <span class="skin-title"></span>
                    </div>
                    <div class="rr">
                        <a href="#skins" class="skins_show">Change skin</a>
                    </div>
                </div>
                <div id="stashwriter_sidebar_skins_container">
                </div>
            </div>
        </div>
        <div style="display: none" id="stash_upload_zone_holder">
                <div class="tt-a stash-tt-a sq stream-upload-thumb"><span class="tt-ww">
            <form method="POST" id="stash-form" class="undraggable" enctype="multipart/form-data" action="/dapi/v1/submit/upload" autocomplete="off">
                <input name="action" value="StashPost" type="hidden">
                <a class="smbutton smbutton-file-upload">
                    <span>Upload Files</span>
                    <input name="file" class="file-upload stash-file" type="file">
                </a>
            </form>
        </span></div>
        </div>
    </div>
            <script type="text/javascript">
            (function() {
                var content = document.querySelector('div.bubbleview, .match-body-height');
                var footer = document.querySelector('#depths');
                if (content && footer) {
                    var height = window.getComputedStyle(content).getPropertyValue('height');
                    height = parseInt(height, 10) || content.clientHeight;
                    content.style.minHeight = height + document.documentElement.clientHeight -
                                              footer.getBoundingClientRect().bottom + 'px';
                }
            })();
        </script>
        <script type="text/javascript">window.__initial_body_data={"bilogger":{"platform":"desktop","clientid":"85ab6bd7-3ff6-8e24-e66f-252169aed8c9","requestid":"590m90153afa36e87b07c87ab5e1748186fe","throttle":"100"},"dapx":{"client":"dw","daid":"85ab6bd73ff68e24e66f252169aed8c9","requestid":"590mb53b49ae22c96bda8bb13a8a7ab83c2d","log_data":[],"eventid":"daweb:login::::pageview","delay_init_event":false},"csrf":"MwcDbHXxN3OS48ct.paxrbz.mPLOKWz6ZDnvdo3XHbNND3MSBzL1cXO_ehWlPaaM8Vc","hub":{"user":"lLaScufMIwSse741lKNanA","ts":"1530025631","auth":"b4a36816b9076a4029cb2860a14b3947e7221cd12ba5c1cf74a98a3acdb4fa92","subs":[],"endpoint":"https:\/\/hub.deviantart.net"}};__wake(["lib\/da\/hub","lib\/da\/ads\/blocker-check","lib\/dom\/wait","lib\/da\/dapx","lib\/da\/legacy-difi-bridge"]);</script>            <script type="text/javascript" src="//st.deviantart.net/roses/rosa/webpackcore.d41d8cd98f00b204e980.js"></script>
                        <script type="text/javascript" src="//st.deviantart.net/roses/rosa/vendor.1b583e84f1b88c808ed8.js"></script>
                        <script type="text/javascript" src="//st.deviantart.net/roses/rosa/commons.55e76556e96707d1483f.js"></script>
                        <script type="text/javascript" src="//st.deviantart.net/roses/rosa/login.60fcc6adeec4acaf81a7.js"></script><div class="popup-global-container"><div class="popup-inner-container"><div class="backdrop" style="z-index: 200;"></div></div></div>
            <a id="deviantART-loves-you"> </a>
<script type="text/javascript">
                DWait.loadDownloadMap({"jms\/thirdparty\/lib\/flatpickr\/flatpickr.js":["https:\/\/st.deviantart.net\/css\/flatpickr_jc.js?3871654166","https:\/\/st.deviantart.net\/css\/flatpickr_lc.css?988834512"],"jms\/pages\/gruzecontrol\/widgets\/utils.js":["https:\/\/st.deviantart.net\/css\/page_editor_jc.js?4188395092","https:\/\/st.deviantart.net\/css\/group_privs_jc.js?621278068","https:\/\/st.deviantart.net\/css\/art-common_jc.js?158549310","https:\/\/st.deviantart.net\/css\/jquery-extras_jc.js?2736332723","https:\/\/st.deviantart.net\/css\/menusys_jc.js?302537211","https:\/\/st.deviantart.net\/css\/v6gruser_jc.js?3343841370","https:\/\/st.deviantart.net\/css\/v6loggedin_lc.css?3001430805"],"jms\/pages\/blogobox.js":["https:\/\/st.deviantart.net\/css\/deviation_old_jc.js?782658437","https:\/\/st.deviantart.net\/css\/deviantart-network-admin_jc.js?985492289","https:\/\/st.deviantart.net\/css\/art-common_jc.js?158549310"],"cssms\/pages\/deviation\/note-modal.css":["https:\/\/st.deviantart.net\/css\/notes-modal_lc.css?3383669418"],"jms\/lib\/pager.js":["https:\/\/st.deviantart.net\/css\/menusys_jc.js?302537211"],"jms\/lib\/popup2menu.js":["https:\/\/st.deviantart.net\/css\/menusys_jc.js?302537211"],"cssms\/lib\/writer.css":["https:\/\/st.deviantart.net\/css\/writer_lc.css?3090682151"],"cssms\/pages\/stash\/stash.override.thumbs.css":["https:\/\/st.deviantart.net\/css\/stash_lc.css?3709464454"],"cssms\/lib\/writer-subtoolbar.css":["https:\/\/st.deviantart.net\/css\/writer_lc.css?3090682151"],"jms\/lib\/writer\/factory.js":["https:\/\/st.deviantart.net\/css\/stashwriter_jc.js?2119410682","https:\/\/st.deviantart.net\/css\/jquery.scrollto_jc.js?1893097371","https:\/\/st.deviantart.net\/css\/jquery.ui_jc.js?3105002537","https:\/\/st.deviantart.net\/css\/dragger_jc.js?939552279","https:\/\/st.deviantart.net\/css\/jquery-extras_jc.js?2736332723","https:\/\/st.deviantart.net\/css\/header_jc.js?3114820648","https:\/\/st.deviantart.net\/css\/art-common_jc.js?158549310","https:\/\/st.deviantart.net\/css\/writer_lc.css?3090682151"],"jms\/lib\/sidebar.js":["https:\/\/st.deviantart.net\/css\/sidebar_jc.js?4079037041","https:\/\/st.deviantart.net\/css\/menusys_jc.js?302537211","https:\/\/st.deviantart.net\/css\/stashwriter_jc.js?2119410682","https:\/\/st.deviantart.net\/css\/art-common_jc.js?158549310","https:\/\/st.deviantart.net\/css\/sidebar_lc.css?1490570941","https:\/\/st.deviantart.net\/css\/jquery.ui_jc.js?3105002537"],"jms\/pages\/stash\/stash.embedded.js":["https:\/\/st.deviantart.net\/css\/stash_jc.js?1411243715","https:\/\/st.deviantart.net\/css\/stash_lc.css?3709464454","https:\/\/st.deviantart.net\/css\/deviantart-network-loggedin_jc.js?1438447932","https:\/\/st.deviantart.net\/css\/jquery.ui_jc.js?3105002537"],"jms\/chrome\/more7.js":["https:\/\/st.deviantart.net\/css\/more7_jc.js?3272996614"],"jms\/chrome\/more7.custom2.js":["https:\/\/st.deviantart.net\/css\/more7_jc.js?3272996614","https:\/\/st.deviantart.net\/css\/v6loggedin_jc.js?3166703289","https:\/\/st.deviantart.net\/css\/deviantart-network-loggedin_jc.js?1438447932"],"jms\/chrome\/loginbar.js":["https:\/\/st.deviantart.net\/css\/more7_jc.js?3272996614","https:\/\/st.deviantart.net\/css\/v6loggedin_jc.js?3166703289","https:\/\/st.deviantart.net\/css\/deviantart-network-loggedin_jc.js?1438447932"]});
                </script>        <script type="text/javascript">
        if (self._qevents) {
            _qevents.push( { qacct:"p-915Y6SMHQQJHI", labels: "UserStatus.LoggedOut"} );
        }
        </script>
        <noscript>
        <div style="display: none;"><img src="//pixel.quantserve.com/pixel/p-915Y6SMHQQJHI.gif" height="1" width="1" alt="Quantcast"></div>
        </noscript>
        <script type="text/javascript">
                (function(d, e, v, i, a, n, t){
                    d.dapx = d.dapx || function() { (d.dapx.q = d.dapx.q || []).push(arguments)};
                    d.dapx.drift = i ? Math.round(new Date()*.001)-i : 0;
                    n = e.createElement(v);
                    t = e.getElementsByTagName(v)[0];
                    n.async = 1;
                    n.src = "https://st.deviantart.net/css/dapx_jc.js?*********";
                    t.parentNode.insertBefore(n, t);
                })(window, document, "script", 1530025631);
            </script><script type="text/javascript">DWait.ready(["jms\/lib\/pubsub.js"], function(){ PubSub.publish("DaPx.initialize", {"client":"dw","daid":"85ab6bd73ff68e24e66f252169aed8c9","requestid":"590mb53b49ae22c96bda8bb13a8a7ab83c2d","log_data":[],"eventid":"daweb:login::::pageview","delay_init_event":false}); });</script>
        <!-- Begin comScore Tag -->
        <script>
         var _comscore = _comscore || [];
        _comscore.push({ c1: "2", c2: "8112885" });
         (function() {
         var s = document.createElement("script"), el =
        document.getElementsByTagName("script")[0]; s.async = true;
         s.src = (document.location.protocol == "https:" ? "https://sb" : "http://b") +
        ".scorecardresearch.com/beacon.js";
         el.parentNode.insertBefore(s, el);
         })();
        </script>
        <noscript>
            <img src="https://sb.scorecardresearch.com/p?c1=2&c2=8112885" />
        </noscript>
        <!-- End comScore Tag -->
        

</body></html>
