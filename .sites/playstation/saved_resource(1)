var ndjsStaticVersion="sync-131618",nslyyidtyi={};function nsbopifkzi(a,b,c,d,e){var f=null;1==c&&(nsagvvln(a,b,e),f=nsgukkebkh(a));if(null==f||1==d)try{window.sessionStorage.setItem(a,b)}catch(g){}}var nslgf={},nsdwhx,nsviymjoy=-1,nsbopifk=-1,nsbopi={},nsgukk={},nscav={},nsgukkebk={},nsviymjoyg={},nslyyidt=[],nsfkgjo=!1,nscavjy="default";
function nslyyidty(a,b,c){var d=nstnemtg([{type:"selector",value:'input[name\x3d"'+b+'"]'}]);if(0<d.length)for(a=0;a<d.length;a++)d[a].value=c;else if(0<a.length)for(var e=0;e<a.length;++e)d=a[e].querySelector("input[name\x3d"+b+"]"),null===d&&(d=document.createElement("input"),d.setAttribute("name",b),d.setAttribute("type","hidden"),a[e].appendChild(d)),d.setAttribute("value",c)}var nds=window.ndsapi||(window.ndsapi={}),nscavj=null,numQueries=0;
function nslgfnpyxj(a){for(var b=[],c=0;c<a.length;c++)-1===b.indexOf(a[c])&&b.push(a[c]);return b}var returned=[],version="null";function nsviymjo(a){for(var b in a)if(a.hasOwnProperty(b)){var c=a[b];c&&c.call&&c.apply&&(a[b]=function(){})}}"{@VER:SION@}".replace(":","")!==ndjsStaticVersion&&(version=ndjsStaticVersion);
function nslgfn(a){nslgf.sid=a;if(!0===nsgukk.ndsidConfig.enable&&null!=a){var b=nsgukk.ndsidConfig.options.hasOwnProperty("formbind")&&nsgukk.ndsidConfig.options.formbind?nstnemtg(nsgukk.ndsidConfig.options.formbind):document.getElementsByTagName("form");nslyyidty(b,nsfkgjoq,a);nsfkgjoq===nsdwhxurq&&nsfkg([])}}function nslgfnpyx(a,b){if(nsgukkebk.hasOwnProperty(a))for(var c in nsgukkebk[a])nsgukkebk[a].hasOwnProperty(c)&&nslgf.hasOwnProperty(c)&&nslgf[c]&&(b[c]=nslgf[c])}var nslyyid;
function nscavjyd(a,b,c){nsqlyrfuyo(a,function(d,e){c=b(c,d,e,a)});return c}var nsdwhxu="NDSASESS";function nsfkg(a){if(nsgukk.hasOwnProperty("pageModeConfig")&&!0===nsgukk.pageModeConfig.enable){var b=nsgukk.pageModeConfig,c={};b.hasOwnProperty("options")&&(c=b.options,null!==a&&nsbop(a)&&0<a.length&&(c&&!1===c.hasOwnProperty("formbind")&&(c.formbind=[]),c.formbind=Array.prototype.concat.call(c.formbind,a)));a=nsbopif("pmd");a=nslyy(a);nshyfqp(a,c)}}
var nsviym="3600",nsviy="ndsi"+ndjsStaticVersion,nsfkgjoq="ndsid",nsdwhxurq="nds-pmd",nsfkgjoqr="widgetData";function nsfkgjoqrf(a){return(new Date(a)).getMinutes()}var nsdwhxur=[];function nslyy(a){var b={};b[nsfkgjoqr]={};var c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];"sid"===c&&c in nsgukkebk.pmd||"wt"===c||"gzrq"===c?b[c]=d:c in nsgukkebk.pmd&&(b[nsfkgjoqr][c]=d)}nsgukkebk.hasOwnProperty("pmd")&&"fmid"in nsgukkebk.pmd&&(b.fmid="nds-pmd");return b}
function nsbop(a){return"[object Array]"===Object.prototype.toString.apply(a)}function nslgfnpy(){if(nsgukk.hasOwnProperty("pageModeConfig")&&!0===nsgukk.pageModeConfig.enable){var a=nsgukk.pageModeConfig;a.hasOwnProperty("inputFieldName")&&0<a.inputFieldName.length&&(nsdwhxurq=a.inputFieldName);!0===nsgukk.ndsidConfig.enable&&nssnfwmip();!1===nsgukkebk.hasOwnProperty("pmd")&&(nsgukkebk.pmd={pmdModuleMissing:!0})}}
function nsviymj(a){var b=0;if(a instanceof Array)b=a.length;else for(var c in a)a.hasOwnProperty(c)&&b++;return b}var nsfkgj={addCallback:function(){},callAllCallbacks:function(){}},nsdwhxurqd=function(a,b){},nslgfnp=function(){var a=new Date;return"ndsa"+Math.random().toString(36).substr(2,16)+a.getTime().toString(36)};
function nsdwh(){return window.innerWidth?window.innerHeight:document.documentElement&&document.documentElement.clientHeight?document.documentElement.clientHeight:document.body&&document.body.clientHeight?document.body.clientHeight:null}
var nsguk=function(){for(var a=0,b=document.domain,c=b.split("."),d="_temp"+(new Date).getTime();a<c.length-1&&-1===document.cookie.indexOf(d+"\x3d"+d);)b=c.slice(-1-++a).join("."),document.cookie=d+"\x3d"+d+";domain\x3d"+b+";";document.cookie=d+"\x3d; expires\x3dThu, 01 Jan 1970 00:00:01 GMT; domain\x3d"+b+";";return b};
function nsbopif(a){var b={r:Math.floor(1E6*Math.random())+1E3,sid:nds.config.sessionId,p:nsfiie(),bd:nskyivzx(),forceIP:nds.config.forceIP,dtrk:nds.config.doNotTrack,jsv:version};nsgukk.gzrq&&(b.gzrq=1);var c="";switch(a){case "i":b.ls={};try{var d=window.localStorage.getItem("ndcd");null!=d&&(b.ls.ndcd=d)}catch(e){}break;case "c":nslgf.hasOwnProperty("wt")&&(c=nslgf.wt);break;case "pmd":nslgf.hasOwnProperty("sid")&&(b.sid=nslgf.sid),nsgukk.hasOwnProperty("pageModeConfig")&&nsgukk.pageModeConfig.hasOwnProperty("wt")&&
(c=nsgukk.pageModeConfig.wt)}b.wt=c;nds.config.placement?(b.wp=nds.config.placement,b.wpp=nds.config.placementPage):b.href=window.location.href;nds.ndeb&&(b.eb=nds.ndeb);nslgfnpyx(a,b);"undefined"===typeof window.ndovStandaloneWidget||"c"!==a&&"pmd"!==a||(b.gf=HashUtil.ndovGrabFields(nsviymjoyg));return b}
function ndwti(a){nsviymjoy=nskyivz();nsbopifk=nskyivz();nslgf.wt=a.fd.wt;nsviymjoyg=a.gf;if(null!=a.wab){var b=a.wab;b=nstnemtg(b);for(var c in b){var d=b[c];if(null!=d){var e="FORM"===d.tagName?"submit":"click";nds.common.addEventListener(d,e,nds.send)}}}if(null!=a.ls&&(e=a.ls,d=ndoGetObjectKeys(e),b=nsviymj(e),0<b))for(c=0;c<b;c++)window.localStorage.setItem(d[c],e[d[c]]);if(null!=a.cwd){if("undefined"==typeof document.addEventListener)return null;a.cwd.websiteId=a.wi;var f=a.cwd;null!=f.pr&&null==
f.cp&&nds.config.placement&&nds.config.placementPage&&(a=nds.config.placement+"."+nds.config.placementPage,c=f.pr,c[a]&&(f.cp=c[a],f.cp.placement=nds.config.placement,f.cp.page=nds.config.placementPage));if(null!=f.cp)if(null!=f.cp.bindmethod&&"manual"==f.cp.bindmethod)nds.callApi=function(a){nsdwhxurqd(f,a)},nds.completeAndCallApi=function(a){nds.send(function(){nds.callApi(a)})};else for(b=null==f.cp.autobind?document.getElementsByTagName("form"):nstnemtg(f.cp.autobind),c=0;c<b.length;c++)if(d=
b[c],null!=d){var g=(a="function"===typeof d.onsubmit)?d.onsubmit:function(){return!0};e="FORM"===d.tagName?"submit":"click";(function(a,b,c,d){var e=function(l){var n=l||window.event;n.preventDefault?n.preventDefault():n.returnValue=!1;nsdwhxurqd(f,function(l,f){if(1==c){a.onsubmit=function(){};try{b(n)}catch(p){}}else nds.common.removeEventListener(a,d,e);if(a.submit)try{a.submit()}catch(p){a.constructor.prototype.submit.call(a)}else a.click()})};!0===c?a.onsubmit=e(d):nds.common.addEventListener(a,
d,e)})(d,g,a,e)}}!0===nsgukk.ndsidConfig.enable&&(a=nsgukkebkh(nsfkgjoq),null!==a&&(nds.setSessionIdFromCookie(nsfkgjoq),nsbopifkzi(nsfkgjoq,a,!1,!0)),nslgfn(nsfiievb(nsfkgjoq)))}nslyyi&&(ndwts=function(){});
nds.load=function(a){if(nslyyi)nsviymjo(nds),nsviymjo(nds.common),nsviymjo(nds.common.util),nds.send=function(a){"function"===typeof a&&a()};else{var b=function(a){var b={},c;a=a.split("\x26");var d=0;for(c=a.length;d<c;d++){var e=a[d].split("\x3d");b[e[0]]=decodeURIComponent(e[1])}return b},c=a.split("?");nds.config.queryParams=1<c.length?b(c[1]):{};"undefined"===typeof nslyyid&&(nslyyid=c=c[0].replace(/\/sync\/js\/?$/,""));c=nslyyid;var d=function(a){a=nsbopif(a);for(var b in a)a.hasOwnProperty(b)&&
!a[b]&&delete a[b];b=nsgukke.stringify(a);b=nshyfqpw(b);return"q\x3d"+encodeURIComponent(b)},e=function(a){return(returned.length===k||-1<returned.indexOf(!1))&&"function"===typeof a?(a(),0>returned.indexOf(!1)&&(returned=[]),!0):!1};nds.getQueryArray=function(a){var b=[];"string"===typeof a&&""!==a&&(b=nsfiievbab(a));a=b.length;for(var c=[],d=0;d<a;d++){var e=nshyfqpw(b[d]);c.push(e)}return c};nds.config.sendTimeout=5E3;nds.send=function(a){nssnfwmi("precomplete");if(!0===nsgukk.eventModeEnabled){returned=
[];!1===nsfkgjo&&setTimeout(nds.init,0);!0===nsgukk.ndsidConfig.enable&&nssnfwmip();var b=nds.getQueryArray("c");k=b.length;for(var d=0;d<k;d++)(function(){var l="q\x3d"+encodeURIComponent(b[d]),f=new Image,h=null;f.onerror=function(){returned.push(!1);!0===e(a)&&(a=null)};f.onload=function(){h&&clearTimeout(h);returned.push(!0);!0===e(a)&&(a=null)};nds.config.sendTimeout&&(h=setTimeout(f.onerror,nds.config.sendTimeout));f.src=c+"/complete/gif/?"+l})()}else"function"===typeof a&&a();nds.ndwtr()};
nds.loadScript=function(a,b,c){var d=document.getElementById(b);d&&d.parentNode.removeChild(d);d=document.createElement("script");d.setAttribute("type","text/javascript");d.setAttribute("src",a);d.setAttribute("id",b);nds.config.hasOwnProperty("cspNonce")&&0<nds.config.cspNonce.length&&d.setAttribute("nonce",nds.config.cspNonce);a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(d,a);"function"===typeof c&&c(d)};nds.config.sessionId=null;a=nds.config.q;for(b=0;b<a.length;++b){var f=
a[b];"function"===typeof f&&f()}null===nds.config.sessionId&&("undefined"!==typeof window.ndovStandaloneWidget?nds.config.sessionId=nsfiievb(nsdwhxu):!0===nsgukk.ndsidConfig.enable&&(nscavj=nsfiievb(nsfkgjoq),nslgfn(nscavj)));nds.init=function(){nsfkgj.callAllCallbacks();if("application/json"===nscavjy)try{var a=null;null!=XMLHttpRequest&&(a=new XMLHttpRequest);!0===nsgukk.ndsidConfig.enable&&nssnfwmip();a.open("GET",c+"/init/js/?"+d("i"));a.setRequestHeader("Cache-Control","max-age\x3d0");a.setRequestHeader("Content-Type",
"application/json");a.send();a.onreadystatechange=function(){if(4===a.readyState&&200===a.status)try{var b=JSON.parse(a.response);ndwti(b)}catch(X){}}}catch(l){}else!0===nsgukk.ndsidConfig.enable&&nssnfwmip(),nds.loadScript(c+"/init/js/?"+d("i"),nsviy);nsfkgjo=!0;!0===nsbop(nds.config.initCallbackList)&&nsqlyrfuyo(nds.config.initCallbackList,function(a){a()})};nds.reinit=function(a,b,c){null!=a&&nds.setPlacement(a);b&&nds.setPlacementPage(b);c&&(nslyyidtyi.ipr.fm=c);nssnfwmi("init");nsfkg([]);nslyyidt&&
0!==nslyyidt.length?B(nslyyidt):nds.init()};if(nsgukkebk.hasOwnProperty("id")&&0<nsviymj(nsgukkebk.id)){returned=[];!0===nsgukk.ndsidConfig.enable&&nssnfwmip();a=nsfiievbab("id");var g=null,k=a.length;for(b=0;b<k;b++){var m=nshyfqpw(a[b]);m="rr\x3dsomething\x26q\x3d"+encodeURIComponent(m);var h=new Image;h.onerror=function(){returned.push(!1);!0===e(f)&&(f=null)};h.onload=function(){g&&clearTimeout(g);returned.push(!0);!0===e(f)&&(f=null)};nds.config.sendTimeout&&(g=setTimeout(h.onerror,nds.config.sendTimeout));
h.src=c+"/init/gif/?"+m}}var B=function(a){nsfkgj.callAllCallbacks();nsfkgj=nds.common.createCallbackList();var b=-1;var c=nstnemtg(a);for(a=0;a<c.length;a++)try{var d=c[a];var e=nds.common.addEventListener(d,"focus",nds.init);nsfkgj.addCallback(e);b++}catch(p){}-1<b&&nshyf(c,nds.init);-1===b&&nds.init()};null==nslyyidt||0===nslyyidt.length?nds.init():B(nslyyidt)}};nds.getInputList=function(){return nslgf};
nds.sendOnEvent=function(a,b){var c=document.getElementById(a);nds.common.addEventListener(c,b,nds.send)};nds.sendOnSubmit=function(){for(var a=document.getElementsByTagName("form"),b=0;b<a.length;++b)nds.common.addEventListener(a[b],"submit",nds.send)};
function nscavjydj(a){var b=[41,8,49,48,51,44,63,0,19,61,43,63,57,15,34,6,42,59,41,19,10,45,54,0,44,34,57,36,48],c="";if("NDX:"===a.substring(0,4)){var d=!0;a=a.substring(4)}else d=!1,c="NDX:";for(var e=0,f=0;f<a.length;f+=1){var g=a.charCodeAt(f)-32;0<=g&&94>g&&(d&&64>g&&(g^=b[e%b.length]),g+=47+(d?-1:1)*e*31,g=(g%94+94)%94,!d&&64>g&&(g^=b[e%b.length]),e++);c+=String.fromCharCode(g+32)}return c}
function nsbopifkz(a){if(nsdwhxurq===nsfkgjoq){a=nsgukkeb(a);for(var b={},c=0;c<a.length;c++){try{b=a[c].querySelector('input[name\x3d"'+nsdwhxurq+'"]')}catch(d){}null!==b&&nsdwhxur.push(b)}}else nsdwhxur=nstnemtg([{type:"selector",value:'input[name\x3d"'+nsdwhxurq+'"]'}])}HashUtil.ndovGrabFields=function(a){return"ndovGrabFields not initialized in default mode"};
function nsgukkeb(a){var b=[];nsgukk.hasOwnProperty("pageModeConfig")&&0<nsgukk.pageModeConfig.formbind.length&&(b=Array.prototype.concat.call(b,nstnemtg(nsgukk.pageModeConfig.formbind)));a&&a.hasOwnProperty("formbind")&&a.formbind&&(b=Array.prototype.concat.call(b,nstnemtg(a.formbind)));return b}function nsgukkebkh(a,b){return(b=document.cookie.match("(^|;)\\s*"+a+"\\s*\x3d\\s*([^;]+)"))?b.pop():null}nds.setInitTargets=function(a){nslyyidt=a};
nds.addInitCallback=function(a){!1===nsbop(nds.config.initCallbackList)&&(nds.config.initCallbackList=[]);"function"===typeof a&&-1===nds.config.initCallbackList.indexOf(a)&&nds.config.initCallbackList.push(a)};nds.removeAllInitCallbacks=function(){nds.config.initCallbackList=[]};function nscavjydje(a,b){nsbopi[a]=b}nds.setSessionId=function(a){nds.config.sessionId=a};nds.setSessionIdFromCookie=function(a){try{nds.setSessionId(nsgukkebkh(a))}catch(b){nds.setSessionId(null)}};
nds.setSessionIdFromSessionStorage=function(a){try{nds.setSessionId(window.sessionStorage.getItem(a))}catch(b){nds.setSessionId(null)}};nds.setSessionIdFromURL=function(a){"undefined"!==typeof nds.config.queryParams&&a in nds.config.queryParams?(a=nds.config.queryParams[a],nds.setSessionId(a?a:null)):nds.setSessionId(null)};function nsswwaawe(a,b){return a===b?!0:!1}
function ndwts(a){nsgukkebk=a.ml;nsgukk=a.co;nslyyidtyi=a.wmd;nslgf=a.fd;nsdwhx=!0;nscavjy=a.wc;nsgukk.initId&&(nsviy=nsgukk.initId);nssnfwmi("init");"undefined"!=typeof nsgukk.initBindings&&nds.setInitTargets(nsgukk.initBindings);"undefined"!==typeof nsgukk.apiBaseUrl&&nsgukk.apiBaseUrl&&nds.setApiBaseUrl(nsgukk.apiBaseUrl);nslgfnpy();nsfkg([])}nds.setSessionIdFromDOM=function(a){try{nds.setSessionId(document.getElementById(a).value)}catch(b){nds.setSessionId(null)}};
nds.setSessionIdFromJS=function(a){if(a in window){var b=a=window[a];"function"===typeof a&&(b=a());nds.setSessionId(b?b:null)}else nds.setSessionId(null)};nds.setForceIP=function(a){nds.config.forceIP=a};nds.setPlacement=function(a){nds.config.placement=a};nds.setPlacementPage=function(a){nds.config.placementPage=a};nds.setTimeout=function(a){nds.config.sendTimeout=+a};nds.setForceUA=function(a){nds.config.forceUA=a};function nshyfq(){return 692441607}
function nsagvvln(a,b,c){null===nsgukkebkh(a)&&(a=a+"\x3d"+b+";max-age\x3d"+nsviym+"; domain\x3d ."+nsguk()+" ; path\x3d/",!0===c&&(a+="; secure"),document.cookie=a)}function nsfiiev(a){return"object"===typeof a&&null!==a&&!nsbop(a)}nds.setCspNonceForInit=function(a){nds.config.cspNonce=a};nds.doNotTrack=function(){nds.config.doNotTrack=!0};nds.bindNewFields=function(a){nssnfwmi("rebind",a);nsfkg([])};function nskyivz(){return parseInt((new Date).getTime()/1E3,10)}
nds.setPageModeFields=function(a){if(null!==a&&nsbop(a)&&0<a.length){for(var b=0;b<nsdwhxur.length;b++)nsdwhxur[b].parentNode.removeChild(nsdwhxur[b]);nsdwhxur=[];nsfkg(a)}};nds.clear=function(){nssnfwmi("clear")};
function nshyfqp(a,b){if(0<nsviymj(a)){var c=nsgukkeb(b);0===nsviymj(c)&&(c=document.getElementsByTagName("form"));a.hasOwnProperty("sid")&&null==a.sid&&(a.sid="");var d=nsgukke.stringify(a);nsgukk.hasOwnProperty("pageModeConfig")&&!0===nsgukk.pageModeConfig.encodeData&&(d=nshyfqpw(d));nslyyidty(c,nsdwhxurq,d);nsbopifkz(b)}}nds.stop=function(){nssnfwmi("stop");nsfkgj.callAllCallbacks()};
function nsfiievb(a){if(null==nds.config.sessionId){var b=nsgukkebkh(a);if(null==b){try{b=window.sessionStorage.getItem(a)}catch(c){b=null}null==b&&(b=nslgfnp(),"undefined"===typeof window.ndovStandaloneWidget&&!0!==nsgukk.clientSideCookie||nsbopifkzi(a,b,!0,!1,"object"===typeof nsgukk.ndsidConfig&&"boolean"===typeof nsgukk.ndsidConfig.secure?nsgukk.ndsidConfig.secure:!1))}return b}return nds.config.sessionId}nds.ndwtr=function(){nssnfwmi("reinit")};nds.setFormFieldData=function(a,b){nslgf[a]=b};
nds.setApiBaseUrl=function(a){nslyyid=a};nds.getApiBaseUrl=function(){return nslyyid};"undefined"==typeof nds&&(nds=window.ndsapi||(window.ndsapi={}));function nsagvvlnu(a){return a.concat("nBCXNxbjl145j")}nds.common={};nds.common.util={};nds.common.bi={};function nshyfqpw(a){return!0===nsgukk.useNdx?nscavjydj(a):nsfiievba(a)}nds.common.querySelectorAll=function(a){return document.querySelectorAll(a)};document.querySelectorAll||(nds.common.querySelectorAll=function(a){return[]});
function nsqlyrfuyo(a,b){var c;if(nsbop(a)||nsagvvlnuk(a))for(c=0;c<a.length;c+=1)b(a[c],c,a);else if(nsfiiev(a)){for(e in a)Object.prototype.hasOwnProperty.call(a,e)&&b(a[e],e,a);var d=nsqlyrfuyo.ie8extraEnums;for(c=0;c<d.length;c+=1){var e=d[c];Object.prototype.hasOwnProperty.call(a,e)&&b(a[e],e,a)}}}nds.common.addEventListener=function(a,b,c){try{a.addEventListener?a.addEventListener(b,c,!1):a.attachEvent&&a.attachEvent("on"+b,c)}catch(d){}return function(){nds.common.removeEventListener(a,b,c)}};
function nstnemtg(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];if("string"===typeof d&&document.getElementById(d))b.push(document.getElementById(d));else if(d.hasOwnProperty("type")&&d.hasOwnProperty("value"))switch(d.type){case "selector":var e=[];try{e=nds.common.querySelectorAll(d.value)}catch(f){}if(0<e.length)for(d.hasOwnProperty("index")&&(e=[e[d.index]]),d=0;d<e.length;d+=1)e[d]&&b.push(e[d])}}return b}
nds.common.removeEventListener=function(a,b,c){try{a.removeEventListener?a.removeEventListener(b,c,!1):a.detachEvent&&a.detachEvent("on"+b,c)}catch(d){}};nds.common.createCallbackList=function(){var a=[];return{addCallback:function(b){b&&a.push(b)},callAllCallbacks:function(){for(var b=0;b<a.length;b+=1)(0,a[b])();a=[]}}};
nds.common.util.truncTo=function(a,b,c){c="undefined"!==typeof c?c:"TRUNC";if("string"!==typeof a)return a;var d=b-c.length;return 1>d?a.substring(0,b):a.length>d?a.substring(0,d)+c:a};nds.common.util.quickHash=function(a){var b=0,c=0,d;if(0===a.length)return"00000000";var e=0;for(d=a.length;e<d;e++){var f=a.charCodeAt(e);0===e%2?(b=(b<<5)-b+f,b|=0):(c=(c<<5)-c+f,c|=0)}0>b&&(b=4294967295+b+1);0>c&&(c=4294967295+c+1);return b.toString(16)+c.toString(16)};
nds.common.bi.getScreenFingerprint=function(){var a="";window.screen&&(a+=[window.screen.width,window.screen.height].sort().join("x"),a+=" "+window.screen.colorDepth);return a};function nsfiie(){var a="";if(window._phantom||window.callPhantom||window.__phantomas)a+="p";window.Buffer&&(a+="n");window.emit&&(a+="c");window.spawn&&(a+="r");window.webdriver&&(a+="s");if(window.domAutomation||window.domAutomationController)a+="b";return a}
nds.common.util.getComputedStyle=function(a,b){if(document.defaultView&&document.defaultView.getComputedStyle)return document.defaultView.getComputedStyle(a,null).getPropertyValue(b);try{if(a.currentStyle)return b=b.replace(/-(\w)/g,function(a,b){return b.toUpperCase()}),a.currentStyle[b]}catch(c){}};
nds.common.bi.getScreenInfo=function(){var a="";"undefined"!==typeof window.screen&&("undefined"!==typeof window.screen.width&&"undefined"!==typeof window.screen.height&&(a+=window.screen.width+"x"+window.screen.height),"undefined"!==typeof window.screen.availWidth&&"undefined"!==typeof window.screen.availHeight&&(a+=" "+window.screen.availWidth+"x"+window.screen.availHeight),"undefined"!==typeof window.screen.colorDepth&&(a+=" "+window.screen.colorDepth),"undefined"!==typeof window.screen.pixelDepth&&
(a+=" "+window.screen.pixelDepth));return a};function nssnfwmi(a,b){for(var c in nsbopi)if(nsbopi.hasOwnProperty(c)){var d=nsbopi[c];if(d.hasOwnProperty(a))(0,d[a])(nslyyidtyi[c],b)}}function nshyf(a,b){var c=10,d=setInterval(function(){for(var e=0;e<a.length;e+=1)try{var f=a[e];if(f&&f.value&&f.value!==f.defaultValue){b();clearInterval(d);return}}catch(g){}--c;1>c&&clearInterval(d)},250);nsfkgj.addCallback(function(){clearInterval(d)})}
nds.common.bi.isFlashInstalled=function(){try{return new ActiveXObject("ShockwaveFlash.ShockwaveFlash"),!0}catch(a){}try{if(void 0!=navigator.mimeTypes["application/x-shockwave-flash"]&&navigator.mimeTypes["application/x-shockwave-flash"].enabledPlugin)return!0}catch(a){}return!1};nds.common.bi.getDeviceLanguage=function(){return window.navigator.userLanguage||window.navigator.language||window.navigator.browserLanguage};
nds.common.bi.getDeviceTimezone=function(){var a=(new Date(2014,0,2)).getTimezoneOffset(),b=(new Date(2014,5,2)).getTimezoneOffset();return Math.max(a,b)};
nds.common.bi.getPlugins=function(){var a=[],b=/([0-9]+)\.[0-9|.]+/g;if(window.ActiveXObject){if(document.plugins&&0<document.plugins.length)for(var c=0;c<document.plugins.length;c++)a.push(document.plugins[c].src.replace(b,"$1"))}else try{if(navigator.plugins&&0<navigator.plugins.length)for(c=0;c<navigator.plugins.length;c++)a.push(navigator.plugins[c].name.replace(b,"$1"))}catch(d){a.push("denied")}0<a.length&&a.sort();b="p";for(c=0;c<a.length;c++)b+=","+a[c];return b};
nds.common.bi.getWebGLInfo=function(){for(var a={},b=document.createElement("canvas"),c=["webgl","experimental-webgl","moz-webgl","webkit-3d"],d,e=0;e<c.length;e++)try{if(d=b.getContext(c[e])){a.ContextName=c[e];break}}catch(f){}if(!(d&&"getParameter"in d))return null;b="VENDOR VERSION RENDERER SHADING_LANGUAGE_VERSION DEPTH_BITS MAX_VERTEX_ATTRIBS MAX_VERTEX_TEXTURE_IMAGE_UNITS MAX_VARYING_VECTORS MAX_VERTEX_UNIFORM_VECTORS MAX_COMBINED_TEXTURE_IMAGE_UNITS MAX_TEXTURE_SIZE MAX_CUBE_MAP_TEXTURE_SIZE NUM_COMPRESSED_TEXTURE_FORMATS MAX_RENDERBUFFER_SIZE MAX_VIEWPORT_DIMS ALIASED_LINE_WIDTH_RANGE ALIASED_POINT_SIZE_RANGE".split(" ");
for(c=0;c<b.length;c++)e=b[c],e in d&&(a[e]=d.getParameter(d[e]));return a};nds.common.bi.getDeviceTouchSettings=function(){var a={mtp:"NA"};"undefined"!==typeof navigator.maxTouchPoints?a.mtp=navigator.maxTouchPoints:"undefined"!==typeof navigator.msMaxTouchPoints&&(a.mtp=navigator.msMaxTouchPoints);a.ts=!1;"ontouchstart"in window&&(a.ts=!0);a.te=!1;try{document.createEvent("TouchEvent"),a.te=!0}catch(b){}return a};
nds.common.bi.getCookiesEnabled=function(a){var b=!0,c="undefined"!==typeof navigator.cookieEnabled&&navigator.cookieEnabled?!0:!1;if(1==a)try{document.cookie="ncookietest\x3d1",b=-1!=document.cookie.indexOf("ncookietest\x3d"),document.cookie="ncookietest\x3d1; expires\x3dThu, 01-Jan-1970 00:00:01 GMT"}catch(d){}return{tc:b,nc:c}};
nds.common.bi.getHTML5CanvasSignature=function(){var a="NA";try{var b=document.createElement("canvas");b.width=200;b.height=40;b.style.display="inline";var c=b.getContext("2d");c.fillText("aBc#$efG~ \ude73\ud63d",4,10);c.fillStyle="rgba(67, 92, 0, 0.5)";c.font="18pt Arial";c.fillText("aBc#$~efG \ude73\ud63d",8,12);a=b.toDataURL()}catch(d){}return a};
nds.common.bi.getFontMetrics=function(){var a=[];try{for(var b=document.createElement("canvas").getContext("2d"),c=nds.common.bi.fontMetricsFontList,d=0;d<c.length;d+=1){b.font='72px "'+c[d]+'"';var e=b.measureText("mmmmmmmmmmlli").width;a.push(e)}}catch(f){}return a};nds.common.bi.getHTML5LocalStorage=function(){var a=!1;try{var b=window.localStorage;b.setItem("ndls","ndls");b.removeItem("ndls");a=!0}catch(c){}return a};
nds.common.bi.getHTML5SupportedVideo=function(){var a="fv";try{var b=document.createElement("video"),c=["ogg","mp4","webm"];if("undefined"!==typeof b)for(var d in c)c.hasOwnProperty(d)&&""!=b.canPlayType("video/"+c[d])&&(a+=","+c[d])}catch(e){}return a};nds.common.bi.getHTML5SupportedAudio=function(){var a="fa";try{var b=document.createElement("audio"),c=["mpeg","ogg","wav"];if("undefined"!==typeof b)for(var d in c)c.hasOwnProperty(d)&&""!=b.canPlayType("audio/"+c[d])&&(a+=","+c[d])}catch(e){}return a};
nds.common.bi.getPlatform=function(){var a="NA";try{a=navigator.platform}catch(b){}return a};function nsswwaawej(){return window.innerWidth?window.innerWidth:document.documentElement&&document.documentElement.clientWidth?document.documentElement.clientWidth:document.body&&document.body.clientWidth?document.body.clientWidth:null}nds.common.bi.getVendor=function(){var a="NA";try{a=navigator.vendor}catch(b){}return a};
function nsfiievba(a){return a.replace(/[A-Za-z]/g,function(a){return String.fromCharCode(a.charCodeAt(0)+("M">=a.toUpperCase()?13:-13))})}nds.common.bi.fontMetricsFontList="monospace;sans-serif;serif;Andale Mono;Arial;Arial Black;Arial Hebrew;Arial MT;Arial Narrow;Arial Rounded MT Bold;Arial Unicode MS;Bitstream Vera Sans Mono;Book Antiqua;Bookman Old Style;Calibri;Cambria;Cambria Math;Century;Century Gothic;Century Schoolbook;Comic Sans;Comic Sans MS;Consolas;Courier;Courier New;Garamond;Geneva;Georgia;Helvetica;Helvetica Neue;Impact;Lucida Bright;Lucida Calligraphy;Lucida Console;Lucida Fax;LUCIDA GRANDE;Lucida Handwriting;Lucida Sans;Lucida Sans Typewriter;Lucida Sans Unicode;Microsoft Sans Serif;Monaco;Monotype Corsiva;MS Gothic;MS Outlook;MS PGothic;MS Reference Sans Serif;MS Sans Serif;MS Serif;MYRIAD;MYRIAD PRO;Palatino;Palatino Linotype;Segoe Print;Segoe Script;Segoe UI;Segoe UI Light;Segoe UI Semibold;Segoe UI Symbol;Tahoma;Times;Times New Roman;Times New Roman PS;Trebuchet MS;Verdana;Wingdings;Wingdings 2;Wingdings 3".split(";");
function nssww(a,b){if("string"===typeof b){var c=b;b=function(a){return a[c]}}var d=nsbop(a)?[]:nsfiiev(a)?{}:void 0;nsqlyrfuyo(a,function(c,f){d[f]=b(c,f,a)});return d}function nskyivzx(){return nsswwaawej()+":"+nsdwh()+":"+window.outerWidth+":"+window.outerHeight+":"+screen.availWidth+":"+screen.availHeight}var nsgukke;nsgukke||(nsgukke={});
Array.prototype.indexOf||(Array.prototype.indexOf=function(a,b){var c=this.length>>>0,d=Number(b)||0;d=0>d?Math.ceil(d):Math.floor(d);for(0>d&&(d+=c);d<c;d++)if(d in this&&this[d]===a)return d;return-1});nsqlyrfuyo.ie8extraEnums=function(){return{toString:null}.propertyIsEnumerable("toString")?[]:"toString toLocaleString valueOf hasOwnProperty isPrototypeOf propertyIsEnumerable constructor".split(" ")}();
var ndoGetObjectKeys=function(){var a=Object.prototype.hasOwnProperty,b=!{toString:null}.propertyIsEnumerable("toString"),c="toString toLocaleString valueOf hasOwnProperty isPrototypeOf propertyIsEnumerable constructor".split(" "),d=c.length;return function(e){if("object"!==typeof e&&("function"!==typeof e||null===e))throw new TypeError("ndoGetObjectKeys called on non-object");var f=[],g;for(g in e)a.call(e,g)&&f.push(g);if(b)for(g=0;g<d;g++)a.call(e,c[g])&&f.push(c[g]);return f}}();
function nsfiievbab(a){a=nsbopif(a);var b=[],c=0,d={r:a.r,sid:a.sid,wt:a.wt},e;for(e in a)if(a.hasOwnProperty(e))if(a[e]){var f=a[e],g=e.length+f.toString().length;2E3<g?(g={r:a.r,sid:a.sid,wt:a.wt},g[e]=f,b.push(nsgukke.stringify(g))):2E3<g+c?(b.push(nsgukke.stringify(d)),d={r:a.r,sid:a.sid,wt:a.wt},d[e]=f,c=g):(d[e]=f,c+=g)}else delete a[e];0<c&&b.push(nsgukke.stringify(d));return b}var nslyyi=!1;
function nssnfwmip(){null==nds.config.sessionId&&nds.setSessionIdFromCookie(nsfkgjoq);null==nds.config.sessionId&&nds.setSessionIdFromSessionStorage(nsfkgjoq);null==nds.config.sessionId&&null!==nscavj&&(nds.setSessionId(nscavj),nsbopifkzi(nsfkgjoq,nscavj,!1,!0))}document.querySelector||(nslyyi=!0);(function(){nscavjydje("wk",{init:function(a){nsdwhx&&(a=Math.floor(1E6*Math.random())+1E3,(window.ndsapi||(window.ndsapi={})).setFormFieldData("wkr",a))}})})();
(function(){var a=!1,b='input[type\x3d"text"],input[type\x3d"password"]',c='input[name\x3d"remember-me"]';nscavjydje("af",{init:function(e){a=e.e;b=e.gtfs;c=e.rms;a&&d.init()},precomplete:function(b){a&&d.findTech()},rebind:function(b){a&&d.init()},stop:function(b){a&&(f.unsubscribeAll(),a=!1)}});var d=function(){return{init:function(){e.init();this.initFieldWatchers();this.findTech()},findTech:function(){try{k.findTech(),m.findTech(),g.findTech()}catch(h){}},initFieldWatchers:function(){f.unsubscribeAll();
for(var a=this.getTargetFields(),b=0;b<a.length;b+=1)f.startWatchingField(a[b])},getTargetFields:function(){return nds.common.querySelectorAll(b)}}}(),e=function(){function a(){var a=ndoGetObjectKeys(b).join(",");nds.setFormFieldData("af",a)}var b={};return{init:function(){b={};a()},reportTech:function(c){b[c]=!0;a()},reportFill:function(){b.filled=!0;a()}}}(),f=function(){function a(){var a=nds.common.createCallbackList(),d=++c;b[d]=a;a.addCallback(function(){delete b[d]});return a}var b={},c=0;
return{unsubscribeAll:function(){for(var a in b)Object.prototype.hasOwnProperty.call(b,a)&&b[a].callAllCallbacks()},startWatchingField:function(b){if(b)if(b.value)e.reportFill();else{var c=a(),d=nds.common.addEventListener(b,"focus",function(){c.callAllCallbacks()});c.addCallback(d);d=["change","input"];for(var f=0;f<d.length;f+=1){var h=nds.common.addEventListener(b,d[f],function(){b.value&&(e.reportFill(),c.callAllCallbacks())});c.addCallback(h)}}}}}(),g=function(){return{findTech:function(){try{for(var a=
nds.common.querySelectorAll(c),b=0;b<a.length;b+=1)a[b].checked&&e.reportTech("rememberme")}catch(n){}}}}(),k=function(){return{findTech:function(){try{0<nds.common.querySelectorAll(":-webkit-autofill").length&&e.reportTech("webkit")}catch(h){}}}}(),m=function(){var a={"background-attachment":"scroll","background-size":"16px 18px","background-position":"98% 50%","background-repeat":"no-repeat","background-image":/^url\("data:image\/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAASCAYAAABSO15qAAAAAXNSR0IArs4c6QAAA..JREFUOB.*"\)$/};
return{findTech:function(b){b||(b=d.getTargetFields());for(var c=0;c<b.length;c+=1){var f=b[c];a:{var h=void 0;var g=f,m=a;for(h in m)if(Object.prototype.hasOwnProperty.call(m,h)){var k=m[h],B=nds.common.util.getComputedStyle(g,h);if(k instanceof RegExp){if(!k.test(B)){h=!1;break a}}else if(B!==k){h=!1;break a}}h=!0}if(!h){h=void 0;f=f.id||f.name;try{h=document.querySelector('[id^\x3d"__lpform_'+f+'"]')}catch(U){}h=!!h}if(h){e.reportTech("lastpass");break}}}}}()})();
(function(){function a(a){d.lastIndex=0;return d.test(a)?'"'+a.replace(d,function(a){var b=g[a];return"string"===typeof b?b:"\\u"+("0000"+a.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+a+'"'}function b(a){return 10>a?"0"+a:a}function c(b,d){var h,g=e,l=d[b];l&&"object"===typeof l&&"function"===typeof l.toNDJSON&&(l=l.toNDJSON(b));"function"===typeof k&&(l=k.call(d,b,l));switch(typeof l){case "string":return a(l);case "number":return isFinite(l)?String(l):"null";case "boolean":case "null":return String(l);
case "object":if(!l)return"null";e+=f;var m=[];if("[object Array]"===Object.prototype.toString.apply(l)){var u=l.length;for(h=0;h<u;h+=1)m[h]=c(h,l)||"null";var v=0===m.length?"[]":e?"[\n"+e+m.join(",\n"+e)+"\n"+g+"]":"["+m.join(",")+"]";e=g;return v}if(k&&"object"===typeof k)for(u=k.length,h=0;h<u;h+=1){if("string"===typeof k[h]){var p=k[h];(v=c(p,l))&&m.push(a(p)+(e?": ":":")+v)}}else for(p in l)Object.prototype.hasOwnProperty.call(l,p)&&(v=c(p,l))&&m.push(a(p)+(e?": ":":")+v);v=0===m.length?"{}":
e?"{\n"+e+m.join(",\n"+e)+"\n"+g+"}":"{"+m.join(",")+"}";e=g;return v}}"function"!==typeof Date.prototype.toNDJSON&&(Date.prototype.toNDJSON=function(a){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+b(this.getUTCMonth()+1)+"-"+b(this.getUTCDate())+"T"+b(this.getUTCHours())+":"+b(this.getUTCMinutes())+":"+b(this.getUTCSeconds())+"Z":null},String.prototype.toNDJSON=Number.prototype.toNDJSON=Boolean.prototype.toNDJSON=function(a){return this.valueOf()});var d=/[\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,
e,f,g={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},k;"function"!==typeof nsgukke.stringify&&(nsgukke.stringify=function(a,b,d){var h;f=e="";if("number"===typeof d)for(h=0;h<d;h+=1)f+=" ";else"string"===typeof d&&(f=d);if((k=b)&&"function"!==typeof b&&("object"!==typeof b||"number"!==typeof b.length))throw Error("nsgukke.stringify");return c("",{"":a})});"function"!==typeof nsgukke.parse&&(nsgukke.parse=function(){var a,b,c={'"':'"',"\\":"\\","/":"/",b:"\b",f:"\f",
n:"\n",r:"\r",t:"\t"},d,e=function(c){if(c&&c!==b)throw new SyntaxError('nsgukke.parse - Expected "'+c+'" instead of "'+b+'"');b=d.charAt(a);a+=1;return b},f=function(){var a="";"-"===b&&(a="-",e("-"));for(;"0"<=b&&"9">=b;)a+=b,e();if("."===b)for(a+=".";e()&&"0"<=b&&"9">=b;)a+=b;if("e"===b||"E"===b){a+=b;e();if("-"===b||"+"===b)a+=b,e();for(;"0"<=b&&"9">=b;)a+=b,e()}var c=+a;return isFinite(c)?c:"-"===a.charAt(0)?-Infinity:Infinity},g=function(){var a,d="",f;if('"'===b)for(;e();){if('"'===b)return e(),
d;if("\\"===b)if(e(),"u"===b){for(a=f=0;4>a;a+=1){var g=parseInt(e(),16);if(!isFinite(g))break;f=16*f+g}d+=String.fromCharCode(f)}else if("string"===typeof c[b])d+=c[b];else break;else d+=b}throw new SyntaxError("nsgukke.parse - Bad string");},k=function(){for(;b&&" ">=b;)e()},p=function(){switch(b){case "t":return e("t"),e("r"),e("u"),e("e"),!0;case "f":return e("f"),e("a"),e("l"),e("s"),e("e"),!1;case "n":return e("n"),e("u"),e("l"),e("l"),null}throw new SyntaxError('nsgukke.parse - Unexpected "'+
b+'"');};var F=function(){k();switch(b){case "{":a:{var a={};if("{"===b){e("{");k();if("}"===b){e("}");var c=a;break a}for(;b;){c=g();k();e(":");a[c]=F();k();if("}"===b){e("}");c=a;break a}e(",");k()}}throw new SyntaxError("nsgukke.parse - Bad object");}return c;case "[":a:{c=[];if("["===b){e("[");k();if("]"===b){e("]");break a}for(;b;){c.push(F());k();if("]"===b){e("]");break a}e(",");k()}}throw new SyntaxError("nsgukke.parse - Bad array");}return c;case '"':return g();case "-":return f();default:return"0"<=
b&&"9">=b?f():p()}};return function(c,e){d=c;a=0;b=" ";var f=F();k();if(b)throw new SyntaxError("nsgukke.parse - Syntax error");return"function"===typeof e?function P(a,b){var c,d=a[b];if(d&&"object"===typeof d)for(c in d)if(Object.prototype.hasOwnProperty.call(d,c)){var f=P(d,c);void 0!==f?d[c]=f:delete d[c]}return e.call(a,b,d)}({"":f},""):f}}())})();
(function(){function a(a){var b=[];b.push(nds.common.bi.getScreenInfo());b.push(nds.common.bi.getDeviceTimezone());b.push(nds.common.bi.getDeviceLanguage());b.push("bp1-"+nds.common.util.quickHash(nds.common.bi.getPlugins()));b.push(nds.common.bi.isFlashInstalled().toString());var d=a.rt||128;b.push(nds.common.util.truncTo(document.referrer.replace(/\|/g,""),d));a=a.ut||512;b.push(nds.common.util.truncTo(navigator.userAgent.replace(/\|/g,""),a));a=nds.common.bi.getWebGLInfo();null===a?b.push("Not Supported"):
b.push("wg1-"+nds.common.util.quickHash(nsgukke.stringify(a)));a="b2";for(d=0;d<b.length;d++)a+="|"+b[d];return a}nscavjydje("di",{init:function(b){nds=window.ndsapi||(window.ndsapi={});if(nds.config.doNotTrack)nds.setFormFieldData("dnt",!0);else{var c={},d="NotAvail";"undefined"!==typeof navigator&&"undefined"!==typeof navigator.userAgent&&(d=navigator.userAgent);var e=window.ndsapi||(window.ndsapi={});e.config&&e.config.forceUA&&(d=e.config.forceUA);d=d.replace(/([0-9]+\.[0-9]+)\.[0-9]+\.[0-9]+/g,
"$1").replace(/([0-9]+\.[0-9]+)\.[0-9]+/g,"$1");d=d.replace(/([0-9]+_[0-9]+)_[0-9]+_[0-9]+/g,"$1").replace(/([0-9]+_[0-9]+)_[0-9]+/g,"$1");c.ua=d;c.sr=nds.common.bi.getScreenFingerprint();c.didtz=nds.common.bi.getDeviceTimezone().toString();d=nds.common.bi.getPlugins();c.bp=nds.common.util.quickHash(d);c.rbp=d;c.flv=nds.common.bi.isFlashInstalled().toString();c.fv=nds.common.bi.getHTML5SupportedVideo();c.fa=nds.common.bi.getHTML5SupportedAudio();c.hf=nds.common.util.quickHash(nds.common.bi.getHTML5CanvasSignature());
c.pl=nds.common.bi.getPlatform();c.ve=nds.common.bi.getVendor();c.ft=nsgukke.stringify(nds.common.bi.getDeviceTouchSettings());c.fc=nsgukke.stringify(nds.common.bi.getCookiesEnabled(b.ac));c.fs=nds.common.bi.getHTML5LocalStorage().toString();c.wg=nds.common.util.quickHash(nsgukke.stringify(nds.common.bi.getWebGLInfo()));c.fm=nds.common.util.quickHash(nds.common.bi.getFontMetrics().join(","));for(var f in c)c.hasOwnProperty(f)&&nds.setFormFieldData(f,c[f])}nds.setFormFieldData("bi",a(b))}})})();
function HashUtil(){}
(function(){function a(a,b,c){a=nds.common.addEventListener(a,b,c);Q.addCallback(a)}function b(a){var b=l(),c=g(qa,b,[K.length]);if(0===R||a.length+c.length<=R)return"";a=a.substring(0,R-c.length);var d=a.lastIndexOf(";");if(0>d)return"";D=b;return a.substring(0,d+1)+c+";"}function c(){L=K=w="";D=null;P&&(f(Y,[]),P=!1)}function d(){for(var a=[],b=0;b<C.length;b++){var c=C[b];c.type&&c.type.match(da)&&(a.push(c.id),a.push(c.value.length))}return a.join(",")}function e(){null!==G&&(clearInterval(G),
G=0);null!==r&&"undefined"!==typeof Z[r+1]&&(r++,G=setInterval(h,Z[r]),Q.addCallback(function(){clearInterval(G)}),!1===E?(H=1,E={pos:M,time:l()}):H=0)}function f(a,b){var c=l();if(null==D){aa=D=ha=l();var d=[nskyivz(),ra,ba];w=w+g("ncip",c,d)+";";D=c}w=w+g(a,c,b)+";";D=c;15E3<=c-aa&&(w=w+g("ts",c,[c-ha])+";",aa=D=c);switch(a){case ia:case ja:case sa:case Y:case ca:m(c);break;default:2E3<c-ka&&m(c)}}function g(a,b,c){b-=D;1<ba&&(b=Math.round(b/ba));a=a+","+b.toString(16);if(null!=c&&0<c.length){a+=
",";b=[];for(var d=0;d<c.length;d++)"number"===typeof c[d]?b.push(Math.round(c[d]).toString(16)):null!=c[d]&&b.push(c[d].toString());c=b.join(",");a+=c}return a}function k(a){if(!(q in a))return null;"string"===typeof a[q].id&&""!==a[q].id?a=a[q].id:"string"===typeof a[q].name&&""!==a[q].name?a=a[q].name:(a=Array.prototype.slice.call(C).indexOf(a[q]),a=0<=a?"ndiprinput"+a:"");return a}function m(a){ka=a;w&&(a=window.ndsapi||(window.ndsapi={}),K+=w,L=b(K),!0===U&&v(!1),w="",0<L.length?a.setFormFieldData("ipr",
L):a.setFormFieldData("ipr",K))}function h(){var a=l();if(!1!==E){var b=Math.abs(M.x-E.pos.x)*fa,c=Math.abs(M.y-E.pos.y)*ea,d=(a-E.time)/1E3,g=n(b/d,4,!0),k=n(c/d,4,!0);c=Math.sqrt(Math.pow(b,2)+Math.pow(c,2));b=n(c/d,4,!0);(null===S||b<S)&&0!==b&&(x=[g,k],S=b);(null===T||b>T)&&0!==b&&(y=[g,k],T=b);I+=b;J+=c;null!==V&&(d=(b-V)/d,(null===z||d<z)&&0!==d&&(z=d),(null===A||d>A)&&0!==d&&(A=d),N+=d);V=b}0!==H&&0===H%O[r]&&(d=0===W?0:a-W,I=n(I/O[r],4,!1),J=n(J,4,!1),g=0,0===d&&(g=-1),N=n(N/(O[r]+g),4,!1),
null===x&&null===y&&0===I&&0===J?f(ca,[d,O[r],"NOP"]):(x[0]=n(x[0],4,!1).toString(16),x[1]=n(x[1],4,!1).toString(16),y[0]=n(y[0],4,!1).toString(16),y[1]=n(y[1],4,!1).toString(16),z=null!==z?n(z,4,!1):0,A=null!==A?n(A,4,!1):0,f(ca,[d,O[r],x[0]+" "+x[1],y[0]+" "+y[1],I,J,z,A,N])),T=y=S=x=null,J=I=0,A=z=null,N=0,W=a);E={pos:M,time:a};H>=la[r]&&e();H++}function B(a){var b=0,c=0;if(a.pageX||a.pageY)b=a.pageX,c=a.pageY;else if(a.clientX||a.clientY)b=a.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,
c=a.clientY+document.body.scrollTop+document.documentElement.scrollTop;return{x:b,y:c}}function n(a,b,c){a=Math.round(a*Math.pow(10,b));if("undefined"===typeof c||!0===c)a/=Math.pow(10,b);return a}function l(){return parseInt((new Date).getTime(),10)}function X(b,c){var d,e=[];if(null===b||"undefined"===typeof b||0===b.length)e=document.documentElement.getElementsByTagName("input");else if(("object"===typeof b||"function"===typeof b)&&"number"===typeof b.length){for(d=0;d<b.length;d++){var f=b[d];
"string"===typeof f?(f=document.getElementById(f),"undefined"!==typeof f&&null!==f&&e.push(f)):"object"===typeof f&&null!==f&&e.push(f)}null!==C&&(e=e.concat(C))}f=[];for(d=0;d<e.length;d++){var t=e[d];t.type&&t.type.match(da)&&(c||0!==t.offsetWidth||0!==t.offsetHeight)&&(f.push(t),a(t,"keydown",pa),a(t,"focus",ta),a(t,"blur",ua))}C=nslgfnpyxj(f);a(document,"click",va);a(document,"touchstart",wa);a(document,"mousemove",xa)}function u(a){a||(a=window.event);var b=null;a.target?b=a.target:a.srcElement&&
(b=a.srcElement);3==b.nodeType&&(b=b.parentNode);if(a.keyCode)var c=a.keyCode;else a.which&&(c=a.which);var d=!1;a.which?d=3==a.which:a.button&&(d=2==a.button);var e=B(a),f={};f.a=a;f[q]=b;f.c=d;f.d=c;f.e=e.x;f.f=e.y;return f}function v(a){if(0<nsdwhxur.length&&nsdwhxur[0].value){var b=nsdwhxur[0].value,c="",d="",e="",f="";nsgukk.hasOwnProperty("pageModeConfig")&&!0===nsgukk.pageModeConfig.encodeData&&(b=nshyfqpw(b));try{d=nsgukke.parse(b);d[nsfkgjoqr]||(d[nsfkgjoqr]={});var g=0<L.length?L:K.toString(),
t=d[nsfkgjoqr].hasOwnProperty("ipr")?d[nsfkgjoqr].ipr:"";!0===a&&g.length!==t.length&&(g=t);var k={ipr:g,forceIP:nds.config.forceIP,wp:nds.config.placement,wpp:nds.config.placementPage,dtrk:nds.config.doNotTrack};"sid"in nsgukkebk.pmd&&null!=nds.config.sessionId&&0<nds.config.sessionId.length&&(d.sid=nds.config.sessionId);for(var h in nsgukkebk.pmd)nsgukkebk.pmd.hasOwnProperty(h)&&(e=h,f=d[nsfkgjoqr].hasOwnProperty(e)?d[nsfkgjoqr][e]:"",k.hasOwnProperty(e)?f=k[e]:nslgf.hasOwnProperty(e)&&(f=nslgf[e]),
d[nsfkgjoqr][e]=f);c=nsgukke.stringify(d);nsgukk.hasOwnProperty("pageModeConfig")&&!0===nsgukk.pageModeConfig.encodeData&&(c=nshyfqpw(c));for(a=0;a<nsdwhxur.length;a++)nsdwhxur[a].value=c}catch(Ba){}}}var p=0,F=!1,U=!0,C=null,da=/^(text|password|email|url|search|tel)$/i,ea=1/window.screen.height,fa=1/window.screen.width,Q={addCallback:function(){},callAllCallbacks:function(){}},P=!1,R=0;nscavjydje("ipr",{init:function(a){var b=window.ndsapi||(window.ndsapi={}),e=a.hasOwnProperty("fm")?a.fm:null;F=
a.hasOwnProperty("bh")?a.bh:!1;R=a.hasOwnProperty("tl")?a.tl:R;p=0;C=null;ea=1/window.screen.height;fa=1/window.screen.width;U=!0;Q.callAllCallbacks();Q=nds.common.createCallbackList();G=M=null;E=r=!1;H=0;Z=[100,200,2E3];la=[100,300,150];O=[10,50,30];W=0;T=y=S=x=null;J=I=0;A=z=V=null;N=0;X(e,F);b.setFormFieldData("ipr","");c();a=d();f(ma,[a]);(a=document.activeElement)&&-1<C.indexOf(a)&&(b={},b[q]=a,f(na,[k(b)]))},reinit:function(){v(!0);!0===nsgukk.eventModeEnabled&&(U=!1);c()},rebind:function(a,
b){X(b,F);var c=d();f(ma,[c])},clear:c,stop:function(){Q.callAllCallbacks();f(Y,[]);P=!0}});var q="b",pa=function(a){u(a);f(ya,[])},ta=function(a){a=u(a);f(za,[q in a&&"undefined"!==typeof a[q].value?a[q].value.length:null,k(a)]);f(na,[k(a)])},ua=function(a){a=u(a);f(ia,[k(a)])},va=function(a){a=u(a);f(ja,[a.e,a.f,k(a)])},wa=function(a){a=u(a);a.a&&a.a.touches&&a.a.touches[0]&&"undefined"!==typeof a.a.touches[0].pageX?f(oa,[a.a.touches[0].pageX,a.a.touches[0].pageY,k(a)]):f(oa,[-1,-1,k(a)])},xa=function(a){M=
B(a);null===G&&(r=-1,e());if(nskyivz()<p)return!1;p=nskyivz()+5;a=u(a);f(Aa,[a.e,a.f,k(a)])},M=null,G=null,r=!1,E=!1,H=0,Z=[100,200,2E3],la=[100,300,150],O=[10,50,30],W=0,x=null,S=null,y=null,T=null,I=0,J=0,V=null,z=null,A=null,N=0,na="ff",ia="fb",ya="kd",Aa="mm",ja="mc",oa="te",sa="fs",za="kk",ma="st",ca="mms",Y="so",qa="tr",ra=2,ba=1,ha=null,D=null,ka=null,aa=null,w="",K="",L=""})();
function nsagvvlnuk(a){return null!==a&&("object"===typeof a||"function"===typeof a)&&"number"===typeof a.length&&"undefined"!==typeof a.item}
(function(){var a=window.ndsapi||(window.ndsapi={});a.configure3DS=function(b){a.config=a.config||{};a.config.threeDSConfig=a.config.threeDSConfig||{};a.ThreeDS={};nsqlyrfuyo(b,function(c,d){!1===a.config.threeDSConfig.hasOwnProperty(d)&&(a.config.threeDSConfig[d]={});a.config.threeDSConfig[d]=b[d]})};a.start3DS=function(){if(a.hasOwnProperty("config")&&a.config.hasOwnProperty("threeDSConfig")&&0<nsviymj(a.config.threeDSConfig)){var b=a.config.threeDSConfig,c={iframeElement:null,callbacks:null,notificationUrl:null},
d=[];nsqlyrfuyo(c,function(a,e){b.hasOwnProperty(e)?c[e]=b[e]:d.push(e)});var e=function(a,b){var d=c.callbacks;d&&d.hasOwnProperty(a)&&"function"===typeof d[a]&&d[a].apply(this,b)};if("undefined"!==typeof ThreeDSWidget)if(a.ThreeDS.ThreeDSWidget=ThreeDSWidget,a.ThreeDS.Error=ThreeDSError,a.ThreeDS.MsgType=MsgType,a.ThreeDS.ErrorCode=ErrorCode,a.ThreeDS.ErrorComponent=ErrorComponent,a.ThreeDS.TransactionStatus=TransactionStatus,0===d.length)e=c.iframeElement,e="string"===typeof e||e instanceof String?
nstnemtg(e):e,(new ThreeDSWidget(c.notificationUrl)).start({iframeElement:e,callbacks:c.callbacks});else{var f=new ThreeDSError;f.setErrorDescription("Missing Configuration");f.setErrorDetail(d.join(","));f.setMessageType(MsgType.ERRO);f.setErrorCode(ErrorCode.PERMANENT_SYSTEM_FAILURE);f.setErrorComponent(ErrorComponent.THREEDS_SDK);e("error",[f,ThreeDS])}}};"function"!==typeof a.load&&(a.load=function(){if(nslyyi)a.configure3DS=function(){},a.start3DS=function(){};else for(var b=a.config.q,c=0;c<
b.length;++c){var d=b[c];"function"===typeof d&&d()}})})();var TextUtils={stringify:function(a){return null===ObjectUtils.normalize(a)?null:a.toString()},stringifyToCharSequence:function(a){a=ObjectUtils.normalize(a);return a instanceof SecureString||null===a?a:a.toString()},concat:function(a,b){var c="",d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];null!==ObjectUtils.normalize(e)&&0<e.length&&(0!==c.length&&(c+=a),c+=e)}return c},isEmpty:function(a){return null===ObjectUtils.normalize(a)||"string"===typeof a&&0===a.length},isEqual:function(a,b){return a===
b},copy:function(a){return a instanceof SecureString?a.getValue():"string"===typeof a?a:null}};NDObject.create="function"===typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b};
var ObjectUtils={normalize:function(a){return"undefined"===typeof a?null:a},isEmpty:function(a){return null===ObjectUtils.normalize(a)||ObjectUtils.isEmptyMap(a)||ObjectUtils.isEmptyDCO(a)||ObjectUtils.isArray(a)&&0===a.length},isArray:function(a){return"undefined"===typeof Array.isArray?"[object Array]"===Object.prototype.toString.call(a):Array.isArray(a)},isMap:function(a){return"[object Object]"===Object.prototype.toString.call(a)&&a.constructor==={}.constructor},isEmptyMap:function(a){if(!ObjectUtils.isMap(a))return!1;
for(var b in a)if(a.hasOwnProperty(b))return!1;return!0},isEmptyDCO:function(a){return Interface.isInstanceOf(a,IMetadataProvider)&&a.isEmpty()},mergeObjects:function(a){for(var b={},c=0;c<a.length;c++)for(var d in a[c])a[c].hasOwnProperty(d)&&(b[d]=a[c][d]);return b}};IMetadataProvider.prototype.getFieldMetadata=function(){};IMetadataProvider.prototype.markAsMalformed=function(){};IMetadataProvider.prototype.getMalformedFields=function(){};IMetadataProvider.prototype.isEmpty=function(){};
ICustomConvertible.prototype.setRawValue=function(){};ICustomConvertible.prototype.getValueForSerialization=function(){};ICustomConvertible.prototype.getAllowedProperties=function(){};IValidatable.prototype.validate=function(){};ICopyable.prototype.copy=function(){};INonStringable.prototype.getValue=function(){};ISyncable.prototype.getFieldMetadata=IMetadataProvider.prototype.getFieldMetadata;ISyncable.prototype.markAsMalformed=IMetadataProvider.prototype.markAsMalformed;
ISyncable.prototype.getMalformedFields=IMetadataProvider.prototype.getMalformedFields;
var Interface={isInstanceOf:function(a,b){return null!==ObjectUtils.normalize(a)&&(a instanceof b||a.hasOwnProperty&&a.hasOwnProperty("mInterfaces")&&a.mInterfaces&&-1!==a.mInterfaces.indexOf(b))},implement:function(a,b){if(b.prototype)for(var c in b.prototype)if(b.prototype.hasOwnProperty(c)&&!(c in a&&"function"===typeof a[c]))throw Error(c+" is not implemented.");a instanceof b||ObjectUtils.isArray(a.mInterfaces)&&-1!==a.mInterfaces.indexOf(b)||(a.mInterfaces=a.mInterfaces||[],a.mInterfaces.push(b))}},
GlobalError="undefined"!==typeof window?Error:global.Error;BaseException.prototype=NDObject.create(GlobalError.prototype);Property.prototype.toString=function(){return this.value};Property.MIN=new Property("min");Property.MAX=new Property("max");Property.MIN_LENGTH=new Property("minLength");Property.MAX_LENGTH=new Property("maxLength");Property.LENGTH=new Property("length");Property.FORMAT=new Property("format");Property.ALLOW_EMPTY=new Property("allowEmpty");Property.ARGUMENT_KEY=new Property("argumentKey");
Property.STRICT=new Property("strict");function JsonDecodeException(a,b){BaseException.call(this,a,b)}Property.VALUE=new Property("value");ValidationDelegate.OBJECT=new ValidationDelegate;ValidationDelegate.ARRAY=new ValidationDelegate;ValidationDelegate.MAP=new ValidationDelegate;ValidationDelegate.DOUBLE=new ValidationDelegate;ValidationDelegate.INT=new ValidationDelegate;ValidationDelegate.STRING=new ValidationDelegate;ValidatorContext.prototype.toString=function(){return this.value};
ValidatorContext.T=new ValidatorContext("T");ValidatorContext.THIS=new ValidatorContext("This");SetterResult.SUCCESS=new SetterResult;SetterResult.NOT_APPLICABLE=new SetterResult;SetterResult.FAILURE=new SetterResult;ValidatorMetadata.prototype.getDelegate=function(){return this.mValidationDelegate};ValidatorMetadata.prototype.getContexts=function(){return this.mContexts};ValidatorMetadata.prototype.getProperties=function(){return this.mProperties};
Builder.prototype.addContext=function(a){this.mContexts.push(a.toString());return this};Builder.prototype.addProperty=function(a,b){this.mProperties[a.toString()]=b;return this};Builder.prototype.addProperties=function(a){for(var b in a)a.hasOwnProperty(b)&&(this.mProperties[b.toString()]=a[b]);return this};function TransactionStatus(a,b){Enumerable.call(this,a,TransactionStatus.VALUES,b);b&&(TransactionStatus.VALUES[a]=this)}Builder.prototype.setDelegate=function(a){this.mValidationDelegate=a;return this};
Builder.prototype.build=function(){return new ValidatorMetadata(this)};FieldMetadata.prototype.addValidator=function(a){this.mValidatorMetadata.push(a);return this};FieldMetadata.prototype.setValueRetriever=function(a){this.mValueRetriever=a;return this};FieldMetadata.prototype.setValueSetter=function(a){this.mValueSetter=a;return this};FieldMetadata.prototype.setValueInitializer=function(a){this.mValueInitializer=a;return this};FieldMetadata.prototype.setFieldType=function(a){this.mFieldType=a;return this};
function WindowSize(a,b){Enumerable.call(this,a,WindowSize.VALUES,b);b&&(WindowSize.VALUES[a]=this)}FieldMetadata.prototype.enableDcoSync=function(){this.mDcoSyncEnabled=!0;return this};FieldMetadata.prototype.enableSerialization=function(){this.mSerializationEnabled=!0;return this};FieldMetadata.prototype.setSerializationTarget=function(a){this.mSerializationTarget=a;return this};FieldMetadata.prototype.isDcoSyncEnabled=function(){return this.mDcoSyncEnabled};
FieldMetadata.prototype.isSerializationEnabled=function(){return this.mSerializationEnabled};FieldMetadata.prototype.getSerializationTarget=function(){return this.mSerializationTarget};FieldMetadata.prototype.getValidatorMetadata=function(){return this.mValidatorMetadata};FieldMetadata.prototype.getValue=function(a){return this.mValueRetriever(a)};FieldMetadata.prototype.setValue=function(a,b){return"function"===typeof this.mValueSetter?this.mValueSetter(a,b):null};
FieldMetadata.prototype.initValue=function(a){return"function"===typeof this.mValueInitializer?this.mValueInitializer(a):null};FieldMetadata.prototype.getFieldType=function(){return this.mFieldType};FieldMetadata.ValidatorMetadata=ValidatorMetadata;FieldMetadata.ValidationDelegate=ValidationDelegate;FieldMetadata.ValidatorContext=ValidatorContext;FieldMetadata.SetterResult=SetterResult;ValidatorMetadata.Builder=Builder;InvalidValueException.prototype=NDObject.create(BaseException.prototype);
Enumerable.prototype.toString=function(){return this.mValue};Enumerable.prototype.toJSON=function(){return this.mValue};Enumerable.prototype.validate=function(a,b){var c=new ValidationResult;this.mIsValid||(!this.mValue&&b&&!1===b[Property.ALLOW_EMPTY.toString()]?c.fail(a,b,[Property.ALLOW_EMPTY]):c.fail(a,b,[Property.VALUE]));return c};
Enumerable.prototype.hashCode=function(){for(var a=Math.pow(2,32),b=0;0===this.mHash||b<this.mValue.length;b++)this.mHash=31*this.mHash+this.mValue.charCodeAt(b),this.mHash%=a;return this.mHash};Type.Double=new Type;Type.Int=new Type;Type.Bool=new Type;function Serializable(){this.object=null}Type.String=new Type;Type.IP=new Type;Type.Email=new Type;Type.WidgetBrowserData=new Type;Type.UUID=new Type;Type.TernaryYesNoUnavailable=new Type;Type.MsgType=new Type;Type.MessageVersionType=new Type;
Type.ErrorCode=new Type;Type.ErrorComponent=new Type;Type.BooleanYesNo=new Type;Type.ACSRenderingType=new Type;Type.URL=new Type;function ICustomConvertible(){}Type.AuthenticationType=new Type;Type.Base64=new Type;function CryptoType(){}Type.Object=new Type;Type.MessageExtensionAttributes=new Type;Type.TransactionStatus=new Type;Type.TransactionReason=new Type;Type.Base64URL=new Type;Type.WhitelistStatus=new Type;Type.WhitelistStatusSource=new Type;Type.ApiVersionType=new Type;
function ValidatorMetadata(a){this.mValidationDelegate=a.mValidationDelegate;this.mContexts=a.mContexts;this.mProperties=a.mProperties}Type.ThreeDSError=new Type;Type.ChallengeOutcome=new Type;Type.ResultsResponse=new Type;Type.BrowserColorDepthType=new Type;Type.WindowSize=new Type;Type.ACSInterface=new Type;Type.ACSUITemplate=new Type;Type.EMVErrorMessage=new Type;Type.ImageSizes=new Type;Type.CancelIndicator=new Type;Type.MessageCategoryType=new Type;Type.Unknown=new Type;Init.Type=Type;
ValidationResult.prototype.fail=function(a,b,c){this.mFailures.push(new Failure(a,b||null,c))};ValidationResult.prototype.failWithMetadata=function(a,b,c){a=new Failure(a,null,c);a.mFieldMetadata=b;this.mFailures.push(a)};ValidationResult.prototype.failIf=function(a,b){b=b||null;if(!a.isSuccess()){var c=a.getFailures(),d;for(d in c){var e=c[d];null!==b&&(e.mAllArguments=ObjectUtils.mergeObjects(e.mAllArguments||{},b))}this.mFailures=this.mFailures.concat(c)}};
ValidationResult.prototype.failWithMetadataIf=function(a,b){if(null!=a){for(var c in a.mFailures){var d=a.mFailures[c];null===ObjectUtils.normalize(d.mFieldMetadata)&&(d.mFieldMetadata=b);null===ObjectUtils.normalize(d.mFieldMetadata)&&(d.mFieldMetadata=new FieldMetadata)}this.mFailures=this.mFailures.concat(a.mFailures)}};ValidationResult.prototype.getFailures=function(){return this.mFailures};ValidationResult.prototype.isSuccess=function(){return 0===this.mFailures.length};
Failure.prototype.getFieldName=function(){return this.mFieldName};Failure.prototype.getFieldMetadata=function(){return this.mFieldMetadata};Failure.prototype.getAllArguments=function(){return this.mAllArguments};Failure.prototype.getFailedProperties=function(){return this.mFailedProperties};ValidationResult.Failure=Failure;Filter.prototype=NDObject.create(ValidationResult.prototype);Filter.prototype.isSuccess=function(){return ValidationResult.prototype.isSuccess.call(this)&&this.mFilteredValueSet};
Filter.prototype.isNonNullSuccess=function(){return this.isSuccess()&&null!==ObjectUtils.normalize(this.mFilteredValue)};Filter.prototype.getFilteredValue=function(){if(!this.isSuccess())throw new TypeError("Cannot get filtered value on unsuccessful validation");if(!this.mFilteredValueSet)throw new TypeError("Filtered value has not been set");return this.mFilteredValue};Filter.prototype.setFilteredValue=function(a){this.mFilteredValue=a;this.mFilteredValueSet=!0};ValidationResult.Filter=Filter;
CustomConvertible.prototype=NDObject.create(ICustomConvertible.prototype);CustomConvertible.prototype.getValue=function(){return this.mValue};CustomConvertible.prototype.setValue=function(a){null==this.mRawValue&&null!=a&&(this.mRawValue=a);this.mValue=a};CustomConvertible.prototype.setRawValue=function(a){this.mRawValue=a};CustomConvertible.prototype.getRawValue=function(){return this.mRawValue};
CustomConvertible.prototype.getValueForSerialization=function(){var a=this.getValue();return null===ObjectUtils.normalize(a)?this.mRawValue:a};CustomConvertible.prototype.equals=function(a){if(this===a)return!0;if(null===ObjectUtils.normalize(a)||a.constructor!==this.constructor)return!1;a=a.getValue();return this.getValue()===a};CustomConvertible.prototype.toString=function(){return TextUtils.stringify(this.getValueForSerialization())};CustomConvertible.prototype.getAllowedProperties=function(){return[]};
StringValidator.prototype=NDObject.create(BaseValidator.prototype);
StringValidator.prototype.validate=function(a,b,c){var d=new ValidationResult;d.failIf(this._validateArguments(b,a));c=TextUtils.stringifyToCharSequence(c);var e=null===c?null:c.length,f=!1!==a[Property.ALLOW_EMPTY.toString()];if("undefined"!==typeof a[Property.LENGTH.toString()]){var g=a[Property.LENGTH.toString()];d.failIf(MathUtil.validateEqualOrNull(b,e,g,this._getFailingProperty(Property.LENGTH,f,g)),a)}"undefined"!==typeof a[Property.MIN_LENGTH.toString()]&&(g=a[Property.MIN_LENGTH.toString()],
d.failIf(MathUtil.validateGreaterThanEqualOrNull(b,e,g,this._getFailingProperty(Property.MIN_LENGTH,f,g)),a));"undefined"!==typeof a[Property.MAX_LENGTH.toString()]&&d.failIf(MathUtil.validateLessThanEqualOrNull(b,e,a[Property.MAX_LENGTH.toString()],Property.MAX_LENGTH),a);e=TextUtils.stringify(a[Property.FORMAT.toString()]);null!==c&&null!==e&&((c=TextUtils.copy(c))&&(new RegExp(e)).test(c)||d.fail(b,a,[Property.FORMAT]));return d};
function SecureString(a,b){CustomConvertible.call(this);b=b||null;this.mCryptoProvider=a||null;this.mCryptoType=CryptoType.INVALID;Interface.implement(this,ICopyable);Interface.implement(this,INonStringable);null!==ObjectUtils.normalize(b)&&this.encryptAndSet(b)}StringValidator.prototype.getAllowedProperties=function(){return StringValidator.ALLOWED_PROPERTIES};StringValidator.prototype._getFailingProperty=function(a,b,c){return b||1!==c?a:Property.ALLOW_EMPTY};
StringValidator.ALLOWED_PROPERTIES=[Property.LENGTH,Property.MIN_LENGTH,Property.MAX_LENGTH,Property.FORMAT,Property.ALLOW_EMPTY];IllegalArgumentException.prototype=NDObject.create(BaseException.prototype);CryptoType.PLAIN_TEXT=new CryptoType;CryptoType.BASE64=new CryptoType;CryptoType.INVALID=new CryptoType;JsonDecodeException.prototype=NDObject.create(BaseException.prototype);function MapContainerValidator(a,b,c){BaseContainerValidator.call(this,a,b,c,!0)}
function Phone(a){CustomConvertible.call(this);this.mDefaultRegion="US";this.setRawValue(a||null)}SecureString.prototype=NDObject.create(CustomConvertible.prototype);SecureString.prototype.setCryptoProvider=function(a){this.mCryptoProvider=a};SecureString.prototype.toString=function(){throw new TypeError;};
SecureString.prototype.encryptAndSet=function(a,b){b=b||null;null===ObjectUtils.normalize(this.mCryptoProvider)||this.mCryptoProvider.getEncryptionType();this.mCryptoType=b;switch(b){case CryptoType.BASE64:var c=null;break;case CryptoType.PLAIN_TEXT:c=null===ObjectUtils.normalize(a)?null:a;break;default:c=null}CustomConvertible.prototype.setValue.call(this,this.getEncryptionString(b,c))};
SecureString.prototype.copy=function(){var a=new SecureString(this.mCryptoProvider);a.mCryptoType=this.mCryptoType;a.setRawValue(this.getValue());return a};SecureString.prototype.isEncrypted=function(a){a=a||this.mCryptoType;return a!==CryptoType.PLAIN_TEXT};SecureString.prototype.setValue=function(){throw new TypeError("setValue is not supported. Use encryptAndSet.");};
SecureString.prototype.setRawValue=function(a){CustomConvertible.prototype.setValue.call(this,a);CustomConvertible.prototype.setRawValue.call(this,a)};SecureString.prototype.validate=function(){return new ValidationResult};SecureString.prototype.getValueForSerialization=function(){try{return CustomConvertible.prototype.getValueForSerialization.call(this)}finally{this.mCryptoType=CryptoType.INVALID,this.setRawValue(null),CustomConvertible.prototype.setValue.call(this,null)}};
function APIResponse(){HTTPLimitedTimingData.call(this);Interface.implement(this,ISyncable)}SecureString.prototype.getAllowedProperties=function(){return StringValidator.ALLOWED_PROPERTIES};JsonSerializer.FIXED_DIGITS=20;
JsonSerializer.prototype.readMapIntoInstance=function(a,b,c){var d=a.getFieldMetadata(),e;for(e in b)if(b.hasOwnProperty(e)){var f=b[e],g=e in d?d[e]:null;null!==g&&TextUtils.isEmpty(g.getSerializationTarget())&&this.injectValueIntoInstance(a,g,f,c)}this.readMapIntoInstanceTargets(a,d,b,c)};
JsonSerializer.prototype.readMapIntoInstanceTargets=function(a,b,c,d){for(var e in b)if(b.hasOwnProperty(e)){var f=b[e],g=f.getSerializationTarget(),k=f.getValue(a);null===ObjectUtils.normalize(k)&&(k=f.initValue(null),Interface.isInstanceOf(k,IMetadataProvider)&&f.setValue(a,k));Interface.isInstanceOf(k,IMetadataProvider)&&this.readMapIntoInstanceTargets(k,k.getFieldMetadata(),c,d);TextUtils.isEmpty(g)||(g=this.extractValue(a,f,g,d),this.injectValueIntoInstance(a,f,g,d))}};
JsonSerializer.prototype.extractValue=function(a,b,c,d){if(!c||1>c.length)return null;c=c.split(".");for(var e=null,f=0;null===e&&f<c.length;f++){var g=d[c[f]];null!==ObjectUtils.normalize(g)&&(f===c.length-1?e=g:"object"!==typeof g||Array.isArray(g)?(a.markAsMalformed(b),e=null):d=g)}return e};
JsonSerializer.prototype.injectValueIntoInstance=function(a,b,c,d){if(this.checkDataType(b,a,c)&&!ObjectUtils.isEmpty(c)){var e=b.getValue(a),f=e,g=b.initValue(c);null!==ObjectUtils.normalize(g)&&!this.isContainer(e)&&b.isSerializationEnabled()&&(b.setValue(a,g),f=g);Interface.isInstanceOf(f,IMetadataProvider)&&b.isDcoSyncEnabled()&&"object"===typeof c&&null!==c&&!Array.isArray(c)?(this.readFromMetadataProviderIntoInstance(f,d,c),FieldMetadata.SetterResult.FAILURE===b.setValue(a,this.prepareValue(f,
b.getFieldType()))&&a.markAsMalformed(b)):Array.isArray(e)?Array.isArray(c)&&this.readFromListIntoInstance(a,c,d,b):ObjectUtils.isMap(e)?ObjectUtils.isMap(c)&&this.readFromMapIntoInstance(a,c,d,b):FieldMetadata.SetterResult.FAILURE===b.setValue(a,this.prepareValue(f,b.getFieldType()))&&a.markAsMalformed(b)}};JsonSerializer.prototype.isContainer=function(a){return"object"===typeof a&&null!==a};
JsonSerializer.prototype.checkDataType=function(a,b,c){var d=null===ObjectUtils.normalize(a)?null:a.getValue(b),e=null===ObjectUtils.normalize(a)||this.isContainer(d)||a.isDcoSyncEnabled()||Init.Type.Object===a.getFieldType();(c=null!==ObjectUtils.normalize(a)&&a.isSerializationEnabled()&&null!=ObjectUtils.normalize(c)?Array.isArray(d)&&!Array.isArray(c)?!1:"object"!==typeof d||null===d||Array.isArray(d)||"object"===typeof c&&null!==d&&!Array.isArray(c)?e||!this.isContainer(c)||Interface.isInstanceOf(a.initValue(null),
ICustomConvertible):!1:!0)||b.markAsMalformed(a);return c};JsonSerializer.prototype.prepareValue=function(a,b){var c=a;"number"!==typeof a||isNaN(a)||(Init.Type.Double===b?c=a.toFixed(JsonSerializer.FIXED_DIGITS):Init.Type.Integer===b&&(c=a.toFixed(0)));return c};
JsonSerializer.prototype.readFromListIntoInstance=function(a,b,c,d){for(var e in b)if(b.hasOwnProperty(e)){var f=b[e],g=d.initValue(this.prepareValue(f,d.getFieldType()));Interface.isInstanceOf(g,IMetadataProvider)&&d.isDcoSyncEnabled()&&this.readFromMetadataProviderIntoInstance(g,c,f);FieldMetadata.SetterResult.FAILURE===d.setValue(a,this.prepareValue(g,d.getFieldType()))&&a.markAsMalformed(d)}};
JsonSerializer.prototype.readFromMapIntoInstance=function(a,b,c,d){for(var e in b)if(b.hasOwnProperty(e)){var f=b[e],g=d.initValue(this.prepareValue(f,d.getFieldType()));Interface.isInstanceOf(g,IMetadataProvider)&&d.isDcoSyncEnabled()&&this.readFromMetadataProviderIntoInstance(g,c,f);FieldMetadata.SetterResult.FAILURE===d.setValue(a,[TextUtils.stringify(e),this.prepareValue(g,d.getFieldType())])&&a.markAsMalformed(d)}};
JsonSerializer.prototype.readFromMetadataProviderIntoInstance=function(a,b,c){this.readMapIntoInstance(a,c,b)};
JsonSerializer.prototype.toMap=function(a,b,c){var d=a.getFieldMetadata(),e;for(e in d)if(d.hasOwnProperty(e)){var f=d[e];if(f.isSerializationEnabled()){var g=f.getValue(a);if(null!==ObjectUtils.normalize(g)){var k=f.getSerializationTarget(),h=b[e];h="object"!==typeof h||null===h||Array.isArray(h)?{}:h;g=this.toSerializable(g,h,c);ObjectUtils.isEmptyMap(h)||(g.object=h);if(f.isSerializationEnabled()&&null!==ObjectUtils.normalize(g.object))if(TextUtils.isEmpty(k))b[e]=g.object;else for(f=k.split("."),
h=c,k=0;k<f.length;k++)if(k===f.length-1)h[f[k]]=g.object;else{var m=h[f[k]];m="object"!==typeof m||null===m||Array.isArray(m)?{}:m;h=h[f[k]]=m}}}}};function Convertible(){IMetadataProvider.call(this);this.mMalformedFields=[]}
JsonSerializer.prototype.toSerializable=function(a,b,c){var d=new Serializable;a=null!==ObjectUtils.normalize(this.mValueProcessor)?this.mValueProcessor(a):a;Interface.isInstanceOf(a,Enumerable)?d.object=a.toString():Interface.isInstanceOf(a,IMetadataProvider)?(this.toMap(a,b,c),d.object=null):ObjectUtils.isMap(a)?d.object=this.toSerializableMap(a,c):ObjectUtils.isArray(a)?d.object=this.toSerializableList(a,c):null!==ObjectUtils.normalize(a)&&(d.object=a);return d};
JsonSerializer.prototype.toSerializableList=function(a,b){var c=[],d;for(d in a)if(a.hasOwnProperty(d)){var e={},f=this.toSerializable(a[d],e,b).object;ObjectUtils.isEmptyMap(e)?ObjectUtils.isEmpty(f)||c.push(f):c.push(e)}return 0===c.length?null:c};
JsonSerializer.prototype.toSerializableMap=function(a,b){var c={},d;for(d in a)if(a.hasOwnProperty(d)){var e={},f=this.toSerializable(a[d],e,b).object;ObjectUtils.isEmptyMap(e)?ObjectUtils.isEmpty(f)||(c[TextUtils.stringify(d)]=f):c[TextUtils.stringify(d)]=e}return ObjectUtils.isEmptyMap(c)?null:c};JsonSerializer.toJson=function(a,b){return JSON.stringify(JsonSerializer.toMap(a,b)).replace(/=/g,"\\u003d")};
JsonSerializer.readJsonIntoInstance=function(a,b){var c=null;try{c=JSON.parse(b)}catch(d){throw new JsonDecodeException(d.message,d);}if(null===c)throw new JsonDecodeException("JSON decode produced a NULL result.");(new JsonSerializer).readMapIntoInstance(a,c,c)};JsonSerializer.toMap=function(a,b){var c={};(new JsonSerializer(function(a){a=b?b(a):a;return Interface.isInstanceOf(a,ICustomConvertible)?a.getValueForSerialization():a})).toMap(a,c,c);return c};JsonSerializer.Serializable=Serializable;
var MetadataUtils={isInstanceOf:function(a,b,c){switch(c){case "Bool":return"boolean"===typeof a;case "String":return"string"===typeof a;case "Int":case "Double":return"number"===typeof a;case "Object":return null!==ObjectUtils.normalize(a);default:return a instanceof b||Interface.isInstanceOf(a,b)}},getFieldPath:function(a,b,c){b=a.isSerializationEnabled()?TextUtils.concat(".",[b,c]):b;a=a.getSerializationTarget();return TextUtils.isEmpty(a)?b:a}};Convertible.prototype=NDObject.create(IMetadataProvider.prototype);
function ThreeDSResponse(a,b){this.encodedBody=a;this.headers=b;this.decodedBody=null}function IntValidator(a){BaseValidator.call(this,a)}Convertible.prototype.toJson=function(){return JsonSerializer.toJson(this)};Convertible.prototype.toMap=function(){return JsonSerializer.toMap(this)};
Convertible.prototype.getProperties=function(){var a=[],b=this.getFieldMetadata(),c;for(c in b)if(b.hasOwnProperty(c)){var d=b[c];d.isSerializationEnabled()&&(d=d.getValue(this),a.push(ObjectUtils.isEmpty(d)?null:d))}return a};function ImageSizes(){Convertible.call(this);this.extraHigh=this.high=this.medium=null;Interface.implement(this,ISyncable)}Convertible.prototype.markAsMalformed=function(a){this.mMalformedFields.push(a)};
Convertible.prototype.getMalformedFields=function(a){var b={},c=this.getFieldMetadata(),d;for(d in c)if(c.hasOwnProperty(d)){var e=c[d],f=e.getSerializationTarget()||TextUtils.concat(".",[a,d]);if(-1!==this.mMalformedFields.indexOf(e))b[f]=e;else if(e=e.getValue(this),Interface.isInstanceOf(e,IMetadataProvider)){f=e.getMalformedFields(f);for(var g in f)f.hasOwnProperty(g)&&(b[g]=f[g])}}return b};
Convertible.prototype.isEmpty=function(){var a=this.getProperties(),b;for(b in a)if(a.hasOwnProperty(b)&&!ObjectUtils.isEmpty(a[b]))return!1;return!0};UUID.prototype=NDObject.create(CustomConvertible.prototype);UUID.prototype.getUUID=function(){return this.getValue()};function ValidationException(a,b,c){c=c||null;BaseException.call(this,ValidationException.formatMessage(a,b),c)}UUID.prototype.setUUID=function(a){this.setValue(a)};
UUID.prototype.validate=function(a,b){var c=new ValidationResult,d=TextUtils.stringify(this.getRawValue());TextUtils.isEmpty(d)?c.fail(a,b,[Property.ALLOW_EMPTY]):(c.failIf((new StringValidator(a)).validate(BaseValidator.sliceArguments(b,StringValidator.ALLOWED_PROPERTIES),a,d),b),/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(d)||c.fail(a,b,[Property.FORMAT]));return c};
UUID.prototype.setRawValue=function(a){CustomConvertible.prototype.setRawValue.call(this,a);this.setValue(TextUtils.stringify(a))};UUID.prototype.getAllowedProperties=function(){return StringValidator.ALLOWED_PROPERTIES};URL.prototype=NDObject.create(CustomConvertible.prototype);URL.prototype.validate=function(){return new ValidationResult};URL.prototype.setUrl=function(a){this.setRawValue(a)};URL.prototype.setRawValue=function(a){CustomConvertible.prototype.setRawValue.call(this,a);this.setValue(a)};
URL.prototype.getAllowedProperties=function(){return StringValidator.ALLOWED_PROPERTIES};function BaseContainerValidator(a,b,c,d){BaseValidator.call(this,a,d);if(!c)throw Error("Manager cannot be null");this.mObjectElementValidator=new ObjectValidator(a,c);this.mManager=c;this.mMetadata=b}MessageVersionType.prototype=NDObject.create(Enumerable.prototype);MessageVersionType.VALUES={};
MessageVersionType.fromString=function(a){if("undefined"===typeof MessageVersionType.VALUES[a])throw new IllegalArgumentException("value is not a valid MessageVersionType value.");return MessageVersionType.VALUES[a]};MessageVersionType.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof MessageVersionType.VALUES[a]?new MessageVersionType(TextUtils.stringify(a),!1):MessageVersionType.VALUES[a]};MessageVersionType.V2_0_0=new MessageVersionType("2.0.0",!0);
MessageVersionType.V2_1_0=new MessageVersionType("2.1.0",!0);MessageVersionType.V2_2_0=new MessageVersionType("2.2.0",!0);MsgType.prototype=NDObject.create(Enumerable.prototype);MsgType.VALUES={};MsgType.fromString=function(a){if("undefined"===typeof MsgType.VALUES[a])throw new IllegalArgumentException("value is not a valid MsgType value.");return MsgType.VALUES[a]};
MsgType.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof MsgType.VALUES[a]?new MsgType(TextUtils.stringify(a),!1):MsgType.VALUES[a]};MsgType.A_REQ=new MsgType("AReq",!0);MsgType.A_RES=new MsgType("ARes",!0);MsgType.C_REQ=new MsgType("CReq",!0);MsgType.C_RES=new MsgType("CRes",!0);MsgType.P_REQ=new MsgType("PReq",!0);MsgType.P_RES=new MsgType("PRes",!0);MsgType.R_REQ=new MsgType("RReq",!0);MsgType.R_RES=new MsgType("RRes",!0);MsgType.ERRO=new MsgType("Erro",!0);
TransactionStatus.prototype=NDObject.create(Enumerable.prototype);TransactionStatus.VALUES={};TransactionStatus.fromString=function(a){if("undefined"===typeof TransactionStatus.VALUES[a])throw new IllegalArgumentException("value is not a valid TransactionStatus value.");return TransactionStatus.VALUES[a]};TransactionStatus.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof TransactionStatus.VALUES[a]?new TransactionStatus(TextUtils.stringify(a),!1):TransactionStatus.VALUES[a]};
TransactionStatus.SUCCESS=new TransactionStatus("Y",!0);TransactionStatus.NOT_AUTHENTICATED=new TransactionStatus("N",!0);TransactionStatus.AUTHENTICATION_NOT_PERFORMED=new TransactionStatus("U",!0);function FieldMetadata(){this.mValidatorMetadata=[];this.mFieldType=this.mValueInitializer=this.mValueSetter=this.mValueRetriever=null;this.mSerializationEnabled=this.mDcoSyncEnabled=!1;this.mSerializationTarget=null}TransactionStatus.ATTEMPTS_PROCESSING_PERFORMED=new TransactionStatus("A",!0);
TransactionStatus.CHALLENGE_REQUIRED=new TransactionStatus("C",!0);TransactionStatus.DECOUPLED_AUTHENTICATION=new TransactionStatus("D",!0);TransactionStatus.REJECTED=new TransactionStatus("R",!0);TransactionStatus.INFORMATIONAL_ONLY=new TransactionStatus("I",!0);TransactionStatus.CHALLENGE_SUPPRESSED=new TransactionStatus("ND-S",!0);ValidationException.prototype=NDObject.create(BaseException.prototype);
ValidationException.formatMessage=function(a,b){null===ObjectUtils.normalize(b)&&(b="Unknown error");return null===ObjectUtils.normalize(a)?"Validation failed: "+b:"Validation failed on "+a+": "+b};HTTPLimitedTimingData.prototype=NDObject.create(Convertible.prototype);HTTPLimitedTimingData.prototype.getFieldMetadata=function(){return HTTPLimitedTimingData.FieldMetadata};HTTPLimitedTimingData.prototype.getConnectTimeMS=function(){return this.connectTimeMS};
HTTPLimitedTimingData.prototype.setConnectTimeMS=function(a){this.connectTimeMS=a};HTTPLimitedTimingData.prototype.getReadTimeMS=function(){return this.readTimeMS};HTTPLimitedTimingData.prototype.setReadTimeMS=function(a){this.readTimeMS=a};HTTPLimitedTimingData.FieldMetadata={};for(var key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(HTTPLimitedTimingData.FieldMetadata[key]=Convertible.FieldMetadata[key]);
HTTPLimitedTimingData.FieldMetadata.connectTimeMS=(new FieldMetadata).setValueRetriever(function(a){return a instanceof HTTPLimitedTimingData?a.getConnectTimeMS():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof HTTPLimitedTimingData&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,void 0,"Int")&&(a.setConnectTimeMS(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
void 0,"Int")?a:null}).setFieldType(Init.Type.Int).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
HTTPLimitedTimingData.FieldMetadata.readTimeMS=(new FieldMetadata).setValueRetriever(function(a){return a instanceof HTTPLimitedTimingData?a.getReadTimeMS():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof HTTPLimitedTimingData&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,void 0,"Int")&&(a.setReadTimeMS(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,void 0,
"Int")?a:null}).setFieldType(Init.Type.Int).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());ACSUITemplate.prototype=NDObject.create(Enumerable.prototype);ACSUITemplate.VALUES={};function Failure(a,b,c){this.mFieldName=a;this.mFieldMetadata=null;this.mAllArguments=null===ObjectUtils.normalize(b)?{}:b;this.mFailedProperties=null===ObjectUtils.normalize(c)?[]:c}
ACSUITemplate.fromString=function(a){if("undefined"===typeof ACSUITemplate.VALUES[a])throw new IllegalArgumentException("value is not a valid ACSUITemplate value.");return ACSUITemplate.VALUES[a]};ACSUITemplate.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof ACSUITemplate.VALUES[a]?new ACSUITemplate(TextUtils.stringify(a),!1):ACSUITemplate.VALUES[a]};ACSUITemplate.TEXT=new ACSUITemplate("01",!0);ACSUITemplate.SINGLE_SELECT=new ACSUITemplate("02",!0);
ACSUITemplate.MULTI_SELECT=new ACSUITemplate("03",!0);ACSUITemplate.OOB=new ACSUITemplate("04",!0);ACSUITemplate.HTML_OTHER=new ACSUITemplate("05",!0);function DoubleValidator(a){BaseValidator.call(this,a)}ACSUITemplate.RESERVED80=new ACSUITemplate("80",!0);ACSUITemplate.RESERVED81=new ACSUITemplate("81",!0);ACSUITemplate.RESERVED82=new ACSUITemplate("82",!0);ACSUITemplate.RESERVED83=new ACSUITemplate("83",!0);ACSUITemplate.RESERVED84=new ACSUITemplate("84",!0);
ACSUITemplate.RESERVED85=new ACSUITemplate("85",!0);ACSUITemplate.RESERVED86=new ACSUITemplate("86",!0);ACSUITemplate.RESERVED87=new ACSUITemplate("87",!0);ACSUITemplate.RESERVED88=new ACSUITemplate("88",!0);ACSUITemplate.RESERVED89=new ACSUITemplate("89",!0);ACSUITemplate.RESERVED90=new ACSUITemplate("90",!0);ACSUITemplate.RESERVED91=new ACSUITemplate("91",!0);function StringValidator(a){BaseValidator.call(this,a)}ACSUITemplate.RESERVED92=new ACSUITemplate("92",!0);
function BrowserColorDepthType(a,b){Enumerable.call(this,a,BrowserColorDepthType.VALUES,b);b&&(BrowserColorDepthType.VALUES[a]=this)}ACSUITemplate.RESERVED93=new ACSUITemplate("93",!0);ACSUITemplate.RESERVED94=new ACSUITemplate("94",!0);ACSUITemplate.RESERVED95=new ACSUITemplate("95",!0);ACSUITemplate.RESERVED96=new ACSUITemplate("96",!0);ACSUITemplate.RESERVED97=new ACSUITemplate("97",!0);ACSUITemplate.RESERVED98=new ACSUITemplate("98",!0);ACSUITemplate.RESERVED99=new ACSUITemplate("99",!0);
MessageExtensionAttributes.prototype=NDObject.create(Convertible.prototype);MessageExtensionAttributes.prototype.getFieldMetadata=function(){return MessageExtensionAttributes.FieldMetadata};MessageExtensionAttributes.prototype.getCriticalityIndicator=function(){return this.criticalityIndicator};MessageExtensionAttributes.prototype.setCriticalityIndicator=function(a){this.criticalityIndicator=a};function IP(a){CustomConvertible.call(this);this.setValue(a||null)}
MessageExtensionAttributes.prototype.getData=function(){return this.data};MessageExtensionAttributes.prototype.setData=function(a){this.data=a};MessageExtensionAttributes.prototype.getId=function(){return this.id};MessageExtensionAttributes.prototype.setId=function(a){this.id=a};MessageExtensionAttributes.prototype.getName=function(){return this.name};MessageExtensionAttributes.prototype.setName=function(a){this.name=a};
MessageExtensionAttributes.fromJson=function(a){var b=new MessageExtensionAttributes;JsonSerializer.readJsonIntoInstance(b,a);return b};MessageExtensionAttributes.fromMap=function(a){var b=new MessageExtensionAttributes;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};MessageExtensionAttributes.FieldMetadata={};for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(MessageExtensionAttributes.FieldMetadata[key]=Convertible.FieldMetadata[key]);
MessageExtensionAttributes.FieldMetadata.criticalityIndicator=(new FieldMetadata).setValueRetriever(function(a){return a instanceof MessageExtensionAttributes?a.getCriticalityIndicator():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof MessageExtensionAttributes&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,void 0,"Bool")&&(a.setCriticalityIndicator(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
void 0,"Bool")?a:null}).enableSerialization().setFieldType(Init.Type.Bool).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
MessageExtensionAttributes.FieldMetadata.data=(new FieldMetadata).setValueRetriever(function(a){return a instanceof MessageExtensionAttributes?a.getData():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof MessageExtensionAttributes&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,Object,"Object")&&(a.setData(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,Object,
"Object")?a:null}).enableSerialization().setFieldType(Init.Type.Object).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
function ThreeDSWidget(a){a=/^(((([^:\/#\?]+:)?(?:(\/\/)((?:(([^:@\/#\?]+)(?::([^:@\/#\?]+))?)@)?(([^:\/#\?\]\[]+|\[[^\/\]@#?]+\])(?::([0-9]+))?))?)?)?((\/?(?:[^\/\?#]+\/+)*)([^\?#]*)))?(\?[^#]+)?)(#.*)?/.exec(a);var b=null;null!==a&&(b={href:a[0],withoutHash:a[1],url:a[2],origin:a[3],protocol:a[4],protocolseparator:a[5],credhost:a[6],cred:a[7],user:a[8],pass:a[9],host:a[10],hostname:a[11],port:a[12],pathname:a[13],segment1:a[14],segment2:a[15],search:a[16],hash:a[17]});a=b;this.mHost=a.origin;this.mAPI=
a.pathname;this.authenticationResponse=this.threeDSServerTransID=null}
MessageExtensionAttributes.FieldMetadata.id=(new FieldMetadata).setValueRetriever(function(a){return a instanceof MessageExtensionAttributes?a.getId():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof MessageExtensionAttributes&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setId(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?
a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,!1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,64).addProperty(Property.MIN_LENGTH,1).build());
MessageExtensionAttributes.FieldMetadata.name=(new FieldMetadata).setValueRetriever(function(a){return a instanceof MessageExtensionAttributes?a.getName():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof MessageExtensionAttributes&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setName(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,
"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,64).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
MathUtil.validateIntegerOrNull=function(a,b){return MathUtil._validateNumberOrNull(a,b,Type.Integer)};MathUtil.validateDoubleOrNull=function(a,b){return MathUtil._validateNumberOrNull(a,b,Type.Double)};
MathUtil.validateEqualOrNull=function(a,b,c,d){var e=new ValidationResult;b=MathUtil.validateDoubleOrNull(a,b);c=MathUtil.validateDoubleOrNull(a,c);e.failIf(b);e.failIf(c);if(b.isNonNullSuccess()&&c.isNonNullSuccess()){var f="undefined"!==typeof Number&&"undefined"!==typeof Number.EPSILON?Number.EPSILON:Math.pow(2,-52);Math.abs(b.getFilteredValue()-c.getFilteredValue())>=f&&e.fail(a,null,[d])}return e};
MathUtil.validateGreaterThanEqualOrNull=function(a,b,c,d){var e=new ValidationResult;b=MathUtil.validateDoubleOrNull(a,b);c=MathUtil.validateDoubleOrNull(a,c);e.failIf(b);e.failIf(c);b.isNonNullSuccess()&&c.isNonNullSuccess()&&b.getFilteredValue()<c.getFilteredValue()&&e.fail(a,null,[d]);return e};
MathUtil.validateLessThanEqualOrNull=function(a,b,c,d){var e=new ValidationResult;b=MathUtil.validateDoubleOrNull(a,b);c=MathUtil.validateDoubleOrNull(a,c);e.failIf(b);e.failIf(c);b.isNonNullSuccess()&&c.isNonNullSuccess()&&b.getFilteredValue()>c.getFilteredValue()&&e.fail(a,null,[d]);return e};function MessageVersionType(a,b){Enumerable.call(this,a,MessageVersionType.VALUES,b);b&&(MessageVersionType.VALUES[a]=this)}
MathUtil._validateNumberOrNull=function(a,b,c){var d=new ValidationResult.Filter;if(c===Type.Integer&&b&&-1!==b.toString().indexOf("."))d.fail(a,null,[Property.FORMAT]);else if("number"===typeof b)d.setFilteredValue(b);else if(null===TextUtils.stringify(b))d.setFilteredValue(null);else try{var e=c===Type.Integer?parseInt(b.toString(),10):parseFloat(b.toString());isNaN(e)?d.fail(a,null,[Property.FORMAT]):d.setFilteredValue(e)}catch(f){d.fail(a,null,[Property.FORMAT])}return d};
APIResponse.prototype=NDObject.create(HTTPLimitedTimingData.prototype);APIResponse.prototype.getFieldMetadata=function(){return APIResponse.FieldMetadata};APIResponse.prototype.getRequestJson=function(){return this.requestJson};APIResponse.prototype.setRequestJson=function(a){this.requestJson=a};APIResponse.prototype.getResponseJson=function(){return this.responseJson};APIResponse.prototype.setResponseJson=function(a){this.responseJson=a};APIResponse.FieldMetadata={};
for(key in HTTPLimitedTimingData.FieldMetadata)HTTPLimitedTimingData.FieldMetadata.hasOwnProperty(key)&&(APIResponse.FieldMetadata[key]=HTTPLimitedTimingData.FieldMetadata[key]);
APIResponse.FieldMetadata.requestJson=(new FieldMetadata).setValueRetriever(function(a){return a instanceof APIResponse?a.getRequestJson():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof APIResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setRequestJson(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?a:null}).setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,
1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
APIResponse.FieldMetadata.responseJson=(new FieldMetadata).setValueRetriever(function(a){return a instanceof APIResponse?a.getResponseJson():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof APIResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setResponseJson(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?a:null}).setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,
1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());Base64URL.prototype=NDObject.create(CustomConvertible.prototype);Base64URL.prototype.getBase64Url=function(){return this.getValue()};Base64URL.prototype.setBase64Url=function(a){this.setRawValue(a)};Base64URL.prototype.validate=function(){return new ValidationResult};
Base64URL.prototype.setRawValue=function(a){CustomConvertible.prototype.setRawValue.call(this,a);this.setValue(TextUtils.stringify(a))};Base64URL.prototype.getAllowedProperties=function(){return StringValidator.ALLOWED_PROPERTIES};BooleanYesNo.prototype=NDObject.create(Enumerable.prototype);BooleanYesNo.VALUES={};BooleanYesNo.fromString=function(a){if("undefined"===typeof BooleanYesNo.VALUES[a])throw new IllegalArgumentException("value is not a valid BooleanYesNo value.");return BooleanYesNo.VALUES[a]};
BooleanYesNo.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof BooleanYesNo.VALUES[a]?new BooleanYesNo(TextUtils.stringify(a),!1):BooleanYesNo.VALUES[a]};BooleanYesNo.YES=new BooleanYesNo("Y",!0);BooleanYesNo.NO=new BooleanYesNo("N",!0);NumericEnumerable.prototype=NDObject.create(Enumerable.prototype);NumericEnumerable.prototype.getValue=function(){return parseInt(this.toString(),10)};
NumericEnumerable.validateNumeric=function(a,b){return a&&null!==ObjectUtils.normalize(b)&&MathUtil.validateIntegerOrNull(null,b).isSuccess()};Challenge.prototype=NDObject.create(Convertible.prototype);Challenge.prototype.getFieldMetadata=function(){return Challenge.FieldMetadata};Challenge.prototype.getThreeDSServerTransID=function(){return this.threeDSServerTransID};Challenge.prototype.setThreeDSServerTransID=function(a){this.threeDSServerTransID=a};Challenge.prototype.getAcsTransID=function(){return this.acsTransID};
Challenge.prototype.setAcsTransID=function(a){this.acsTransID=a};Challenge.prototype.getMessageType=function(){return this.messageType};function ValidationDelegate(){}Challenge.prototype.setMessageType=function(a){this.messageType=a};Challenge.prototype.getMessageVersion=function(){return this.messageVersion};Challenge.prototype.setMessageVersion=function(a){this.messageVersion=a};Challenge.fromJson=function(a){var b=new Challenge;JsonSerializer.readJsonIntoInstance(b,a);return b};
Challenge.fromMap=function(a){var b=new Challenge;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};Challenge.FieldMetadata={};for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(Challenge.FieldMetadata[key]=Convertible.FieldMetadata[key]);
Challenge.FieldMetadata.threeDSServerTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof Challenge?a.getThreeDSServerTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof Challenge&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setThreeDSServerTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,
!1).build());
Challenge.FieldMetadata.acsTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof Challenge?a.getAcsTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof Challenge&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setAcsTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,!1).build());
Challenge.FieldMetadata.messageType=(new FieldMetadata).setValueRetriever(function(a){return a instanceof Challenge?a.getMessageType():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof Challenge&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,MsgType,"MsgType")&&(a.setMessageType(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MsgType.fromObject(a)}).enableSerialization().setFieldType(Init.Type.MsgType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,
!1).build());
Challenge.FieldMetadata.messageVersion=(new FieldMetadata).setValueRetriever(function(a){return a instanceof Challenge?a.getMessageVersion():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof Challenge&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,MessageVersionType,"MessageVersionType")&&(a.setMessageVersion(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MessageVersionType.fromObject(a)}).enableSerialization().setFieldType(Init.Type.MessageVersionType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,!1).build());
ImageSizes.prototype=NDObject.create(Convertible.prototype);function InvalidValueException(a){BaseException.call(this,a)}ImageSizes.prototype.getFieldMetadata=function(){return ImageSizes.FieldMetadata};ImageSizes.prototype.getMedium=function(){return this.medium};ImageSizes.prototype.setMedium=function(a){this.medium=a};ImageSizes.prototype.getHigh=function(){return this.high};ImageSizes.prototype.setHigh=function(a){this.high=a};ImageSizes.prototype.getExtraHigh=function(){return this.extraHigh};
ImageSizes.prototype.setExtraHigh=function(a){this.extraHigh=a};ImageSizes.fromJson=function(a){var b=new ImageSizes;JsonSerializer.readJsonIntoInstance(b,a);return b};ImageSizes.fromMap=function(a){var b=new ImageSizes;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};ImageSizes.FieldMetadata={};for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(ImageSizes.FieldMetadata[key]=Convertible.FieldMetadata[key]);
ImageSizes.FieldMetadata.medium=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ImageSizes?a.getMedium():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ImageSizes&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,URL,"URL")&&(a.setMedium(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new URL;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.URL).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ImageSizes.FieldMetadata.high=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ImageSizes?a.getHigh():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ImageSizes&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,URL,"URL")&&(a.setHigh(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new URL;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.URL).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
function EMVProtocolErrorEvent(){Convertible.call(this);this.sDKTransactionID=this.errorMessage=null;Interface.implement(this,ISyncable)}
ImageSizes.FieldMetadata.extraHigh=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ImageSizes?a.getExtraHigh():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ImageSizes&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,URL,"URL")&&(a.setExtraHigh(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new URL;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.URL).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
TransactionReason.prototype=NDObject.create(Enumerable.prototype);TransactionReason.VALUES={};TransactionReason.fromString=function(a){if("undefined"===typeof TransactionReason.VALUES[a])throw new IllegalArgumentException("value is not a valid TransactionReason value.");return TransactionReason.VALUES[a]};TransactionReason.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof TransactionReason.VALUES[a]?new TransactionReason(TextUtils.stringify(a),!1):TransactionReason.VALUES[a]};
function Enumerable(a,b,c){if("undefined"!==typeof b[a])throw new InvalidValueException(a+" is already defined");this.mValue=a;this.mIsValid=c;this.mHash=0;Interface.implement(this,IValidatable)}TransactionReason.CARD_AUTHENTICATION_FAILED=new TransactionReason("01",!0);TransactionReason.UNKNOWN_DEVICE=new TransactionReason("02",!0);TransactionReason.UNSUPPORTED_DEVICE=new TransactionReason("03",!0);TransactionReason.EXCEEDS_AUTHENTICATION_FREQUENCY_LIMIT=new TransactionReason("04",!0);
TransactionReason.EXPIRED_CARD=new TransactionReason("05",!0);TransactionReason.INVALID_CARD_NUMBER=new TransactionReason("06",!0);TransactionReason.INVALID_TRANSACTION=new TransactionReason("07",!0);function SetterResult(){}TransactionReason.NO_CARD_RECORD=new TransactionReason("08",!0);TransactionReason.SECURITY_FAILURE=new TransactionReason("09",!0);TransactionReason.STOLEN_CARD=new TransactionReason("10",!0);TransactionReason.SUSPECTED_FRAUD=new TransactionReason("11",!0);
TransactionReason.TRANSACTION_NOT_PERMITTED_TO_CARDHOLDER=new TransactionReason("12",!0);TransactionReason.CARDHOLDER_NOT_ENROLLED_IN_SERVICE=new TransactionReason("13",!0);TransactionReason.TRANSACTION_TIMEOUT_AT_ACS=new TransactionReason("14",!0);TransactionReason.LOW_CONFIDENCE=new TransactionReason("15",!0);TransactionReason.MEDIUM_CONFIDENCE=new TransactionReason("16",!0);TransactionReason.HIGH_CONFIDENCE=new TransactionReason("17",!0);
function EMVErrorMessage(){Convertible.call(this);this.transactionID=this.errorDetails=this.errorDescription=this.errorCode=null;Interface.implement(this,ISyncable)}TransactionReason.VERY_HIGH_CONFIDENCE=new TransactionReason("18",!0);TransactionReason.EXCEEDS_ACS_MAXIUM_CHALLENGES=new TransactionReason("19",!0);function INonStringable(){}
function ChallengeOutcome(){Challenge.call(this);this.whyInfoText=this.whyInfoLabel=this.whitelistingInfoText=this.transStatus=this.submitAuthenticationLabel=this.sdkTransID=this.resendInformationLabel=this.psImage=this.oobContinueLabel=this.oobAppLabel=this.oobAppURL=this.messageExtension=this.issuerImage=this.expandInfoText=this.expandInfoLabel=this.challengeSelectInfo=this.challengeInfoTextIndicator=this.challengeInfoText=this.challengeInfoLabel=this.challengeInfoHeader=this.challengeCompletionInd=
this.challengeAddInfo=this.acsUiType=this.acsHTML=this.acsCounterAtoS=null;Interface.implement(this,ISyncable)}TransactionReason.NON_PAYMENT_TRANSACTION_NOT_SUPPORTED=new TransactionReason("20",!0);TransactionReason.THREE_RI_TRANSACTION_NOT_SUPPORTED=new TransactionReason("21",!0);TransactionReason.ACS_TECHNICAL_ISSUE=new TransactionReason("22",!0);TransactionReason.DECOUPLED_AUTHENTICATION_NOT_BY3_DS_REQUESTOR=new TransactionReason("23",!0);
TransactionReason.DECOUPLED_MAX_EXPIRY_TIME_EXCEEDED=new TransactionReason("24",!0);TransactionReason.DECOUPLED_AUTHENTICATION_INSUFFICIENT_TIME=new TransactionReason("25",!0);TransactionReason.AUTHENTICATION_NOT_PERFORMED=new TransactionReason("26",!0);TransactionReason.SUPPRESSED_BY_POLICY=new TransactionReason("ND-S-POL",!0);TransactionReason.SUPPRESSED_MANUALLY=new TransactionReason("ND-S-MAN",!0);TransactionReason.RESERVED80=new TransactionReason("80",!0);
TransactionReason.RESERVED81=new TransactionReason("81",!0);TransactionReason.RESERVED82=new TransactionReason("82",!0);TransactionReason.RESERVED83=new TransactionReason("83",!0);TransactionReason.RESERVED84=new TransactionReason("84",!0);TransactionReason.RESERVED85=new TransactionReason("85",!0);TransactionReason.RESERVED86=new TransactionReason("86",!0);TransactionReason.RESERVED87=new TransactionReason("87",!0);TransactionReason.RESERVED88=new TransactionReason("88",!0);
TransactionReason.RESERVED89=new TransactionReason("89",!0);TransactionReason.RESERVED90=new TransactionReason("90",!0);TransactionReason.RESERVED91=new TransactionReason("91",!0);TransactionReason.RESERVED92=new TransactionReason("92",!0);TransactionReason.RESERVED93=new TransactionReason("93",!0);TransactionReason.RESERVED94=new TransactionReason("94",!0);TransactionReason.RESERVED95=new TransactionReason("95",!0);TransactionReason.RESERVED96=new TransactionReason("96",!0);
function WidgetBrowserData(){Convertible.call(this);this.challengeWindowSize=this.browserUserAgent=this.browserTZ=this.browserScreenWidth=this.browserScreenHeight=this.browserColorDepth=this.browserLanguage=this.browserJavascriptEnabled=this.browserJavaEnabled=null;Interface.implement(this,ISyncable)}TransactionReason.RESERVED97=new TransactionReason("97",!0);TransactionReason.RESERVED98=new TransactionReason("98",!0);TransactionReason.RESERVED99=new TransactionReason("99",!0);Base64.prototype=NDObject.create(CustomConvertible.prototype);
Base64.prototype.getBase64=function(){return this.getValue()};Base64.prototype.setBase64=function(a){this.setRawValue(a)};Base64.prototype.validate=function(){return new ValidationResult};Base64.prototype.setRawValue=function(a){CustomConvertible.prototype.setRawValue.call(this,a);this.setValue(TextUtils.stringify(a))};Base64.prototype.getAllowedProperties=function(){return StringValidator.ALLOWED_PROPERTIES};
Validator.prototype.validate=function(a){a=a||null;var b=new ValidationResult;this._checkForMalformedFields(b,this.mMetadataProvider);var c=this.mMetadataProvider.getFieldMetadata(),d;for(d in c){var e=c[d],f=e.getValue(this.mMetadataProvider);this._applyValidators(b,MetadataUtils.getFieldPath(e,a,d),f,e,FieldMetadata.ValidatorContext.THIS)}return b};function MessageCategoryType(a,b){Enumerable.call(this,a,MessageCategoryType.VALUES,b);b&&(MessageCategoryType.VALUES[a]=this)}
Validator.prototype.validateChild=function(a,b,c){var d=new ValidationResult;null!==b&&this._applyValidators(d,a,c,b,FieldMetadata.ValidatorContext.T);return d};
Validator.prototype._applyValidators=function(a,b,c,d,e){var f={},g=d.getValidatorMetadata(),k;for(k in g){var h=g[k],m=h.getDelegate(),l=h.getProperties();-1!==h.getContexts().indexOf(FieldMetadata.ValidatorContext.T.toString())&&(f=ObjectUtils.mergeObjects([f,l]));-1!==h.getContexts().indexOf(e.toString())&&(h=this._buildValidatorInstance(m,e,b,d),a.failWithMetadataIf(h.validate(l,b,c),d))}e===FieldMetadata.ValidatorContext.T&&a.failIf(this._selfValidateField(f,b,c))};
Validator.prototype._buildValidatorInstance=function(a,b,c,d){switch(a){case FieldMetadata.ValidationDelegate.OBJECT:c=b===FieldMetadata.ValidatorContext.THIS?new ObjectContainerValidator(c,d,this):new ObjectValidator(c);break;case FieldMetadata.ValidationDelegate.ARRAY:c=b===FieldMetadata.ValidatorContext.THIS?new ArrayContainerValidator(c,d,this):null;break;case FieldMetadata.ValidationDelegate.MAP:c=b===FieldMetadata.ValidatorContext.THIS?new MapContainerValidator(c,d,this):null;break;case FieldMetadata.ValidationDelegate.DOUBLE:c=
new DoubleValidator(c);break;case FieldMetadata.ValidationDelegate.INT:c=new IntValidator(c);break;case FieldMetadata.ValidationDelegate.STRING:c=new StringValidator(c);break;default:c=null}if(null==c)throw Error("Invalid delegate for "+b+" context: "+a);return c};Validator.prototype._selfValidateField=function(a,b,c){var d=new ValidationResult;Interface.isInstanceOf(c,IValidatable)&&d.failIf(c.validate(b,a));return d};
Validator.prototype._checkForMalformedFields=function(a,b){var c=b.getMalformedFields(null),d;for(d in c)a.failWithMetadata(d,c[d],[Property.FORMAT])};ErrorComponent.prototype=NDObject.create(Enumerable.prototype);ErrorComponent.VALUES={};ErrorComponent.fromString=function(a){if("undefined"===typeof ErrorComponent.VALUES[a])throw new IllegalArgumentException("value is not a valid ErrorComponent value.");return ErrorComponent.VALUES[a]};
ErrorComponent.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof ErrorComponent.VALUES[a]?new ErrorComponent(TextUtils.stringify(a),!1):ErrorComponent.VALUES[a]};ErrorComponent.THREEDS_SDK=new ErrorComponent("C",!0);ErrorComponent.THREEDS_SERVER=new ErrorComponent("S",!0);ErrorComponent.DS=new ErrorComponent("D",!0);ErrorComponent.ACS=new ErrorComponent("A",!0);CancelIndicator.prototype=NDObject.create(Enumerable.prototype);CancelIndicator.VALUES={};
CancelIndicator.fromString=function(a){if("undefined"===typeof CancelIndicator.VALUES[a])throw new IllegalArgumentException("value is not a valid CancelIndicator value.");return CancelIndicator.VALUES[a]};CancelIndicator.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof CancelIndicator.VALUES[a]?new CancelIndicator(TextUtils.stringify(a),!1):CancelIndicator.VALUES[a]};CancelIndicator.CARDHOLDER_CANCELLED=new CancelIndicator("01",!0);
CancelIndicator.DECOUPLED_AUTHENTICATION_TRANSACTION_TIMEOUT=new CancelIndicator("03",!0);CancelIndicator.OTHER_ACS_TIMEOUT=new CancelIndicator("04",!0);CancelIndicator.FIRST_C_REQ_ACS_TIMEOUT=new CancelIndicator("05",!0);CancelIndicator.TRANSACTION_ERROR=new CancelIndicator("06",!0);CancelIndicator.UNKNOWN_CANCELLED=new CancelIndicator("07",!0);function Base64URL(a){CustomConvertible.call(this);this.setRawValue(a||null)}CancelIndicator.SDK_TRANSACTION_TIMEOUT=new CancelIndicator("08",!0);
CancelIndicator.RESERVED80=new CancelIndicator("80",!0);CancelIndicator.RESERVED81=new CancelIndicator("81",!0);function IValidatable(){}CancelIndicator.RESERVED82=new CancelIndicator("82",!0);CancelIndicator.RESERVED83=new CancelIndicator("83",!0);
function AuthenticationResponse(){APIResponse.call(this);this.whiteListStatusSource=this.whiteListStatus=this.encodedCReq=this.transStatusReason=this.transStatus=this.messageVersion=this.messageType=this.messageExtension=this.eci=this.dsTransID=this.dsReferenceNumber=this.cardholderInfo=this.broadInfo=this.authenticationValue=this.authenticationType=this.acsURL=this.acsTransID=this.acsSignedContent=this.acsRenderingType=this.acsReferenceNumber=this.acsOperatorID=this.acsDecConInd=this.acsChallengeMandated=
this.threeDSServerTransID=this.sdkTransID=null;Interface.implement(this,ISyncable)}CancelIndicator.RESERVED84=new CancelIndicator("84",!0);CancelIndicator.RESERVED85=new CancelIndicator("85",!0);CancelIndicator.RESERVED86=new CancelIndicator("86",!0);CancelIndicator.RESERVED87=new CancelIndicator("87",!0);CancelIndicator.RESERVED88=new CancelIndicator("88",!0);CancelIndicator.RESERVED89=new CancelIndicator("89",!0);CancelIndicator.RESERVED90=new CancelIndicator("90",!0);
CancelIndicator.RESERVED91=new CancelIndicator("91",!0);CancelIndicator.RESERVED92=new CancelIndicator("92",!0);CancelIndicator.RESERVED93=new CancelIndicator("93",!0);CancelIndicator.RESERVED94=new CancelIndicator("94",!0);CancelIndicator.RESERVED95=new CancelIndicator("95",!0);CancelIndicator.RESERVED96=new CancelIndicator("96",!0);CancelIndicator.RESERVED97=new CancelIndicator("97",!0);CancelIndicator.RESERVED98=new CancelIndicator("98",!0);CancelIndicator.RESERVED99=new CancelIndicator("99",!0);
BrowserColorDepthType.prototype=NDObject.create(Enumerable.prototype);BrowserColorDepthType.VALUES={};BrowserColorDepthType.fromString=function(a){if("undefined"===typeof BrowserColorDepthType.VALUES[a])throw new IllegalArgumentException("value is not a valid BrowserColorDepthType value.");return BrowserColorDepthType.VALUES[a]};
BrowserColorDepthType.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof BrowserColorDepthType.VALUES[a]?new BrowserColorDepthType(TextUtils.stringify(a),!1):BrowserColorDepthType.VALUES[a]};BrowserColorDepthType.CD1_BIT=new BrowserColorDepthType("1",!0);BrowserColorDepthType.CD4_BITS=new BrowserColorDepthType("4",!0);BrowserColorDepthType.CD8_BITS=new BrowserColorDepthType("8",!0);function Property(a){this.value=a}
BrowserColorDepthType.CD15_BITS=new BrowserColorDepthType("15",!0);BrowserColorDepthType.CD16_BITS=new BrowserColorDepthType("16",!0);BrowserColorDepthType.CD24_BITS=new BrowserColorDepthType("24",!0);BrowserColorDepthType.CD32_BITS=new BrowserColorDepthType("32",!0);BrowserColorDepthType.CD48_BITS=new BrowserColorDepthType("48",!0);MessageCategoryType.prototype=NDObject.create(Enumerable.prototype);MessageCategoryType.VALUES={};
function ErrorCode(a,b){NumericEnumerable.call(this,a,ErrorCode.VALUES,b);b&&(ErrorCode.VALUES[a]=this)}MessageCategoryType.fromString=function(a){if("undefined"===typeof MessageCategoryType.VALUES[a])throw new IllegalArgumentException("value is not a valid MessageCategoryType value.");return MessageCategoryType.VALUES[a]};
MessageCategoryType.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof MessageCategoryType.VALUES[a]?new MessageCategoryType(TextUtils.stringify(a),!1):MessageCategoryType.VALUES[a]};function IllegalArgumentException(a){BaseException.call(this,a)}MessageCategoryType.PA=new MessageCategoryType("01",!0);function ObjectContainerValidator(a,b,c,d){BaseContainerValidator.call(this,a,b,c,d||!1)}MessageCategoryType.NPA=new MessageCategoryType("02",!0);
MessageCategoryType.MASTERCARD_MESSAGE=new MessageCategoryType("80",!0);MessageCategoryType.RESERVED81=new MessageCategoryType("81",!0);MessageCategoryType.RESERVED82=new MessageCategoryType("82",!0);MessageCategoryType.RESERVED83=new MessageCategoryType("83",!0);MessageCategoryType.RESERVED84=new MessageCategoryType("84",!0);MessageCategoryType.RESERVED85=new MessageCategoryType("85",!0);MessageCategoryType.RESERVED86=new MessageCategoryType("86",!0);
MessageCategoryType.RESERVED87=new MessageCategoryType("87",!0);MessageCategoryType.RESERVED88=new MessageCategoryType("88",!0);MessageCategoryType.RESERVED89=new MessageCategoryType("89",!0);MessageCategoryType.RESERVED90=new MessageCategoryType("90",!0);MessageCategoryType.RESERVED91=new MessageCategoryType("91",!0);MessageCategoryType.RESERVED92=new MessageCategoryType("92",!0);MessageCategoryType.RESERVED93=new MessageCategoryType("93",!0);
MessageCategoryType.RESERVED94=new MessageCategoryType("94",!0);MessageCategoryType.RESERVED95=new MessageCategoryType("95",!0);MessageCategoryType.RESERVED96=new MessageCategoryType("96",!0);MessageCategoryType.RESERVED97=new MessageCategoryType("97",!0);MessageCategoryType.RESERVED98=new MessageCategoryType("98",!0);MessageCategoryType.RESERVED99=new MessageCategoryType("99",!0);ChallengeOutcome.prototype=NDObject.create(Challenge.prototype);ChallengeOutcome.prototype.getFieldMetadata=function(){return ChallengeOutcome.FieldMetadata};
ChallengeOutcome.prototype.getAcsCounterAtoS=function(){return this.acsCounterAtoS};function ISyncable(){}ChallengeOutcome.prototype.setAcsCounterAtoS=function(a){this.acsCounterAtoS=a};ChallengeOutcome.prototype.getAcsHTML=function(){return this.acsHTML};ChallengeOutcome.prototype.setAcsHTML=function(a){this.acsHTML=a};function BooleanYesNo(a,b){Enumerable.call(this,a,BooleanYesNo.VALUES,b);b&&(BooleanYesNo.VALUES[a]=this)}ChallengeOutcome.prototype.getAcsUiType=function(){return this.acsUiType};
ChallengeOutcome.prototype.setAcsUiType=function(a){this.acsUiType=a};ChallengeOutcome.prototype.getChallengeAddInfo=function(){return this.challengeAddInfo};ChallengeOutcome.prototype.setChallengeAddInfo=function(a){this.challengeAddInfo=a};ChallengeOutcome.prototype.getChallengeCompletionInd=function(){return this.challengeCompletionInd};ChallengeOutcome.prototype.setChallengeCompletionInd=function(a){this.challengeCompletionInd=a};ChallengeOutcome.prototype.getChallengeInfoHeader=function(){return this.challengeInfoHeader};
ChallengeOutcome.prototype.setChallengeInfoHeader=function(a){this.challengeInfoHeader=a};function URL(a){CustomConvertible.call(this);this.setRawValue(a);this.mAuthInfo=null}ChallengeOutcome.prototype.getChallengeInfoLabel=function(){return this.challengeInfoLabel};ChallengeOutcome.prototype.setChallengeInfoLabel=function(a){this.challengeInfoLabel=a};ChallengeOutcome.prototype.getChallengeInfoText=function(){return this.challengeInfoText};
ChallengeOutcome.prototype.setChallengeInfoText=function(a){this.challengeInfoText=a};ChallengeOutcome.prototype.getChallengeInfoTextIndicator=function(){return this.challengeInfoTextIndicator};ChallengeOutcome.prototype.setChallengeInfoTextIndicator=function(a){this.challengeInfoTextIndicator=a};ChallengeOutcome.prototype.getChallengeSelectInfo=function(){null===ObjectUtils.normalize(this.challengeSelectInfo)&&(this.challengeSelectInfo=[]);return this.challengeSelectInfo};
ChallengeOutcome.prototype.setChallengeSelectInfo=function(a){this.challengeSelectInfo=a};function EMVCompletionEvent(){Convertible.call(this);this.transactionStatus=this.sdkTransactionID=null;Interface.implement(this,ISyncable)}ChallengeOutcome.prototype.addChallengeSelectInfo=function(a){this.getChallengeSelectInfo().push(a)};ChallengeOutcome.prototype.getExpandInfoLabel=function(){return this.expandInfoLabel};ChallengeOutcome.prototype.setExpandInfoLabel=function(a){this.expandInfoLabel=a};
ChallengeOutcome.prototype.getExpandInfoText=function(){return this.expandInfoText};ChallengeOutcome.prototype.setExpandInfoText=function(a){this.expandInfoText=a};ChallengeOutcome.prototype.getIssuerImage=function(){return this.issuerImage};ChallengeOutcome.prototype.setIssuerImage=function(a){this.issuerImage=a};ChallengeOutcome.prototype.getMessageExtension=function(){null===ObjectUtils.normalize(this.messageExtension)&&(this.messageExtension=[]);return this.messageExtension};
function BaseException(a,b){this.cause=b||null;this.message=a;GlobalError.hasOwnProperty("captureStackTrace")?GlobalError.captureStackTrace(this,this.constructor):this.stack=(new GlobalError).stack}ChallengeOutcome.prototype.setMessageExtension=function(a){this.messageExtension=a};ChallengeOutcome.prototype.addMessageExtension=function(a){this.getMessageExtension().push(a)};ChallengeOutcome.prototype.getOobAppURL=function(){return this.oobAppURL};
ChallengeOutcome.prototype.setOobAppURL=function(a){this.oobAppURL=a};ChallengeOutcome.prototype.getOobAppLabel=function(){return this.oobAppLabel};ChallengeOutcome.prototype.setOobAppLabel=function(a){this.oobAppLabel=a};ChallengeOutcome.prototype.getOobContinueLabel=function(){return this.oobContinueLabel};ChallengeOutcome.prototype.setOobContinueLabel=function(a){this.oobContinueLabel=a};ChallengeOutcome.prototype.getPsImage=function(){return this.psImage};
ChallengeOutcome.prototype.setPsImage=function(a){this.psImage=a};ChallengeOutcome.prototype.getResendInformationLabel=function(){return this.resendInformationLabel};ChallengeOutcome.prototype.setResendInformationLabel=function(a){this.resendInformationLabel=a};ChallengeOutcome.prototype.getSdkTransID=function(){return this.sdkTransID};ChallengeOutcome.prototype.setSdkTransID=function(a){this.sdkTransID=a};ChallengeOutcome.prototype.getSubmitAuthenticationLabel=function(){return this.submitAuthenticationLabel};
ChallengeOutcome.prototype.setSubmitAuthenticationLabel=function(a){this.submitAuthenticationLabel=a};ChallengeOutcome.prototype.getTransStatus=function(){return this.transStatus};ChallengeOutcome.prototype.setTransStatus=function(a){this.transStatus=a};ChallengeOutcome.prototype.getWhitelistingInfoText=function(){return this.whitelistingInfoText};function MathUtil(){}ChallengeOutcome.prototype.setWhitelistingInfoText=function(a){this.whitelistingInfoText=a};
ChallengeOutcome.prototype.getWhyInfoLabel=function(){return this.whyInfoLabel};ChallengeOutcome.prototype.setWhyInfoLabel=function(a){this.whyInfoLabel=a};ChallengeOutcome.prototype.getWhyInfoText=function(){return this.whyInfoText};ChallengeOutcome.prototype.setWhyInfoText=function(a){this.whyInfoText=a};ChallengeOutcome.fromJson=function(a){var b=new ChallengeOutcome;JsonSerializer.readJsonIntoInstance(b,a);return b};
ChallengeOutcome.fromMap=function(a){var b=new ChallengeOutcome;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};ChallengeOutcome.FieldMetadata={};for(key in Challenge.FieldMetadata)Challenge.FieldMetadata.hasOwnProperty(key)&&(ChallengeOutcome.FieldMetadata[key]=Challenge.FieldMetadata[key]);
ChallengeOutcome.FieldMetadata.acsCounterAtoS=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getAcsCounterAtoS():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setAcsCounterAtoS(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,
"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.LENGTH,3).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.acsHTML=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getAcsHTML():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,Base64URL,"Base64URL")&&(a.setAcsHTML(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new Base64URL;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.Base64URL).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,
1E5).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.acsUiType=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getAcsUiType():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ACSUITemplate,"ACSUITemplate")&&(a.setAcsUiType(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return ACSUITemplate.fromObject(a)}).enableSerialization().setFieldType(Init.Type.ACSUITemplate).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.challengeAddInfo=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getChallengeAddInfo():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setChallengeAddInfo(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,256).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.challengeCompletionInd=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getChallengeCompletionInd():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,BooleanYesNo,"BooleanYesNo")&&(a.setChallengeCompletionInd(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return BooleanYesNo.fromObject(a)}).enableSerialization().setFieldType(Init.Type.BooleanYesNo).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.challengeInfoHeader=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getChallengeInfoHeader():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setChallengeInfoHeader(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,45).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.challengeInfoLabel=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getChallengeInfoLabel():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setChallengeInfoLabel(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,45).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.challengeInfoText=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getChallengeInfoText():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setChallengeInfoText(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,350).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.challengeInfoTextIndicator=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getChallengeInfoTextIndicator():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,BooleanYesNo,"BooleanYesNo")&&(a.setChallengeInfoTextIndicator(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return BooleanYesNo.fromObject(a)}).enableSerialization().setFieldType(Init.Type.BooleanYesNo).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.challengeSelectInfo=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getChallengeSelectInfo():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,Object,"Object")&&(a.addChallengeSelectInfo(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
Object,"Object")?a:null}).enableSerialization().setFieldType(Init.Type.Object).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.ALLOW_EMPTY,!1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.ARRAY).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.expandInfoLabel=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getExpandInfoLabel():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setExpandInfoLabel(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,
"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,45).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.expandInfoText=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getExpandInfoText():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setExpandInfoText(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,
"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,256).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.issuerImage=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getIssuerImage():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ImageSizes,"ImageSizes")&&(a.setIssuerImage(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(){return new ImageSizes}).enableDcoSync().enableSerialization().setFieldType(Init.Type.ImageSizes).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.messageExtension=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getMessageExtension():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,MessageExtensionAttributes,"MessageExtensionAttributes")&&(a.addMessageExtension(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(){return new MessageExtensionAttributes}).enableDcoSync().enableSerialization().setFieldType(Init.Type.MessageExtensionAttributes).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.ARRAY).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.MAX_LENGTH,
10).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.ALLOW_EMPTY,!1).build());
ChallengeOutcome.FieldMetadata.oobAppURL=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getOobAppURL():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,URL,"URL")&&(a.setOobAppURL(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new URL;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.URL).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,
256).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.oobAppLabel=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getOobAppLabel():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setOobAppLabel(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?
a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,45).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.oobContinueLabel=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getOobContinueLabel():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setOobContinueLabel(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,45).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.psImage=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getPsImage():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ImageSizes,"ImageSizes")&&(a.setPsImage(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(){return new ImageSizes}).enableDcoSync().enableSerialization().setFieldType(Init.Type.ImageSizes).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.resendInformationLabel=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getResendInformationLabel():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setResendInformationLabel(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,45).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.sdkTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getSdkTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setSdkTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.submitAuthenticationLabel=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getSubmitAuthenticationLabel():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setSubmitAuthenticationLabel(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,45).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.transStatus=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getTransStatus():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,TransactionStatus,"TransactionStatus")&&(a.setTransStatus(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return TransactionStatus.fromObject(a)}).enableSerialization().setFieldType(Init.Type.TransactionStatus).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.whitelistingInfoText=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getWhitelistingInfoText():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setWhitelistingInfoText(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.MAX_LENGTH,64).build());function DateUtils(){}
ChallengeOutcome.FieldMetadata.whyInfoLabel=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getWhyInfoLabel():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setWhyInfoLabel(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?
a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,45).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ChallengeOutcome.FieldMetadata.whyInfoText=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ChallengeOutcome?a.getWhyInfoText():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ChallengeOutcome&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setWhyInfoText(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?
a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,256).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());WindowSize.prototype=NDObject.create(Enumerable.prototype);
WindowSize.VALUES={};WindowSize.fromString=function(a){if("undefined"===typeof WindowSize.VALUES[a])throw new IllegalArgumentException("value is not a valid WindowSize value.");return WindowSize.VALUES[a]};function Builder(){this.mValidationDelegate=ValidationDelegate.OBJECT;this.mContexts=[];this.mProperties={}}WindowSize.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof WindowSize.VALUES[a]?new WindowSize(TextUtils.stringify(a),!1):WindowSize.VALUES[a]};
WindowSize.WS250_400=new WindowSize("01",!0);WindowSize.WS390_400=new WindowSize("02",!0);WindowSize.WS500_600=new WindowSize("03",!0);WindowSize.WS600_400=new WindowSize("04",!0);WindowSize.FULL_SCREEN=new WindowSize("05",!0);ACSInterface.prototype=NDObject.create(Enumerable.prototype);ACSInterface.VALUES={};ACSInterface.fromString=function(a){if("undefined"===typeof ACSInterface.VALUES[a])throw new IllegalArgumentException("value is not a valid ACSInterface value.");return ACSInterface.VALUES[a]};
ACSInterface.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof ACSInterface.VALUES[a]?new ACSInterface(TextUtils.stringify(a),!1):ACSInterface.VALUES[a]};ACSInterface.NATIVE_UI=new ACSInterface("01",!0);ACSInterface.HTML_UI=new ACSInterface("02",!0);ACSInterface.BOTH=new ACSInterface("03",!0);ErrorCode.prototype=NDObject.create(NumericEnumerable.prototype);ErrorCode.VALUES={};
ErrorCode.fromString=function(a){if("undefined"===typeof ErrorCode.VALUES[a])throw new IllegalArgumentException("value is not a valid ErrorCode value.");return ErrorCode.VALUES[a]};ErrorCode.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof ErrorCode.VALUES[a]?new ErrorCode(TextUtils.stringify(a),!1):ErrorCode.VALUES[a]};ErrorCode.MESSAGE_RECEIVED_INVALID=new ErrorCode("101",!0);ErrorCode.MESSAGE_VERSION_NOT_SUPPORTED=new ErrorCode("102",!0);
ErrorCode.REQUIRED_DATA_ELEMENT_MISSING=new ErrorCode("201",!0);function ResultsResponse(){APIResponse.call(this);this.transStatusReason=this.transStatus=this.messageCategory=this.interactionCounter=this.eci=this.challengeCancel=this.authenticationValue=this.acsTransID=this.dsTransID=this.threeDSServerTransID=null;Interface.implement(this,ISyncable)}ErrorCode.CRITICAL_MESSAGE_EXTENSION_NOT_RECOGNISED=new ErrorCode("202",!0);ErrorCode.DATA_ELEMENT_FORMATTING_ERROR=new ErrorCode("203",!0);
ErrorCode.DUPLICATE_DATA_ELEMENT=new ErrorCode("204",!0);ErrorCode.TRANSACTION_ID_NOT_RECOGNIZED=new ErrorCode("301",!0);ErrorCode.DATA_DECRYPTION_FAILURE=new ErrorCode("302",!0);ErrorCode.ACCESS_DENIED_INVALID_ENDPOINT=new ErrorCode("303",!0);ErrorCode.ISO_CODE_INVALID=new ErrorCode("304",!0);ErrorCode.TRANSACTION_DATA_NOT_VALID=new ErrorCode("305",!0);ErrorCode.MERCHANT_CATEGORY_CODE_NOT_VALID_FOR_PAYMENT_SYSTEM=new ErrorCode("306",!0);ErrorCode.SERIAL_NUMBER_NOT_VALID=new ErrorCode("307",!0);
ErrorCode.UNSUPPORTED_DEVICE=new ErrorCode("401",!0);ErrorCode.TRANSACTION_TIMED_OUT=new ErrorCode("402",!0);ErrorCode.TRANSIENT_SYSTEM_FAILURE=new ErrorCode("403",!0);ErrorCode.PERMANENT_SYSTEM_FAILURE=new ErrorCode("404",!0);ErrorCode.SYSTEM_CONNECTION_FAILURE=new ErrorCode("405",!0);ErrorCode.THREE_DSSDK_INITIALIZATION_FAILURE=new ErrorCode("10000",!0);ObjectValidator.prototype=NDObject.create(BaseValidator.prototype);ObjectValidator.prototype.getAllowedProperties=function(){return[Property.ALLOW_EMPTY]};
ObjectValidator.prototype.validate=function(a,b,c){var d=new ValidationResult;d.failIf(this._validateArguments(b,a));d.failIf(this.metadataProviderValidate(b,c));c=TextUtils.stringifyToCharSequence(c);TextUtils.isEmpty(c)&&"undefined"!==typeof a[Property.ALLOW_EMPTY.toString()]&&!1===a[Property.ALLOW_EMPTY.toString()]&&d.fail(b,a,[Property.ALLOW_EMPTY]);return d};
ObjectValidator.prototype.metadataProviderValidate=function(a,b){var c=new ValidationResult;Interface.isInstanceOf(b,IMetadataProvider)&&c.failIf((new Validator(b)).validate(a));return c};BaseValidator.prototype.getAllowedProperties=function(){throw Error("getAllowedProperties is abstract and must be implemented.");};BaseValidator.prototype.validate=function(a,b,c){throw Error("validate is abstract and must be implemented.");};
BaseValidator.prototype._validateArguments=function(a,b){return this.mValidateArguments?BaseValidator.validateArguments(a,b,this.getAllowedProperties()):new ValidationResult};BaseValidator.prototype._getParentFieldName=function(){return this.mParentFieldName};function CustomConvertible(){this.mRawValue=this.mValue=null;Interface.implement(this,IValidatable)}
BaseValidator.validateArguments=function(a,b,c){var d=new ValidationResult;if(c&&b)for(var e in b){var f=!1,g;for(g in c)c[g].toString()===e&&(f=!0);f||d.fail(a,b,[Property.ARGUMENT_KEY])}return d};BaseValidator.sliceArguments=function(a,b){var c={};if(b&&a)for(var d=0;d<b.length;d++){var e=b[d];"undefined"===typeof a[e]&&(c[e]=a[e])}return c};
DateUtils.stringToDate=function(a,b){if(null===ObjectUtils.normalize(a)||null===ObjectUtils.normalize(b))return null;var c=a.replace(/[^a-zA-Z0-9]/g,"-"),d=b.replace(/[^a-zA-Z0-9]/g,"-"),e=d.split("-"),f=c.split("-");if(1===f.length&&1===e.length){if(c.length!==d.length)return null;var g=null,k=null;f=[];e=[];d+=" ";c+=" ";for(var h=0;h<d.length;h++)k!==d[h]&&(null!==g&&(f.push(c.substring(g,h)),e.push(d.substring(g,h))),g=h),k=d[h]}var m=e.indexOf("MM");h=e.indexOf("dd");k=e.indexOf("yyyy");var l=
e.indexOf("yy");g=e.indexOf("HH");c=e.indexOf("mm");e=e.indexOf("ss");d=new Date;-1<l&&(f[l]="20"+f[l],k=l);k=-1<k?f[k]:d.getFullYear();m=-1<m?f[m]-1:d.getMonth()-1;h=-1<h?f[h]:d.getDate();g=-1<g?f[g]:d.getHours();c=-1<c?f[c]:d.getMinutes();f=-1<e?f[e]:d.getSeconds();return new Date(k,m,h,g,c,f)};
DateUtils.dateToString=function(a,b){return a instanceof Date?b.replace(/[^a-zA-Z0-9]/g,"-").replace(/MM/,DateUtils.zeroPadString(a.getMonth()+1)).replace(/dd/,DateUtils.zeroPadString(a.getDate())).replace(/yyyy/,String(a.getFullYear())).replace(/yy/,String(a.getFullYear()).substring(2)).replace(/HH/,DateUtils.zeroPadString(a.getHours())).replace(/mm/,DateUtils.zeroPadString(a.getMinutes())).replace(/ss/,DateUtils.zeroPadString(a.getSeconds())):null};
DateUtils.isDate=function(a){return a instanceof Date};DateUtils.zeroPadString=function(a){if(null===ObjectUtils.normalize(a))return null;a=("number"===typeof a?a:parseInt(a)).toString();return 2<=a.length?a:"0"+a};EMVErrorMessage.prototype=NDObject.create(Convertible.prototype);EMVErrorMessage.prototype.getFieldMetadata=function(){return EMVErrorMessage.FieldMetadata};EMVErrorMessage.prototype.getErrorCode=function(){return this.errorCode};
EMVErrorMessage.prototype.setErrorCode=function(a){this.errorCode=a};EMVErrorMessage.prototype.getErrorDescription=function(){return this.errorDescription};EMVErrorMessage.prototype.setErrorDescription=function(a){this.errorDescription=a};EMVErrorMessage.prototype.getErrorDetails=function(){return this.errorDetails};EMVErrorMessage.prototype.setErrorDetails=function(a){this.errorDetails=a};EMVErrorMessage.prototype.getTransactionID=function(){return this.transactionID};
EMVErrorMessage.prototype.setTransactionID=function(a){this.transactionID=a};EMVErrorMessage.fromJson=function(a){var b=new EMVErrorMessage;JsonSerializer.readJsonIntoInstance(b,a);return b};EMVErrorMessage.fromMap=function(a){var b=new EMVErrorMessage;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};EMVErrorMessage.FieldMetadata={};for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(EMVErrorMessage.FieldMetadata[key]=Convertible.FieldMetadata[key]);
EMVErrorMessage.FieldMetadata.errorCode=(new FieldMetadata).setValueRetriever(function(a){return a instanceof EMVErrorMessage?a.getErrorCode():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof EMVErrorMessage&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setErrorCode(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,
1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
EMVErrorMessage.FieldMetadata.errorDescription=(new FieldMetadata).setValueRetriever(function(a){return a instanceof EMVErrorMessage?a.getErrorDescription():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof EMVErrorMessage&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setErrorDescription(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,
"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
EMVErrorMessage.FieldMetadata.errorDetails=(new FieldMetadata).setValueRetriever(function(a){return a instanceof EMVErrorMessage?a.getErrorDetails():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof EMVErrorMessage&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setErrorDetails(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?
a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
EMVErrorMessage.FieldMetadata.transactionID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof EMVErrorMessage?a.getTransactionID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof EMVErrorMessage&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setTransactionID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?
a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());AuthenticationType.prototype=NDObject.create(Enumerable.prototype);
AuthenticationType.VALUES={};AuthenticationType.fromString=function(a){if("undefined"===typeof AuthenticationType.VALUES[a])throw new IllegalArgumentException("value is not a valid AuthenticationType value.");return AuthenticationType.VALUES[a]};AuthenticationType.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof AuthenticationType.VALUES[a]?new AuthenticationType(TextUtils.stringify(a),!1):AuthenticationType.VALUES[a]};
AuthenticationType.STATIC=new AuthenticationType("01",!0);AuthenticationType.DYNAMIC=new AuthenticationType("02",!0);AuthenticationType.OOB=new AuthenticationType("03",!0);AuthenticationType.DECOUPLED=new AuthenticationType("04",!0);AuthenticationType.RESERVED80=new AuthenticationType("80",!0);AuthenticationType.RESERVED81=new AuthenticationType("81",!0);AuthenticationType.RESERVED82=new AuthenticationType("82",!0);AuthenticationType.RESERVED83=new AuthenticationType("83",!0);
function nsfoehwx(a,b){return a===b?!0:!1}AuthenticationType.RESERVED84=new AuthenticationType("84",!0);AuthenticationType.RESERVED85=new AuthenticationType("85",!0);AuthenticationType.RESERVED86=new AuthenticationType("86",!0);AuthenticationType.RESERVED87=new AuthenticationType("87",!0);AuthenticationType.RESERVED88=new AuthenticationType("88",!0);AuthenticationType.RESERVED89=new AuthenticationType("89",!0);AuthenticationType.RESERVED90=new AuthenticationType("90",!0);
AuthenticationType.RESERVED91=new AuthenticationType("91",!0);function EMVRuntimeErrorEvent(){Convertible.call(this);this.errorMessage=this.errorCode=null;Interface.implement(this,ISyncable)}AuthenticationType.RESERVED92=new AuthenticationType("92",!0);AuthenticationType.RESERVED93=new AuthenticationType("93",!0);AuthenticationType.RESERVED94=new AuthenticationType("94",!0);AuthenticationType.RESERVED95=new AuthenticationType("95",!0);AuthenticationType.RESERVED96=new AuthenticationType("96",!0);
AuthenticationType.RESERVED97=new AuthenticationType("97",!0);AuthenticationType.RESERVED98=new AuthenticationType("98",!0);AuthenticationType.RESERVED99=new AuthenticationType("99",!0);WhitelistStatus.prototype=NDObject.create(Enumerable.prototype);WhitelistStatus.VALUES={};WhitelistStatus.fromString=function(a){if("undefined"===typeof WhitelistStatus.VALUES[a])throw new IllegalArgumentException("value is not a valid WhitelistStatus value.");return WhitelistStatus.VALUES[a]};
WhitelistStatus.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof WhitelistStatus.VALUES[a]?new WhitelistStatus(TextUtils.stringify(a),!1):WhitelistStatus.VALUES[a]};WhitelistStatus.WHITELISTED=new WhitelistStatus("Y",!0);WhitelistStatus.NOT_WHITELISTED=new WhitelistStatus("N",!0);function Filter(){ValidationResult.call(this);this.mFilteredValue=null;this.mFilteredValueSet=!1}
function XMLHttpRequestConnection(){INdsConnection.call(this);"undefined"!==typeof XMLHttpRequest&&(this.mXhr=new XMLHttpRequest,this.withCredentials=!1)}function Type(){}WhitelistStatus.NOT_ELIGIBLE=new WhitelistStatus("E",!0);WhitelistStatus.PENDING_CONFIRMATION=new WhitelistStatus("P",!0);WhitelistStatus.REJECTED=new WhitelistStatus("R",!0);WhitelistStatus.UNKNOWN=new WhitelistStatus("U",!0);ApiVersionType.prototype=NDObject.create(Enumerable.prototype);ApiVersionType.VALUES={};
ApiVersionType.fromString=function(a){if("undefined"===typeof ApiVersionType.VALUES[a])throw new IllegalArgumentException("value is not a valid ApiVersionType value.");return ApiVersionType.VALUES[a]};ApiVersionType.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof ApiVersionType.VALUES[a]?new ApiVersionType(TextUtils.stringify(a),!1):ApiVersionType.VALUES[a]};ApiVersionType.V1=new ApiVersionType("V1",!0);ApiVersionType.V2=new ApiVersionType("V2",!0);
ThreeDSError.prototype=NDObject.create(Convertible.prototype);ThreeDSError.prototype.getFieldMetadata=function(){return ThreeDSError.FieldMetadata};ThreeDSError.prototype.getThreeDSServerTransID=function(){return this.threeDSServerTransID};function MsgType(a,b){Enumerable.call(this,a,MsgType.VALUES,b);b&&(MsgType.VALUES[a]=this)}ThreeDSError.prototype.setThreeDSServerTransID=function(a){this.threeDSServerTransID=a};ThreeDSError.prototype.getAcsTransID=function(){return this.acsTransID};
ThreeDSError.prototype.setAcsTransID=function(a){this.acsTransID=a};ThreeDSError.prototype.getDsTransID=function(){return this.dsTransID};ThreeDSError.prototype.setDsTransID=function(a){this.dsTransID=a};ThreeDSError.prototype.getErrorCode=function(){return this.errorCode};ThreeDSError.prototype.setErrorCode=function(a){this.errorCode=a};ThreeDSError.prototype.getErrorComponent=function(){return this.errorComponent};ThreeDSError.prototype.setErrorComponent=function(a){this.errorComponent=a};
ThreeDSError.prototype.getErrorDescription=function(){return this.errorDescription};ThreeDSError.prototype.setErrorDescription=function(a){this.errorDescription=a};ThreeDSError.prototype.getErrorDetail=function(){return this.errorDetail};ThreeDSError.prototype.setErrorDetail=function(a){this.errorDetail=a};ThreeDSError.prototype.getErrorMessageType=function(){return this.errorMessageType};ThreeDSError.prototype.setErrorMessageType=function(a){this.errorMessageType=a};
ThreeDSError.prototype.getMessageType=function(){return this.messageType};function Init(){}ThreeDSError.prototype.setMessageType=function(a){this.messageType=a};ThreeDSError.prototype.getMessageVersion=function(){return this.messageVersion};ThreeDSError.prototype.setMessageVersion=function(a){this.messageVersion=a};ThreeDSError.prototype.getSdkTransID=function(){return this.sdkTransID};ThreeDSError.prototype.setSdkTransID=function(a){this.sdkTransID=a};
ThreeDSError.fromJson=function(a){var b=new ThreeDSError;JsonSerializer.readJsonIntoInstance(b,a);return b};ThreeDSError.fromMap=function(a){var b=new ThreeDSError;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};ThreeDSError.FieldMetadata={};for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(ThreeDSError.FieldMetadata[key]=Convertible.FieldMetadata[key]);function CountryCode(a){CustomConvertible.call(this);this.setValue(a||null)}
ThreeDSError.FieldMetadata.threeDSServerTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ThreeDSError?a.getThreeDSServerTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ThreeDSError&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setThreeDSServerTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ThreeDSError.FieldMetadata.acsTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ThreeDSError?a.getAcsTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ThreeDSError&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setAcsTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ThreeDSError.FieldMetadata.dsTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ThreeDSError?a.getDsTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ThreeDSError&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setDsTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ThreeDSError.FieldMetadata.errorCode=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ThreeDSError?a.getErrorCode():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ThreeDSError&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ErrorCode,"ErrorCode")&&(a.setErrorCode(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return ErrorCode.fromObject(a)}).enableSerialization().setFieldType(Init.Type.ErrorCode).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,
!1).build());function TransactionReason(a,b){Enumerable.call(this,a,TransactionReason.VALUES,b);b&&(TransactionReason.VALUES[a]=this)}
ThreeDSError.FieldMetadata.errorComponent=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ThreeDSError?a.getErrorComponent():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ThreeDSError&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ErrorComponent,"ErrorComponent")&&(a.setErrorComponent(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return ErrorComponent.fromObject(a)}).enableSerialization().setFieldType(Init.Type.ErrorComponent).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,
!1).build());
ThreeDSError.FieldMetadata.errorDescription=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ThreeDSError?a.getErrorDescription():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ThreeDSError&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setErrorDescription(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?
a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,!1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,2048).addProperty(Property.MIN_LENGTH,1).build());
function UUID(a){CustomConvertible.call(this);this.setValue(a||null)}function SupportedVersionsResponse(){APIResponse.call(this);this.dsIdentifier=this.aCSVersion=this.messageExtension=this.dsEndProtocolVersion=this.dsStartProtocolVersion=this.threeDSMethodURL=this.acsEndProtocolVersion=this.acsStartProtocolVersion=this.threeDSServerTransID=this.threeDSServerEndVersion=this.threeDSServerStartVersion=null;Interface.implement(this,ISyncable)}
ThreeDSError.FieldMetadata.errorDetail=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ThreeDSError?a.getErrorDetail():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ThreeDSError&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setErrorDetail(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,
!1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,2048).addProperty(Property.MIN_LENGTH,1).build());
ThreeDSError.FieldMetadata.errorMessageType=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ThreeDSError?a.getErrorMessageType():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ThreeDSError&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,MsgType,"MsgType")&&(a.setErrorMessageType(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MsgType.fromObject(a)}).enableSerialization().setFieldType(Init.Type.MsgType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ThreeDSError.FieldMetadata.messageType=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ThreeDSError?a.getMessageType():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ThreeDSError&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,MsgType,"MsgType")&&(a.setMessageType(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MsgType.fromObject(a)}).enableSerialization().setFieldType(Init.Type.MsgType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ThreeDSError.FieldMetadata.messageVersion=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ThreeDSError?a.getMessageVersion():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ThreeDSError&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,MessageVersionType,"MessageVersionType")&&(a.setMessageVersion(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MessageVersionType.fromObject(a)}).enableSerialization().setFieldType(Init.Type.MessageVersionType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ThreeDSError.FieldMetadata.sdkTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ThreeDSError?a.getSdkTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ThreeDSError&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setSdkTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
TernaryYesNoUnavailable.prototype=NDObject.create(Enumerable.prototype);TernaryYesNoUnavailable.VALUES={};TernaryYesNoUnavailable.fromString=function(a){if("undefined"===typeof TernaryYesNoUnavailable.VALUES[a])throw new IllegalArgumentException("value is not a valid TernaryYesNoUnavailable value.");return TernaryYesNoUnavailable.VALUES[a]};
TernaryYesNoUnavailable.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof TernaryYesNoUnavailable.VALUES[a]?new TernaryYesNoUnavailable(TextUtils.stringify(a),!1):TernaryYesNoUnavailable.VALUES[a]};TernaryYesNoUnavailable.UNAVAILABLE=new TernaryYesNoUnavailable("U",!0);TernaryYesNoUnavailable.YES=new TernaryYesNoUnavailable("Y",!0);TernaryYesNoUnavailable.NO=new TernaryYesNoUnavailable("N",!0);ResultsResponse.prototype=NDObject.create(APIResponse.prototype);
ResultsResponse.prototype.getFieldMetadata=function(){return ResultsResponse.FieldMetadata};function ThreeDSRequest(a){this.headers={};this.encodedBody=null;this.withCredentials=!1;this.bodyMap="string"===typeof a?JSON.parse(a):a||{}}ResultsResponse.prototype.getThreeDSServerTransID=function(){return this.threeDSServerTransID};ResultsResponse.prototype.setThreeDSServerTransID=function(a){this.threeDSServerTransID=a};ResultsResponse.prototype.getDsTransID=function(){return this.dsTransID};
ResultsResponse.prototype.setDsTransID=function(a){this.dsTransID=a};ResultsResponse.prototype.getAcsTransID=function(){return this.acsTransID};ResultsResponse.prototype.setAcsTransID=function(a){this.acsTransID=a};ResultsResponse.prototype.getAuthenticationValue=function(){return this.authenticationValue};ResultsResponse.prototype.setAuthenticationValue=function(a){this.authenticationValue=a};ResultsResponse.prototype.getChallengeCancel=function(){return this.challengeCancel};
ResultsResponse.prototype.setChallengeCancel=function(a){this.challengeCancel=a};ResultsResponse.prototype.getEci=function(){return this.eci};ResultsResponse.prototype.setEci=function(a){this.eci=a};ResultsResponse.prototype.getInteractionCounter=function(){return this.interactionCounter};ResultsResponse.prototype.setInteractionCounter=function(a){this.interactionCounter=a};ResultsResponse.prototype.getMessageCategory=function(){return this.messageCategory};
ResultsResponse.prototype.setMessageCategory=function(a){this.messageCategory=a};ResultsResponse.prototype.getTransStatus=function(){return this.transStatus};ResultsResponse.prototype.setTransStatus=function(a){this.transStatus=a};function ValidationResult(){this.mFailures=[]}ResultsResponse.prototype.getTransStatusReason=function(){return this.transStatusReason};ResultsResponse.prototype.setTransStatusReason=function(a){this.transStatusReason=a};
ResultsResponse.fromJson=function(a){var b=new ResultsResponse;JsonSerializer.readJsonIntoInstance(b,a);return b};ResultsResponse.fromMap=function(a){var b=new ResultsResponse;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};ResultsResponse.FieldMetadata={};for(key in APIResponse.FieldMetadata)APIResponse.FieldMetadata.hasOwnProperty(key)&&(ResultsResponse.FieldMetadata[key]=APIResponse.FieldMetadata[key]);
ResultsResponse.FieldMetadata.threeDSServerTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ResultsResponse?a.getThreeDSServerTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ResultsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setThreeDSServerTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);
return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,!1).build());
ResultsResponse.FieldMetadata.dsTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ResultsResponse?a.getDsTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ResultsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setDsTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,
!1).build());
ResultsResponse.FieldMetadata.acsTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ResultsResponse?a.getAcsTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ResultsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setAcsTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,!1).build());
ResultsResponse.FieldMetadata.authenticationValue=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ResultsResponse?a.getAuthenticationValue():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ResultsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,Base64,"Base64")&&(a.setAuthenticationValue(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new Base64;b.setRawValue(a);
return b}).enableSerialization().setFieldType(Init.Type.Base64).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.LENGTH,28).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ResultsResponse.FieldMetadata.challengeCancel=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ResultsResponse?a.getChallengeCancel():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ResultsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,CancelIndicator,"CancelIndicator")&&(a.setChallengeCancel(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return CancelIndicator.fromObject(a)}).enableSerialization().setFieldType(Init.Type.CancelIndicator).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ResultsResponse.FieldMetadata.eci=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ResultsResponse?a.getEci():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ResultsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setEci(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.LENGTH,
2).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ResultsResponse.FieldMetadata.interactionCounter=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ResultsResponse?a.getInteractionCounter():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ResultsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setInteractionCounter(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,!1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.LENGTH,2).build());
ResultsResponse.FieldMetadata.messageCategory=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ResultsResponse?a.getMessageCategory():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ResultsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,MessageCategoryType,"MessageCategoryType")&&(a.setMessageCategory(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MessageCategoryType.fromObject(a)}).enableSerialization().setFieldType(Init.Type.MessageCategoryType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,
!1).build());
ResultsResponse.FieldMetadata.transStatus=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ResultsResponse?a.getTransStatus():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ResultsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,TransactionStatus,"TransactionStatus")&&(a.setTransStatus(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return TransactionStatus.fromObject(a)}).enableSerialization().setFieldType(Init.Type.TransactionStatus).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,!1).build());
ResultsResponse.FieldMetadata.transStatusReason=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ResultsResponse?a.getTransStatusReason():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ResultsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,TransactionReason,"TransactionReason")&&(a.setTransStatusReason(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return TransactionReason.fromObject(a)}).enableSerialization().setFieldType(Init.Type.TransactionReason).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetBrowserData.prototype=NDObject.create(Convertible.prototype);WidgetBrowserData.prototype.getFieldMetadata=function(){return WidgetBrowserData.FieldMetadata};WidgetBrowserData.prototype.getBrowserJavaEnabled=function(){return this.browserJavaEnabled};WidgetBrowserData.prototype.setBrowserJavaEnabled=function(a){this.browserJavaEnabled=a};function ValidatorContext(a){this.value=a}WidgetBrowserData.prototype.getBrowserJavascriptEnabled=function(){return this.browserJavascriptEnabled};
WidgetBrowserData.prototype.setBrowserJavascriptEnabled=function(a){this.browserJavascriptEnabled=a};WidgetBrowserData.prototype.getBrowserLanguage=function(){return this.browserLanguage};WidgetBrowserData.prototype.setBrowserLanguage=function(a){this.browserLanguage=a};WidgetBrowserData.prototype.getBrowserColorDepth=function(){return this.browserColorDepth};WidgetBrowserData.prototype.setBrowserColorDepth=function(a){this.browserColorDepth=a};WidgetBrowserData.prototype.getBrowserScreenHeight=function(){return this.browserScreenHeight};
WidgetBrowserData.prototype.setBrowserScreenHeight=function(a){this.browserScreenHeight=a};WidgetBrowserData.prototype.getBrowserScreenWidth=function(){return this.browserScreenWidth};WidgetBrowserData.prototype.setBrowserScreenWidth=function(a){this.browserScreenWidth=a};WidgetBrowserData.prototype.getBrowserTZ=function(){return this.browserTZ};WidgetBrowserData.prototype.setBrowserTZ=function(a){this.browserTZ=a};WidgetBrowserData.prototype.getBrowserUserAgent=function(){return this.browserUserAgent};
WidgetBrowserData.prototype.setBrowserUserAgent=function(a){this.browserUserAgent=a};WidgetBrowserData.prototype.getChallengeWindowSize=function(){return this.challengeWindowSize};function ICopyable(){}WidgetBrowserData.prototype.setChallengeWindowSize=function(a){this.challengeWindowSize=a};WidgetBrowserData.fromJson=function(a){var b=new WidgetBrowserData;JsonSerializer.readJsonIntoInstance(b,a);return b};
WidgetBrowserData.fromMap=function(a){var b=new WidgetBrowserData;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};function Challenge(){Convertible.call(this);this.messageVersion=this.messageType=this.acsTransID=this.threeDSServerTransID=null;Interface.implement(this,ISyncable)}WidgetBrowserData.FieldMetadata={};for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(WidgetBrowserData.FieldMetadata[key]=Convertible.FieldMetadata[key]);
WidgetBrowserData.FieldMetadata.browserJavaEnabled=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetBrowserData?a.getBrowserJavaEnabled():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetBrowserData&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,void 0,"Bool")&&(a.setBrowserJavaEnabled(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
void 0,"Bool")?a:null}).enableSerialization().setSerializationTarget("browserJavaEnabled").setFieldType(Init.Type.Bool).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());function nsovanfrso(a){return a.concat("nBCXNxbjl145j")}
WidgetBrowserData.FieldMetadata.browserJavascriptEnabled=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetBrowserData?a.getBrowserJavascriptEnabled():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetBrowserData&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,void 0,"Bool")&&(a.setBrowserJavascriptEnabled(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
void 0,"Bool")?a:null}).enableSerialization().setSerializationTarget("browserJavascriptEnabled").setFieldType(Init.Type.Bool).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetBrowserData.FieldMetadata.browserLanguage=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetBrowserData?a.getBrowserLanguage():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetBrowserData&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setBrowserLanguage(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setSerializationTarget("browserLanguage").setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,8).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetBrowserData.FieldMetadata.browserColorDepth=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetBrowserData?a.getBrowserColorDepth():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetBrowserData&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,BrowserColorDepthType,"BrowserColorDepthType")&&(a.setBrowserColorDepth(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return BrowserColorDepthType.fromObject(a)}).enableSerialization().setSerializationTarget("browserColorDepth").setFieldType(Init.Type.BrowserColorDepthType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetBrowserData.FieldMetadata.browserScreenHeight=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetBrowserData?a.getBrowserScreenHeight():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetBrowserData&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setBrowserScreenHeight(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setSerializationTarget("browserScreenHeight").setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,6).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetBrowserData.FieldMetadata.browserScreenWidth=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetBrowserData?a.getBrowserScreenWidth():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetBrowserData&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setBrowserScreenWidth(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setSerializationTarget("browserScreenWidth").setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,6).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetBrowserData.FieldMetadata.browserTZ=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetBrowserData?a.getBrowserTZ():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetBrowserData&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setBrowserTZ(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?
a:null}).enableSerialization().setSerializationTarget("browserTZ").setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,5).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetBrowserData.FieldMetadata.browserUserAgent=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetBrowserData?a.getBrowserUserAgent():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetBrowserData&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setBrowserUserAgent(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setSerializationTarget("browserUserAgent").setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,2048).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetBrowserData.FieldMetadata.challengeWindowSize=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetBrowserData?a.getChallengeWindowSize():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetBrowserData&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,WindowSize,"WindowSize")&&(a.setChallengeWindowSize(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return WindowSize.fromObject(a)}).enableSerialization().setSerializationTarget("challengeWindowSize").setFieldType(Init.Type.WindowSize).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WhitelistStatusSource.prototype=NDObject.create(Enumerable.prototype);WhitelistStatusSource.VALUES={};WhitelistStatusSource.fromString=function(a){if("undefined"===typeof WhitelistStatusSource.VALUES[a])throw new IllegalArgumentException("value is not a valid WhitelistStatusSource value.");return WhitelistStatusSource.VALUES[a]};
WhitelistStatusSource.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof WhitelistStatusSource.VALUES[a]?new WhitelistStatusSource(TextUtils.stringify(a),!1):WhitelistStatusSource.VALUES[a]};function Email(a){CustomConvertible.call(this);this.setRawValue(a)}WhitelistStatusSource.THREE_DS_SERVER=new WhitelistStatusSource("01",!0);function ApiVersionType(a,b){Enumerable.call(this,a,ApiVersionType.VALUES,b);b&&(ApiVersionType.VALUES[a]=this)}
WhitelistStatusSource.DS=new WhitelistStatusSource("02",!0);WhitelistStatusSource.ACS=new WhitelistStatusSource("03",!0);WhitelistStatusSource.RESERVED80=new WhitelistStatusSource("80",!0);WhitelistStatusSource.RESERVED81=new WhitelistStatusSource("81",!0);WhitelistStatusSource.RESERVED82=new WhitelistStatusSource("82",!0);WhitelistStatusSource.RESERVED83=new WhitelistStatusSource("83",!0);WhitelistStatusSource.RESERVED84=new WhitelistStatusSource("84",!0);
WhitelistStatusSource.RESERVED85=new WhitelistStatusSource("85",!0);WhitelistStatusSource.RESERVED86=new WhitelistStatusSource("86",!0);function CurrencyCode(a){CustomConvertible.call(this);this.setValue(a)}WhitelistStatusSource.RESERVED87=new WhitelistStatusSource("87",!0);WhitelistStatusSource.RESERVED88=new WhitelistStatusSource("88",!0);WhitelistStatusSource.RESERVED89=new WhitelistStatusSource("89",!0);WhitelistStatusSource.RESERVED90=new WhitelistStatusSource("90",!0);
WhitelistStatusSource.RESERVED91=new WhitelistStatusSource("91",!0);WhitelistStatusSource.RESERVED92=new WhitelistStatusSource("92",!0);WhitelistStatusSource.RESERVED93=new WhitelistStatusSource("93",!0);WhitelistStatusSource.RESERVED94=new WhitelistStatusSource("94",!0);WhitelistStatusSource.RESERVED95=new WhitelistStatusSource("95",!0);WhitelistStatusSource.RESERVED96=new WhitelistStatusSource("96",!0);WhitelistStatusSource.RESERVED97=new WhitelistStatusSource("97",!0);
WhitelistStatusSource.RESERVED98=new WhitelistStatusSource("98",!0);WhitelistStatusSource.RESERVED99=new WhitelistStatusSource("99",!0);ACSRenderingType.prototype=NDObject.create(Convertible.prototype);ACSRenderingType.prototype.getFieldMetadata=function(){return ACSRenderingType.FieldMetadata};ACSRenderingType.prototype.getAcsInterface=function(){return this.acsInterface};ACSRenderingType.prototype.setAcsInterface=function(a){this.acsInterface=a};ACSRenderingType.prototype.getAcsUiTemplate=function(){return this.acsUiTemplate};
ACSRenderingType.prototype.setAcsUiTemplate=function(a){this.acsUiTemplate=a};function nsdzkhnrcu(){return 692441607}ACSRenderingType.fromJson=function(a){var b=new ACSRenderingType;JsonSerializer.readJsonIntoInstance(b,a);return b};ACSRenderingType.fromMap=function(a){var b=new ACSRenderingType;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};ACSRenderingType.FieldMetadata={};
for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(ACSRenderingType.FieldMetadata[key]=Convertible.FieldMetadata[key]);
ACSRenderingType.FieldMetadata.acsInterface=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ACSRenderingType?a.getAcsInterface():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ACSRenderingType&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ACSInterface,"ACSInterface")&&(a.setAcsInterface(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return ACSInterface.fromObject(a)}).enableSerialization().setFieldType(Init.Type.ACSInterface).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
ACSRenderingType.FieldMetadata.acsUiTemplate=(new FieldMetadata).setValueRetriever(function(a){return a instanceof ACSRenderingType?a.getAcsUiTemplate():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof ACSRenderingType&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ACSUITemplate,"ACSUITemplate")&&(a.setAcsUiTemplate(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return ACSUITemplate.fromObject(a)}).enableSerialization().setFieldType(Init.Type.ACSUITemplate).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
Action.prototype=NDObject.create(Enumerable.prototype);Action.VALUES={};Action.fromString=function(a){if("undefined"===typeof Action.VALUES[a])throw new IllegalArgumentException("value is not a valid Action value.");return Action.VALUES[a]};Action.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof Action.VALUES[a]?new Action(TextUtils.stringify(a),!1):Action.VALUES[a]};Action.INIT=new Action("init",!0);Action.CHALLENGE_REQUEST=new Action("challenge-request",!0);
Action.APP_FINAL_CHALLENGE_RESPONSE=new Action("app-final-cres",!0);StatusMsg.prototype=NDObject.create(Enumerable.prototype);function ACSRenderingType(){Convertible.call(this);this.acsUiTemplate=this.acsInterface=null;Interface.implement(this,ISyncable)}StatusMsg.VALUES={};StatusMsg.fromString=function(a){if("undefined"===typeof StatusMsg.VALUES[a])throw new IllegalArgumentException("value is not a valid StatusMsg value.");return StatusMsg.VALUES[a]};
StatusMsg.fromObject=function(a){return null!==ObjectUtils.normalize(a)&&"undefined"===typeof StatusMsg.VALUES[a]?new StatusMsg(TextUtils.stringify(a),!1):StatusMsg.VALUES[a]};StatusMsg.G_ZIP_COMPRESS_FAILURE=new StatusMsg("The client was unable to compress the request content.",!0);StatusMsg.NO_ENVIRONMENT_DATA=new StatusMsg("Must provide a valid EnvironmentData object.",!0);StatusMsg.JSON_DECODE_FAILURE=new StatusMsg("JSON decode failure.",!0);
StatusMsg.NO_ADDITIONAL_INFORMATION=new StatusMsg("No additional information.",!0);StatusMsg.UNEXPECTED_EVENT=new StatusMsg("The client encountered an unexpected event while",!0);StatusMsg.CLIENT_TIMEOUT=new StatusMsg("A client timeout occurred while",!0);StatusMsg.PROTOCOL_ERROR=new StatusMsg("A protocol error occurred while",!0);StatusMsg.UNKNOWN_HOST=new StatusMsg("The client is unable to reach: ",!0);
StatusMsg.SSL_HANDSHAKE_ERROR=new StatusMsg("The client and server could not negotiate the desired level of security during SSL handshake.",!0);StatusMsg.UNKNOWN_ACTION=new StatusMsg("Unknown action.",!0);StatusMsg.INIT_REQUEST_FAILURE=new StatusMsg("Could not initialize the request.",!0);StatusMsg.SV_RES_ERROR=new StatusMsg("Supported versions response error.",!0);StatusMsg.INIT_RESPONSE_FAILURE=new StatusMsg("Could not initialize the response.",!0);
StatusMsg.POST_REQUEST_PARSE_FAILURE=new StatusMsg("Could not parse the POST request.",!0);StatusMsg.A_RES_ERROR=new StatusMsg("Authenticate response error.",!0);StatusMsg.RESPONSE_ERROR=new StatusMsg("Could not get response.",!0);StatusMsg.RESPONSE_TIMEOUT=new StatusMsg("Response timed out.",!0);StatusMsg.MISSING_FIELDS=new StatusMsg("One or more fields are missing.",!0);StatusMsg.INVALID_ISO_CODES=new StatusMsg("One or more invalid ISO codes found.",!0);
function JsonSerializer(a){this.mValueProcessor=a||null}function TernaryYesNoUnavailable(a,b){Enumerable.call(this,a,TernaryYesNoUnavailable.VALUES,b);b&&(TernaryYesNoUnavailable.VALUES[a]=this)}StatusMsg.MALFORMED_FIELDS=new StatusMsg("One or more fields are malformed.",!0);StatusMsg.INVALID_DCO_KEYS=new StatusMsg("DCOMap keys must be the appropriate class for value.",!0);
function WidgetOutcomeMessage(){Convertible.call(this);this.resultsResponse=this.challengeOutcome=this.error=this.transStatus=this.type=null;Interface.implement(this,ISyncable)}StatusMsg.ERROR_WITH_CODE=new StatusMsg("Got an error with the code",!0);StatusMsg.WIDGET_ERROR=new StatusMsg("3DS widget error.",!0);StatusMsg.MISSING_ACS_URL=new StatusMsg("acsURL is missing from the Authentication response.",!0);StatusMsg.MISSING_DS_ID=new StatusMsg("Directory Server ID is missing.",!0);
function StatusMsg(a,b){Enumerable.call(this,a,StatusMsg.VALUES,b);b&&(StatusMsg.VALUES[a]=this)}StatusMsg.UNEXPECTED_ERROR=new StatusMsg("Unexpected error.",!0);function Action(a,b){Enumerable.call(this,a,Action.VALUES,b);b&&(Action.VALUES[a]=this)}StatusMsg.UNKNOWN_ERROR=new StatusMsg("Unknown error: {0}",!0);StatusMsg.UNEXPECTED_RESPONSE=new StatusMsg("Unexpected response from server.",!0);StatusMsg.CHALLENGE_FAILED=new StatusMsg("Challenge failed.",!0);
function ArrayContainerValidator(a,b,c){BaseContainerValidator.call(this,a,b,c,!0)}StatusMsg.CHALLENGE_CANCELLED=new StatusMsg("Challenge cancelled.",!0);StatusMsg.CHALLENGE_TIMEOUT=new StatusMsg("Challenge timed out.",!0);StatusMsg.ACTIVITY_NOT_FOUND=new StatusMsg("Placeholder activity doesn't exist.",!0);StatusMsg.VIEW_NOT_FOUND=new StatusMsg("Placeholder view doesn't exist.",!0);function NDObject(){}StatusMsg.INVALID_ENDPOINT=new StatusMsg("Invalid 3DS API Endpoint.",!0);
StatusMsg.CREATE_TRANSACTION_FAILURE=new StatusMsg("Could not create transaction.",!0);StatusMsg.INVALID_NAVIGATION_PLACEHOLDER=new StatusMsg("Placeholder must inherit from UINavigationController.",!0);StatusMsg.SENDING_ACTION=new StatusMsg(" sending data to the server",!0);StatusMsg.RECEIVING_ACTION=new StatusMsg(" receiving data from the server",!0);StatusMsg.NO_CONNECTION=new StatusMsg("Connection is not opened.",!0);
StatusMsg.SDK_PUBLIC_KEY_MISMATCH=new StatusMsg("Received SDK public key does not match.",!0);function Validator(a){this.mMetadataProvider=a}StatusMsg.THREE_DSSDK_INITIALIZATION_FAILURE=new StatusMsg("ThreeDS SDK has not been properly initialized. Please verify the configuration parameters.",!0);StatusMsg.INVALID_AUTH_REQUEST_PARAMETERS=new StatusMsg("Authentication request parameters did not initialize correctly.",!0);StatusMsg.THREE_DS_FAILURE=new StatusMsg("3DS Flow Error.",!0);
StatusMsg.GENERAL_THREE_DS_FLOW_ERROR_MESSAGE=new StatusMsg("3DS Flow Failure. Please check the messages.",!0);StatusMsg.CERTIFICATE_CHAIN_VERIFICATION_FAILED=new StatusMsg("Certificate Chain Verification Failed.",!0);StatusMsg.HTTP_CONNECTION_BEFORE_BAD_RETURN_TYPE=new StatusMsg("httpConnection.before must return a ThreeDSRequest instance.",!0);StatusMsg.DEVICE_INFO_BAD_RETURN_TYPE=new StatusMsg("promises.deviceInfo must return a DeviceData instance.",!0);function IMetadataProvider(){}
StatusMsg.INVALID_JWS_COMPACT_SERIALIZATION_FORMAT=new StatusMsg("Wrong JWS compact serialization format.",!0);StatusMsg.INVALID_PUBLIC_KEY=new StatusMsg("Invalid Public Key: {0}",!0);StatusMsg.INVALID_PUBLIC_KEY_TYPE=new StatusMsg("Invalid Public Key Type: {0}",!0);StatusMsg.INVALID_DATA_TYPE=new StatusMsg("{0} must be {1}.",!0);StatusMsg.INVALID_DATA_TYPE_REQUIRED=new StatusMsg("{0} is required and must be {1}.",!0);
StatusMsg.TOO_MANY_CHALLENGE_STEPS=new StatusMsg("Too many steps in challenge (\x3e999). Abandoning flow.",!0);StatusMsg.DATA_DECRYPTION_FAILURE=new StatusMsg("Failed to decrypt data.",!0);StatusMsg.DATA_DECRYPTION_FAILURE_DETAILS=new StatusMsg("Failed to decrypt data: {0}",!0);StatusMsg.INVALID_DSID=new StatusMsg("Invalid DS ID: {0}",!0);StatusMsg.TIMEOUT_TOO_LOW=new StatusMsg("Timeout must be greater than or equal to 5.",!0);
StatusMsg.MALFORMED_CONFIG_ATTRIBUTES=new StatusMsg("attrs is malformed. Expected array of objects with exactly one string value.",!0);StatusMsg.UNSUPPORTED_APP_SERVER_REQUEST=new StatusMsg("Unsupported App Server Request: {0}.",!0);StatusMsg.INVALID_CHALLENGE_RESPONSE=new StatusMsg("Challenge response validation failed: {0}.",!0);DoubleValidator.prototype=NDObject.create(BaseValidator.prototype);
DoubleValidator.prototype.validate=function(a,b,c){var d=new ValidationResult;d.failIf(this._validateArguments(b,a));var e=MathUtil.validateDoubleOrNull(b,c);e.isSuccess()||d.fail(b,a,[Property.FORMAT]);e.isSuccess()&&(c=e.getFilteredValue());d.isSuccess()&&(d.failIf(MathUtil.validateGreaterThanEqualOrNull(b,c,a[Property.MIN.toString()],Property.MIN),a),d.failIf(MathUtil.validateLessThanEqualOrNull(b,c,a[Property.MAX.toString()],Property.MAX),a));return d};
DoubleValidator.prototype.getAllowedProperties=function(){return[Property.MIN,Property.MAX]};BaseContainerValidator.prototype=NDObject.create(BaseValidator.prototype);BaseContainerValidator.prototype.validate=function(a,b,c){return this.mObjectElementValidator.validate(a,b,c)};BaseContainerValidator.prototype.validateElement=function(a){return this.mManager.validateChild(this._getParentFieldName(),this.mMetadata,a)};BaseContainerValidator.prototype.getAllowedProperties=function(){return this.mObjectElementValidator.getAllowedProperties()};
ObjectContainerValidator.prototype=NDObject.create(BaseContainerValidator.prototype);ObjectContainerValidator.prototype.validate=function(a,b,c){var d=new ValidationResult;d.failIf(this._validateArguments(b,a));d.failIf(BaseContainerValidator.prototype.validate.call(this,a,b,c));d.failIf(this.validateElement(c));return d};MapContainerValidator.prototype=NDObject.create(BaseContainerValidator.prototype);
MapContainerValidator.prototype.validate=function(a,b,c){var d=new ValidationResult;d.failIf(this._validateArguments(b,a));d.failIf(BaseContainerValidator.prototype.validate.call(this,a,b,c));if(!ObjectUtils.isMap(c))return null!==ObjectUtils.normalize(c)&&d.fail(TextUtils.concat(".",[this._getParentFieldName(),b]),a,[Property.FORMAT]),d;for(var e in c)null===ObjectUtils.normalize(c[e])?delete c[e]:d.failIf(this.validateElement(c[e]));return d};ArrayContainerValidator.prototype=NDObject.create(BaseContainerValidator.prototype);
ArrayContainerValidator.prototype.validate=function(a,b,c){var d=new ValidationResult;d.failIf(this._validateArguments(b,a));d.failIf(BaseContainerValidator.prototype.validate.call(this,a,b,c));if(!Array.isArray(c))return null!==ObjectUtils.normalize(c)&&d.fail(b,null,[Property.FORMAT]),d;d.failIf(MathUtil.validateGreaterThanEqualOrNull(b,c.length,a[Property.MIN_LENGTH.toString()],Property.MIN_LENGTH),a);d.failIf(MathUtil.validateLessThanEqualOrNull(b,c.length,a[Property.MAX_LENGTH.toString()],Property.MAX_LENGTH),
a);a=function(a){return null!==ObjectUtils.normalize(a)};if("function"===typeof c.filter)c=c.filter(a);else{b=[];for(var e=0;e<c.length;e++)a(c[e])&&b.push(c[e])}for(e=0;e<c.length;e++)d.failIf(this.validateElement(c[e]));return d};ArrayContainerValidator.prototype.getAllowedProperties=function(){return[Property.ALLOW_EMPTY,Property.MIN_LENGTH,Property.MAX_LENGTH]};IntValidator.prototype=NDObject.create(BaseValidator.prototype);
IntValidator.prototype.validate=function(a,b,c){var d=new ValidationResult;d.failIf(this._validateArguments.call(b,a));var e=c;!0===a[Property.STRICT.toString()]?d.failIf(MathUtil.validateIntegerOrNull(b,c),a):(c=MathUtil.validateDoubleOrNull(b,c),d.failIf(c,a),c.isSuccess()&&(e=c.getFilteredValue()));d.isSuccess()&&(d.failIf(MathUtil.validateGreaterThanEqualOrNull(b,e,a[Property.MIN.toString()],Property.MIN),a),d.failIf(MathUtil.validateLessThanEqualOrNull(b,e,a[Property.MAX.toString()],Property.MAX),
a));return d};IntValidator.prototype.getAllowedProperties=function(){return[Property.STRICT,Property.MIN,Property.MAX]};AuthenticationRequestWebParameters.prototype=NDObject.create(Convertible.prototype);AuthenticationRequestWebParameters.prototype.getFieldMetadata=function(){return AuthenticationRequestWebParameters.FieldMetadata};AuthenticationRequestWebParameters.prototype.getBrowserData=function(){return this.browserData};
AuthenticationRequestWebParameters.prototype.setBrowserData=function(a){this.browserData=a};function MessageExtensionAttributes(){Convertible.call(this);this.name=this.id=this.data=this.criticalityIndicator=null;Interface.implement(this,ISyncable)}AuthenticationRequestWebParameters.prototype.getThreeDSServerTransID=function(){return this.threeDSServerTransID};AuthenticationRequestWebParameters.prototype.setThreeDSServerTransID=function(a){this.threeDSServerTransID=a};
AuthenticationRequestWebParameters.prototype.getThreeDSCompInd=function(){return this.threeDSCompInd};AuthenticationRequestWebParameters.prototype.setThreeDSCompInd=function(a){this.threeDSCompInd=a};AuthenticationRequestWebParameters.fromJson=function(a){var b=new AuthenticationRequestWebParameters;JsonSerializer.readJsonIntoInstance(b,a);return b};AuthenticationRequestWebParameters.fromMap=function(a){var b=new AuthenticationRequestWebParameters;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};
AuthenticationRequestWebParameters.FieldMetadata={};for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(AuthenticationRequestWebParameters.FieldMetadata[key]=Convertible.FieldMetadata[key]);function CancelIndicator(a,b){Enumerable.call(this,a,CancelIndicator.VALUES,b);b&&(CancelIndicator.VALUES[a]=this)}
AuthenticationRequestWebParameters.FieldMetadata.browserData=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationRequestWebParameters?a.getBrowserData():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationRequestWebParameters&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,WidgetBrowserData,"WidgetBrowserData")&&(a.setBrowserData(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(){return new WidgetBrowserData}).enableDcoSync().enableSerialization().setFieldType(Init.Type.WidgetBrowserData).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationRequestWebParameters.FieldMetadata.threeDSServerTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationRequestWebParameters?a.getThreeDSServerTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationRequestWebParameters&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setThreeDSServerTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=
new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationRequestWebParameters.FieldMetadata.threeDSCompInd=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationRequestWebParameters?a.getThreeDSCompInd():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationRequestWebParameters&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,TernaryYesNoUnavailable,"TernaryYesNoUnavailable")&&(a.setThreeDSCompInd(b),c=FieldMetadata.SetterResult.SUCCESS));
return c}).setValueInitializer(function(a){return TernaryYesNoUnavailable.fromObject(a)}).enableSerialization().setFieldType(Init.Type.TernaryYesNoUnavailable).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());EMVRuntimeErrorEvent.prototype=NDObject.create(Convertible.prototype);EMVRuntimeErrorEvent.prototype.getFieldMetadata=function(){return EMVRuntimeErrorEvent.FieldMetadata};
EMVRuntimeErrorEvent.prototype.getErrorCode=function(){return this.errorCode};EMVRuntimeErrorEvent.prototype.setErrorCode=function(a){this.errorCode=a};EMVRuntimeErrorEvent.prototype.getErrorMessage=function(){return this.errorMessage};EMVRuntimeErrorEvent.prototype.setErrorMessage=function(a){this.errorMessage=a};EMVRuntimeErrorEvent.fromJson=function(a){var b=new EMVRuntimeErrorEvent;JsonSerializer.readJsonIntoInstance(b,a);return b};
EMVRuntimeErrorEvent.fromMap=function(a){var b=new EMVRuntimeErrorEvent;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};EMVRuntimeErrorEvent.FieldMetadata={};for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(EMVRuntimeErrorEvent.FieldMetadata[key]=Convertible.FieldMetadata[key]);
EMVRuntimeErrorEvent.FieldMetadata.errorCode=(new FieldMetadata).setValueRetriever(function(a){return a instanceof EMVRuntimeErrorEvent?a.getErrorCode():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof EMVRuntimeErrorEvent&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setErrorCode(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,
"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
EMVRuntimeErrorEvent.FieldMetadata.errorMessage=(new FieldMetadata).setValueRetriever(function(a){return a instanceof EMVRuntimeErrorEvent?a.getErrorMessage():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof EMVRuntimeErrorEvent&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setErrorMessage(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());ChallengeResponse.prototype=NDObject.create(ChallengeOutcome.prototype);
ChallengeResponse.prototype.getFieldMetadata=function(){return ChallengeResponse.FieldMetadata};ChallengeResponse.FieldMetadata={};for(key in ChallengeOutcome.FieldMetadata)ChallengeOutcome.FieldMetadata.hasOwnProperty(key)&&(ChallengeResponse.FieldMetadata[key]=ChallengeOutcome.FieldMetadata[key]);AuthenticationResponse.prototype=NDObject.create(APIResponse.prototype);AuthenticationResponse.prototype.getFieldMetadata=function(){return AuthenticationResponse.FieldMetadata};
AuthenticationResponse.prototype.getSdkTransID=function(){return this.sdkTransID};AuthenticationResponse.prototype.setSdkTransID=function(a){this.sdkTransID=a};AuthenticationResponse.prototype.getThreeDSServerTransID=function(){return this.threeDSServerTransID};AuthenticationResponse.prototype.setThreeDSServerTransID=function(a){this.threeDSServerTransID=a};AuthenticationResponse.prototype.getAcsChallengeMandated=function(){return this.acsChallengeMandated};
AuthenticationResponse.prototype.setAcsChallengeMandated=function(a){this.acsChallengeMandated=a};AuthenticationResponse.prototype.getAcsDecConInd=function(){return this.acsDecConInd};AuthenticationResponse.prototype.setAcsDecConInd=function(a){this.acsDecConInd=a};AuthenticationResponse.prototype.getAcsOperatorID=function(){return this.acsOperatorID};AuthenticationResponse.prototype.setAcsOperatorID=function(a){this.acsOperatorID=a};
function INdsConnection(){this.mOnSuccess=function(){};this.mOnError=function(){}}AuthenticationResponse.prototype.getAcsReferenceNumber=function(){return this.acsReferenceNumber};AuthenticationResponse.prototype.setAcsReferenceNumber=function(a){this.acsReferenceNumber=a};AuthenticationResponse.prototype.getAcsRenderingType=function(){return this.acsRenderingType};AuthenticationResponse.prototype.setAcsRenderingType=function(a){this.acsRenderingType=a};
AuthenticationResponse.prototype.getAcsSignedContent=function(){return this.acsSignedContent};AuthenticationResponse.prototype.setAcsSignedContent=function(a){this.acsSignedContent=a};AuthenticationResponse.prototype.getAcsTransID=function(){return this.acsTransID};AuthenticationResponse.prototype.setAcsTransID=function(a){this.acsTransID=a};AuthenticationResponse.prototype.getAcsURL=function(){return this.acsURL};AuthenticationResponse.prototype.setAcsURL=function(a){this.acsURL=a};
AuthenticationResponse.prototype.getAuthenticationType=function(){return this.authenticationType};function ACSUITemplate(a,b){Enumerable.call(this,a,ACSUITemplate.VALUES,b);b&&(ACSUITemplate.VALUES[a]=this)}AuthenticationResponse.prototype.setAuthenticationType=function(a){this.authenticationType=a};AuthenticationResponse.prototype.getAuthenticationValue=function(){return this.authenticationValue};AuthenticationResponse.prototype.setAuthenticationValue=function(a){this.authenticationValue=a};
AuthenticationResponse.prototype.getBroadInfo=function(){null===ObjectUtils.normalize(this.broadInfo)&&(this.broadInfo={});return this.broadInfo};AuthenticationResponse.prototype.setBroadInfo=function(a){this.broadInfo=a};AuthenticationResponse.prototype.putBroadInfo=function(a,b){this.getBroadInfo()["function"===typeof a.toString?a.toString():a]=b};AuthenticationResponse.prototype.getCardholderInfo=function(){return this.cardholderInfo};
AuthenticationResponse.prototype.setCardholderInfo=function(a){this.cardholderInfo=a};AuthenticationResponse.prototype.getDsReferenceNumber=function(){return this.dsReferenceNumber};AuthenticationResponse.prototype.setDsReferenceNumber=function(a){this.dsReferenceNumber=a};AuthenticationResponse.prototype.getDsTransID=function(){return this.dsTransID};AuthenticationResponse.prototype.setDsTransID=function(a){this.dsTransID=a};AuthenticationResponse.prototype.getEci=function(){return this.eci};
function WhitelistStatus(a,b){Enumerable.call(this,a,WhitelistStatus.VALUES,b);b&&(WhitelistStatus.VALUES[a]=this)}AuthenticationResponse.prototype.setEci=function(a){this.eci=a};AuthenticationResponse.prototype.getMessageExtension=function(){null===ObjectUtils.normalize(this.messageExtension)&&(this.messageExtension=[]);return this.messageExtension};AuthenticationResponse.prototype.setMessageExtension=function(a){this.messageExtension=a};AuthenticationResponse.prototype.addMessageExtension=function(a){this.getMessageExtension().push(a)};
function BaseValidator(a,b){this.mParentFieldName=a;this.mValidateArguments="undefined"!==typeof b?b:!0}AuthenticationResponse.prototype.getMessageType=function(){return this.messageType};function AuthenticationType(a,b){Enumerable.call(this,a,AuthenticationType.VALUES,b);b&&(AuthenticationType.VALUES[a]=this)}function ObjectValidator(a){BaseValidator.call(this,a,!1)}AuthenticationResponse.prototype.setMessageType=function(a){this.messageType=a};
AuthenticationResponse.prototype.getMessageVersion=function(){return this.messageVersion};AuthenticationResponse.prototype.setMessageVersion=function(a){this.messageVersion=a};AuthenticationResponse.prototype.getTransStatus=function(){return this.transStatus};AuthenticationResponse.prototype.setTransStatus=function(a){this.transStatus=a};AuthenticationResponse.prototype.getTransStatusReason=function(){return this.transStatusReason};
AuthenticationResponse.prototype.setTransStatusReason=function(a){this.transStatusReason=a};AuthenticationResponse.prototype.getEncodedCReq=function(){return this.encodedCReq};AuthenticationResponse.prototype.setEncodedCReq=function(a){this.encodedCReq=a};AuthenticationResponse.prototype.getWhiteListStatus=function(){return this.whiteListStatus};AuthenticationResponse.prototype.setWhiteListStatus=function(a){this.whiteListStatus=a};AuthenticationResponse.prototype.getWhiteListStatusSource=function(){return this.whiteListStatusSource};
function nsbfhmwjpt(a){return(new Date(a)).getMinutes()}AuthenticationResponse.prototype.setWhiteListStatusSource=function(a){this.whiteListStatusSource=a};AuthenticationResponse.fromJson=function(a){var b=new AuthenticationResponse;JsonSerializer.readJsonIntoInstance(b,a);return b};AuthenticationResponse.fromMap=function(a){var b=new AuthenticationResponse;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};AuthenticationResponse.FieldMetadata={};
for(key in APIResponse.FieldMetadata)APIResponse.FieldMetadata.hasOwnProperty(key)&&(AuthenticationResponse.FieldMetadata[key]=APIResponse.FieldMetadata[key]);
AuthenticationResponse.FieldMetadata.sdkTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getSdkTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setSdkTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.threeDSServerTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getThreeDSServerTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setThreeDSServerTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;
b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.acsChallengeMandated=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getAcsChallengeMandated():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,BooleanYesNo,"BooleanYesNo")&&(a.setAcsChallengeMandated(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return BooleanYesNo.fromObject(a)}).enableSerialization().setFieldType(Init.Type.BooleanYesNo).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.acsDecConInd=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getAcsDecConInd():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,BooleanYesNo,"BooleanYesNo")&&(a.setAcsDecConInd(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return BooleanYesNo.fromObject(a)}).enableSerialization().setFieldType(Init.Type.BooleanYesNo).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.acsOperatorID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getAcsOperatorID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setAcsOperatorID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.acsReferenceNumber=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getAcsReferenceNumber():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setAcsReferenceNumber(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,32).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.acsRenderingType=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getAcsRenderingType():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ACSRenderingType,"ACSRenderingType")&&(a.setAcsRenderingType(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(){return new ACSRenderingType}).enableDcoSync().enableSerialization().setFieldType(Init.Type.ACSRenderingType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.acsSignedContent=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getAcsSignedContent():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setAcsSignedContent(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.acsTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getAcsTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setAcsTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.acsURL=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getAcsURL():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,URL,"URL")&&(a.setAcsURL(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new URL;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.URL).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.authenticationType=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getAuthenticationType():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,AuthenticationType,"AuthenticationType")&&(a.setAuthenticationType(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return AuthenticationType.fromObject(a)}).enableSerialization().setFieldType(Init.Type.AuthenticationType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.authenticationValue=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getAuthenticationValue():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,Base64,"Base64")&&(a.setAuthenticationValue(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new Base64;
b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.Base64).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.LENGTH,28).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.broadInfo=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getBroadInfo():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;if(a instanceof AuthenticationResponse){c=FieldMetadata.SetterResult.FAILURE;var d=Object;ObjectUtils.isArray(b)&&2===b.length&&"string"===typeof b[0]&&MetadataUtils.isInstanceOf(b[1],d,"Object")&&(a.putBroadInfo(b[0],b[1]),c=FieldMetadata.SetterResult.SUCCESS)}return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
Object,"Object")?a:null}).enableSerialization().setFieldType(Init.Type.Object).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.ALLOW_EMPTY,!1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.MAP).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.cardholderInfo=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getCardholderInfo():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setCardholderInfo(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,128).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.dsReferenceNumber=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getDsReferenceNumber():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setDsReferenceNumber(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.dsTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getDsTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setDsTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.eci=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getEci():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setEci(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?a:
null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.LENGTH,2).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.messageExtension=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getMessageExtension():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,MessageExtensionAttributes,"MessageExtensionAttributes")&&(a.addMessageExtension(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(){return new MessageExtensionAttributes}).enableDcoSync().enableSerialization().setFieldType(Init.Type.MessageExtensionAttributes).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.ARRAY).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.MAX_LENGTH,
10).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.ALLOW_EMPTY,!1).build());function ThreeDSDate(a){CustomConvertible.call(this);this.mValidationOptions={};this.setRawValue(a)}
AuthenticationResponse.FieldMetadata.messageType=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getMessageType():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,MsgType,"MsgType")&&(a.setMessageType(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MsgType.fromObject(a)}).enableSerialization().setFieldType(Init.Type.MsgType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.messageVersion=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getMessageVersion():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,MessageVersionType,"MessageVersionType")&&(a.setMessageVersion(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MessageVersionType.fromObject(a)}).enableSerialization().setFieldType(Init.Type.MessageVersionType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.transStatus=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getTransStatus():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,TransactionStatus,"TransactionStatus")&&(a.setTransStatus(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return TransactionStatus.fromObject(a)}).enableSerialization().setFieldType(Init.Type.TransactionStatus).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.transStatusReason=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getTransStatusReason():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,TransactionReason,"TransactionReason")&&(a.setTransStatusReason(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return TransactionReason.fromObject(a)}).enableSerialization().setFieldType(Init.Type.TransactionReason).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.encodedCReq=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getEncodedCReq():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,Base64URL,"Base64URL")&&(a.setEncodedCReq(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new Base64URL;b.setRawValue(a);
return b}).enableSerialization().setFieldType(Init.Type.Base64URL).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.whiteListStatus=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getWhiteListStatus():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,WhitelistStatus,"WhitelistStatus")&&(a.setWhiteListStatus(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return WhitelistStatus.fromObject(a)}).enableSerialization().setFieldType(Init.Type.WhitelistStatus).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
AuthenticationResponse.FieldMetadata.whiteListStatusSource=(new FieldMetadata).setValueRetriever(function(a){return a instanceof AuthenticationResponse?a.getWhiteListStatusSource():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof AuthenticationResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,WhitelistStatusSource,"WhitelistStatusSource")&&(a.setWhiteListStatusSource(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return WhitelistStatusSource.fromObject(a)}).enableSerialization().setFieldType(Init.Type.WhitelistStatusSource).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
SupportedVersionsResponse.prototype=NDObject.create(APIResponse.prototype);SupportedVersionsResponse.prototype.getFieldMetadata=function(){return SupportedVersionsResponse.FieldMetadata};SupportedVersionsResponse.prototype.getThreeDSServerStartVersion=function(){return this.threeDSServerStartVersion};SupportedVersionsResponse.prototype.setThreeDSServerStartVersion=function(a){this.threeDSServerStartVersion=a};SupportedVersionsResponse.prototype.getThreeDSServerEndVersion=function(){return this.threeDSServerEndVersion};
SupportedVersionsResponse.prototype.setThreeDSServerEndVersion=function(a){this.threeDSServerEndVersion=a};SupportedVersionsResponse.prototype.getThreeDSServerTransID=function(){return this.threeDSServerTransID};SupportedVersionsResponse.prototype.setThreeDSServerTransID=function(a){this.threeDSServerTransID=a};SupportedVersionsResponse.prototype.getAcsStartProtocolVersion=function(){return this.acsStartProtocolVersion};
SupportedVersionsResponse.prototype.setAcsStartProtocolVersion=function(a){this.acsStartProtocolVersion=a};SupportedVersionsResponse.prototype.getAcsEndProtocolVersion=function(){return this.acsEndProtocolVersion};SupportedVersionsResponse.prototype.setAcsEndProtocolVersion=function(a){this.acsEndProtocolVersion=a};SupportedVersionsResponse.prototype.getThreeDSMethodURL=function(){return this.threeDSMethodURL};
SupportedVersionsResponse.prototype.setThreeDSMethodURL=function(a){this.threeDSMethodURL=a};SupportedVersionsResponse.prototype.getDsStartProtocolVersion=function(){return this.dsStartProtocolVersion};SupportedVersionsResponse.prototype.setDsStartProtocolVersion=function(a){this.dsStartProtocolVersion=a};SupportedVersionsResponse.prototype.getDsEndProtocolVersion=function(){return this.dsEndProtocolVersion};
SupportedVersionsResponse.prototype.setDsEndProtocolVersion=function(a){this.dsEndProtocolVersion=a};SupportedVersionsResponse.prototype.getMessageExtension=function(){null===ObjectUtils.normalize(this.messageExtension)&&(this.messageExtension=[]);return this.messageExtension};SupportedVersionsResponse.prototype.setMessageExtension=function(a){this.messageExtension=a};SupportedVersionsResponse.prototype.addMessageExtension=function(a){this.getMessageExtension().push(a)};
SupportedVersionsResponse.prototype.getACSVersion=function(){return this.aCSVersion};SupportedVersionsResponse.prototype.setACSVersion=function(a){this.aCSVersion=a};SupportedVersionsResponse.prototype.getDsIdentifier=function(){return this.dsIdentifier};SupportedVersionsResponse.prototype.setDsIdentifier=function(a){this.dsIdentifier=a};SupportedVersionsResponse.fromJson=function(a){var b=new SupportedVersionsResponse;JsonSerializer.readJsonIntoInstance(b,a);return b};
SupportedVersionsResponse.fromMap=function(a){var b=new SupportedVersionsResponse;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};SupportedVersionsResponse.FieldMetadata={};for(key in APIResponse.FieldMetadata)APIResponse.FieldMetadata.hasOwnProperty(key)&&(SupportedVersionsResponse.FieldMetadata[key]=APIResponse.FieldMetadata[key]);
SupportedVersionsResponse.FieldMetadata.threeDSServerStartVersion=(new FieldMetadata).setValueRetriever(function(a){return a instanceof SupportedVersionsResponse?a.getThreeDSServerStartVersion():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof SupportedVersionsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ApiVersionType,"ApiVersionType")&&(a.setThreeDSServerStartVersion(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return ApiVersionType.fromObject(a)}).enableSerialization().setFieldType(Init.Type.ApiVersionType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
SupportedVersionsResponse.FieldMetadata.threeDSServerEndVersion=(new FieldMetadata).setValueRetriever(function(a){return a instanceof SupportedVersionsResponse?a.getThreeDSServerEndVersion():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof SupportedVersionsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ApiVersionType,"ApiVersionType")&&(a.setThreeDSServerEndVersion(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return ApiVersionType.fromObject(a)}).enableSerialization().setFieldType(Init.Type.ApiVersionType).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
SupportedVersionsResponse.FieldMetadata.threeDSServerTransID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof SupportedVersionsResponse?a.getThreeDSServerTransID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof SupportedVersionsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,UUID,"UUID")&&(a.setThreeDSServerTransID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=
new UUID;b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.UUID).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.ALLOW_EMPTY,!1).build());
SupportedVersionsResponse.FieldMetadata.acsStartProtocolVersion=(new FieldMetadata).setValueRetriever(function(a){return a instanceof SupportedVersionsResponse?a.getAcsStartProtocolVersion():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof SupportedVersionsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setAcsStartProtocolVersion(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,8).addProperty(Property.MIN_LENGTH,5).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
SupportedVersionsResponse.FieldMetadata.acsEndProtocolVersion=(new FieldMetadata).setValueRetriever(function(a){return a instanceof SupportedVersionsResponse?a.getAcsEndProtocolVersion():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof SupportedVersionsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setAcsEndProtocolVersion(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,8).addProperty(Property.MIN_LENGTH,5).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
SupportedVersionsResponse.FieldMetadata.threeDSMethodURL=(new FieldMetadata).setValueRetriever(function(a){return a instanceof SupportedVersionsResponse?a.getThreeDSMethodURL():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof SupportedVersionsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,URL,"URL")&&(a.setThreeDSMethodURL(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){var b=new URL;
b.setRawValue(a);return b}).enableSerialization().setFieldType(Init.Type.URL).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
SupportedVersionsResponse.FieldMetadata.dsStartProtocolVersion=(new FieldMetadata).setValueRetriever(function(a){return a instanceof SupportedVersionsResponse?a.getDsStartProtocolVersion():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof SupportedVersionsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setDsStartProtocolVersion(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,8).addProperty(Property.MIN_LENGTH,5).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
SupportedVersionsResponse.FieldMetadata.dsEndProtocolVersion=(new FieldMetadata).setValueRetriever(function(a){return a instanceof SupportedVersionsResponse?a.getDsEndProtocolVersion():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof SupportedVersionsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setDsEndProtocolVersion(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MAX_LENGTH,8).addProperty(Property.MIN_LENGTH,5).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
SupportedVersionsResponse.FieldMetadata.messageExtension=(new FieldMetadata).setValueRetriever(function(a){return a instanceof SupportedVersionsResponse?a.getMessageExtension():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof SupportedVersionsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,MessageExtensionAttributes,"MessageExtensionAttributes")&&(a.addMessageExtension(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(){return new MessageExtensionAttributes}).enableDcoSync().enableSerialization().setFieldType(Init.Type.MessageExtensionAttributes).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.ARRAY).addContext(FieldMetadata.ValidatorContext.THIS).addProperty(Property.MAX_LENGTH,
10).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.ALLOW_EMPTY,!1).build());
SupportedVersionsResponse.FieldMetadata.ACSVersion=(new FieldMetadata).setValueRetriever(function(a){return a instanceof SupportedVersionsResponse?a.getACSVersion():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof SupportedVersionsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,void 0,"Double")&&(a.setACSVersion(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
void 0,"Double")?a:null}).enableSerialization().setFieldType(Init.Type.Double).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
SupportedVersionsResponse.FieldMetadata.dsIdentifier=(new FieldMetadata).setValueRetriever(function(a){return a instanceof SupportedVersionsResponse?a.getDsIdentifier():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof SupportedVersionsResponse&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setDsIdentifier(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());Phone.prototype=NDObject.create(CustomConvertible.prototype);Phone.prototype.validate=function(){return new ValidationResult};Phone.prototype.setDefaultRegion=function(a){this.mDefaultRegion=a};Phone.prototype.setPhoneNumber=function(a){this.setRawValue(a)};
Phone.prototype.getPhoneNumber=function(){return this.getValue()};Phone.prototype.setRawValue=function(a){CustomConvertible.prototype.setRawValue.call(this,a);this.setValue(a)};CurrencyCode.prototype=NDObject.create(CustomConvertible.prototype);CurrencyCode.prototype.getCurrencyCode=function(){return this.getValue()};CurrencyCode.prototype.setCurrencyCode=function(a){this.setValue(a)};CurrencyCode.prototype.validate=function(){return new ValidationResult};
CurrencyCode.prototype.setRawValue=function(a){CustomConvertible.prototype.setRawValue.call(this,a);this.setValue(TextUtils.stringify(a))};ThreeDSDate.prototype=NDObject.create(CustomConvertible.prototype);ThreeDSDate.prototype.getDate=function(){return this.getValueForSerialization()};ThreeDSDate.prototype.setDate=function(a){this.setRawValue(a)};
ThreeDSDate.prototype.validate=function(a,b){this.mValidationOptions={};if(null!==ObjectUtils.normalize(b))for(var c in b)b.hasOwnProperty(c)&&(this.mValidationOptions[c]=b[c]);c=new ValidationResult.Filter;var d=this.getRawValue(),e=DateUtils.isDate(d)?d:null;if(null!==e)return c.setFilteredValue(e),c;d=TextUtils.stringify(d);if(TextUtils.isEmpty(d))c.fail(a,this.mValidationOptions,[Property.ALLOW_EMPTY]);else{e=TextUtils.stringify(this.mValidationOptions[Property.FORMAT.toString()]);e=this.getValidFormats(e);
var f=null,g=!1,k;for(k in e)if(e.hasOwnProperty(k)){var h=e[k];try{f=DateUtils.stringToDate(d,h)}catch(m){f=null}if(null!==f){c.setFilteredValue(f);g=!0;break}}g||c.fail(a,this.mValidationOptions,[Property.FORMAT])}return c};
ThreeDSDate.prototype.getValueForSerialization=function(){var a=this.getValue();if(null===ObjectUtils.normalize(a))return CustomConvertible.prototype.getValueForSerialization.call(this);var b=this.normalizeFormat(Property.FORMAT in this.mValidationOptions?TextUtils.stringify(this.mValidationOptions[Property.FORMAT.toString()]):null);return DateUtils.dateToString(a,b)};
ThreeDSDate.prototype.setRawValue=function(a){CustomConvertible.prototype.setRawValue.call(this,a);a=this.validate(null,this.mValidationOptions);this.setValue(a.isSuccess()?a.getFilteredValue():null)};
ThreeDSDate.prototype.equals=function(a){if(this===a)return!0;if(null===a||!(a instanceof Date))return!1;var b=a.getValue(),c=this.getValue();if(null===ObjectUtils.normalize(c)||null===ObjectUtils.normalize(b)){a=a.getValueForSerialization();var d=this.getValueForSerialization();return null===a||null===d?c===b:d.equals(a)}return c.getTime()===b.getTime()};
ThreeDSDate.prototype.normalizeFormat=function(a){switch(null==a?"":a.toLowerCase()){case "yymm":a="yyMM";break;case "yyyymmdd":a="yyyyMMdd";break;case "yyyymmddhhmm":a="yyyyMMddHHmm";break;case "yyyymmddhhmmss":a="yyyyMMddHHmmss";break;default:a="yyyyMMdd"}return a};ThreeDSDate.prototype.getValidFormats=function(a){var b=[];null===ObjectUtils.normalize(a)?(b.push("yyyyMMddHHmmss"),b.push("yyyyMMddHHmm"),b.push("yyyyMMdd"),b.push("yyMM")):b.push(this.normalizeFormat(a));return b};
ThreeDSDate.prototype.getAllowedProperties=function(){return[Property.FORMAT]};function HTTPLimitedTimingData(){Convertible.call(this);Interface.implement(this,ISyncable)}CountryCode.prototype=NDObject.create(CustomConvertible.prototype);CountryCode.prototype.getCountryCode=function(){return this.getValue()};CountryCode.prototype.setCountryCode=function(a){this.setValue(a)};CountryCode.prototype.validate=function(){return new ValidationResult};
CountryCode.prototype.setRawValue=function(a){CustomConvertible.prototype.setRawValue.call(this,a);this.setValue(TextUtils.stringify(a))};WidgetOutcomeMessage.prototype=NDObject.create(Convertible.prototype);WidgetOutcomeMessage.prototype.getFieldMetadata=function(){return WidgetOutcomeMessage.FieldMetadata};function Base64(a){CustomConvertible.call(this,a||null);this.setRawValue(a)}function ErrorComponent(a,b){Enumerable.call(this,a,ErrorComponent.VALUES,b);b&&(ErrorComponent.VALUES[a]=this)}
WidgetOutcomeMessage.prototype.getType=function(){return this.type};WidgetOutcomeMessage.prototype.setType=function(a){this.type=a};WidgetOutcomeMessage.prototype.getTransStatus=function(){return this.transStatus};WidgetOutcomeMessage.prototype.setTransStatus=function(a){this.transStatus=a};WidgetOutcomeMessage.prototype.getError=function(){return this.error};WidgetOutcomeMessage.prototype.setError=function(a){this.error=a};WidgetOutcomeMessage.prototype.getChallengeOutcome=function(){return this.challengeOutcome};
WidgetOutcomeMessage.prototype.setChallengeOutcome=function(a){this.challengeOutcome=a};function WhitelistStatusSource(a,b){Enumerable.call(this,a,WhitelistStatusSource.VALUES,b);b&&(WhitelistStatusSource.VALUES[a]=this)}WidgetOutcomeMessage.prototype.getResultsResponse=function(){return this.resultsResponse};WidgetOutcomeMessage.prototype.setResultsResponse=function(a){this.resultsResponse=a};
WidgetOutcomeMessage.fromJson=function(a){var b=new WidgetOutcomeMessage;JsonSerializer.readJsonIntoInstance(b,a);return b};WidgetOutcomeMessage.fromMap=function(a){var b=new WidgetOutcomeMessage;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};WidgetOutcomeMessage.FieldMetadata={};for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(WidgetOutcomeMessage.FieldMetadata[key]=Convertible.FieldMetadata[key]);
WidgetOutcomeMessage.FieldMetadata.type=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetOutcomeMessage?a.getType():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetOutcomeMessage&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setType(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,
1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetOutcomeMessage.FieldMetadata.transStatus=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetOutcomeMessage?a.getTransStatus():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetOutcomeMessage&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,TransactionStatus,"TransactionStatus")&&(a.setTransStatus(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return TransactionStatus.fromObject(a)}).enableSerialization().setFieldType(Init.Type.TransactionStatus).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetOutcomeMessage.FieldMetadata.error=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetOutcomeMessage?a.getError():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetOutcomeMessage&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ThreeDSError,"ThreeDSError")&&(a.setError(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(){return new ThreeDSError}).enableDcoSync().enableSerialization().setFieldType(Init.Type.ThreeDSError).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetOutcomeMessage.FieldMetadata.challengeOutcome=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetOutcomeMessage?a.getChallengeOutcome():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetOutcomeMessage&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ChallengeOutcome,"ChallengeOutcome")&&(a.setChallengeOutcome(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(){return new ChallengeOutcome}).enableDcoSync().enableSerialization().setFieldType(Init.Type.ChallengeOutcome).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
WidgetOutcomeMessage.FieldMetadata.resultsResponse=(new FieldMetadata).setValueRetriever(function(a){return a instanceof WidgetOutcomeMessage?a.getResultsResponse():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof WidgetOutcomeMessage&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,ResultsResponse,"ResultsResponse")&&(a.setResultsResponse(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(){return new ResultsResponse}).enableDcoSync().enableSerialization().setFieldType(Init.Type.ResultsResponse).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
EMVProtocolErrorEvent.prototype=NDObject.create(Convertible.prototype);function ChallengeResponse(){ChallengeOutcome.call(this);Interface.implement(this,ISyncable)}EMVProtocolErrorEvent.prototype.getFieldMetadata=function(){return EMVProtocolErrorEvent.FieldMetadata};EMVProtocolErrorEvent.prototype.getErrorMessage=function(){return this.errorMessage};EMVProtocolErrorEvent.prototype.setErrorMessage=function(a){this.errorMessage=a};EMVProtocolErrorEvent.prototype.getSDKTransactionID=function(){return this.sDKTransactionID};
EMVProtocolErrorEvent.prototype.setSDKTransactionID=function(a){this.sDKTransactionID=a};EMVProtocolErrorEvent.fromJson=function(a){var b=new EMVProtocolErrorEvent;JsonSerializer.readJsonIntoInstance(b,a);return b};EMVProtocolErrorEvent.fromMap=function(a){var b=new EMVProtocolErrorEvent;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};EMVProtocolErrorEvent.FieldMetadata={};
for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(EMVProtocolErrorEvent.FieldMetadata[key]=Convertible.FieldMetadata[key]);
EMVProtocolErrorEvent.FieldMetadata.errorMessage=(new FieldMetadata).setValueRetriever(function(a){return a instanceof EMVProtocolErrorEvent?a.getErrorMessage():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof EMVProtocolErrorEvent&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,EMVErrorMessage,"EMVErrorMessage")&&(a.setErrorMessage(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(){return new EMVErrorMessage}).enableDcoSync().enableSerialization().setFieldType(Init.Type.EMVErrorMessage).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
EMVProtocolErrorEvent.FieldMetadata.SDKTransactionID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof EMVProtocolErrorEvent?a.getSDKTransactionID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof EMVProtocolErrorEvent&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setSDKTransactionID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());EMVCompletionEvent.prototype=NDObject.create(Convertible.prototype);
EMVCompletionEvent.prototype.getFieldMetadata=function(){return EMVCompletionEvent.FieldMetadata};EMVCompletionEvent.prototype.getSdkTransactionID=function(){return this.sdkTransactionID};EMVCompletionEvent.prototype.setSdkTransactionID=function(a){this.sdkTransactionID=a};EMVCompletionEvent.prototype.getTransactionStatus=function(){return this.transactionStatus};function ACSInterface(a,b){Enumerable.call(this,a,ACSInterface.VALUES,b);b&&(ACSInterface.VALUES[a]=this)}
EMVCompletionEvent.prototype.setTransactionStatus=function(a){this.transactionStatus=a};EMVCompletionEvent.fromJson=function(a){var b=new EMVCompletionEvent;JsonSerializer.readJsonIntoInstance(b,a);return b};EMVCompletionEvent.fromMap=function(a){var b=new EMVCompletionEvent;(new JsonSerializer).readMapIntoInstance(b,a,a);return b};EMVCompletionEvent.FieldMetadata={};for(key in Convertible.FieldMetadata)Convertible.FieldMetadata.hasOwnProperty(key)&&(EMVCompletionEvent.FieldMetadata[key]=Convertible.FieldMetadata[key]);
EMVCompletionEvent.FieldMetadata.sdkTransactionID=(new FieldMetadata).setValueRetriever(function(a){return a instanceof EMVCompletionEvent?a.getSdkTransactionID():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof EMVCompletionEvent&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setSdkTransactionID(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());
EMVCompletionEvent.FieldMetadata.transactionStatus=(new FieldMetadata).setValueRetriever(function(a){return a instanceof EMVCompletionEvent?a.getTransactionStatus():null}).setValueSetter(function(a,b){var c=FieldMetadata.SetterResult.NOT_APPLICABLE;a instanceof EMVCompletionEvent&&(c=FieldMetadata.SetterResult.FAILURE,MetadataUtils.isInstanceOf(b,String,"String")&&(a.setTransactionStatus(b),c=FieldMetadata.SetterResult.SUCCESS));return c}).setValueInitializer(function(a){return MetadataUtils.isInstanceOf(a,
String,"String")?a:null}).enableSerialization().setFieldType(Init.Type.String).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.STRING).addContext(FieldMetadata.ValidatorContext.T).addProperty(Property.MIN_LENGTH,1).build()).addValidator((new FieldMetadata.ValidatorMetadata.Builder).setDelegate(FieldMetadata.ValidationDelegate.OBJECT).addContext(FieldMetadata.ValidatorContext.THIS).build());Email.prototype=NDObject.create(CustomConvertible.prototype);
Email.prototype.getEmail=function(){return this.getValue()};Email.prototype.setEmail=function(a){this.setRawValue(a)};Email.prototype.validate=function(){return new ValidationResult};Email.prototype.setRawValue=function(a){CustomConvertible.prototype.setRawValue.call(this,a);this.setValue(TextUtils.stringify(a))};Email.prototype.getAllowedProperties=function(){return StringValidator.ALLOWED_PROPERTIES};IP.prototype=NDObject.create(CustomConvertible.prototype);IP.prototype.getIP=function(){return this.getValue()};
IP.prototype.setIP=function(a){this.setValue(a)};IP.prototype.validate=function(){return new ValidationResult};IP.prototype.setRawValue=function(a){CustomConvertible.prototype.setRawValue.call(this,a);this.setValue(TextUtils.stringify(a))};IP.prototype.getAllowedProperties=function(){return StringValidator.ALLOWED_PROPERTIES};
function ThreeDSError(){Convertible.call(this);this.sdkTransID=this.messageVersion=this.messageType=this.errorMessageType=this.errorDetail=this.errorDescription=this.errorComponent=this.errorCode=this.dsTransID=this.acsTransID=this.threeDSServerTransID=null;Interface.implement(this,ISyncable)}
"undefined"!==typeof exports&&(exports.ACSInterface=ACSInterface,exports.ACSRenderingType=ACSRenderingType,exports.ACSUITemplate=ACSUITemplate,exports.APIResponse=APIResponse,exports.Action=Action,exports.ApiVersionType=ApiVersionType,exports.ArrayContainerValidator=ArrayContainerValidator,exports.AuthenticationRequestWebParameters=AuthenticationRequestWebParameters,exports.AuthenticationResponse=AuthenticationResponse,exports.AuthenticationType=AuthenticationType,exports.Base64=Base64,exports.Base64URL=
Base64URL,exports.BaseContainerValidator=BaseContainerValidator,exports.BaseException=BaseException,exports.BaseValidator=BaseValidator,exports.BooleanYesNo=BooleanYesNo,exports.BrowserColorDepthType=BrowserColorDepthType,exports.CancelIndicator=CancelIndicator,exports.Challenge=Challenge,exports.ChallengeOutcome=ChallengeOutcome,exports.ChallengeResponse=ChallengeResponse,exports.Convertible=Convertible,exports.CountryCode=CountryCode,exports.CryptoType=CryptoType,exports.CurrencyCode=CurrencyCode,
exports.CustomConvertible=CustomConvertible,exports.DateUtils=DateUtils,exports.DoubleValidator=DoubleValidator,exports.EMVCompletionEvent=EMVCompletionEvent,exports.EMVErrorMessage=EMVErrorMessage,exports.EMVProtocolErrorEvent=EMVProtocolErrorEvent,exports.EMVRuntimeErrorEvent=EMVRuntimeErrorEvent,exports.Email=Email,exports.Enumerable=Enumerable,exports.ErrorCode=ErrorCode,exports.ErrorComponent=ErrorComponent,exports.FieldMetadata=FieldMetadata,exports.HTTPLimitedTimingData=HTTPLimitedTimingData,
exports.ICopyable=ICopyable,exports.ICustomConvertible=ICustomConvertible,exports.IMetadataProvider=IMetadataProvider,exports.INonStringable=INonStringable,exports.IP=IP,exports.ISyncable=ISyncable,exports.IValidatable=IValidatable,exports.IllegalArgumentException=IllegalArgumentException,exports.ImageSizes=ImageSizes,exports.Init=Init,exports.IntValidator=IntValidator,exports.Interface=Interface,exports.InvalidValueException=InvalidValueException,exports.JsonDecodeException=JsonDecodeException,exports.JsonSerializer=
JsonSerializer,exports.MapContainerValidator=MapContainerValidator,exports.MathUtil=MathUtil,exports.MessageCategoryType=MessageCategoryType,exports.MessageExtensionAttributes=MessageExtensionAttributes,exports.MessageVersionType=MessageVersionType,exports.MetadataUtils=MetadataUtils,exports.MsgType=MsgType,exports.NDObject=NDObject,exports.NumericEnumerable=NumericEnumerable,exports.ObjectContainerValidator=ObjectContainerValidator,exports.ObjectUtils=ObjectUtils,exports.ObjectValidator=ObjectValidator,
exports.Phone=Phone,exports.Property=Property,exports.ResultsResponse=ResultsResponse,exports.SecureString=SecureString,exports.StatusMsg=StatusMsg,exports.StringValidator=StringValidator,exports.SupportedVersionsResponse=SupportedVersionsResponse,exports.TernaryYesNoUnavailable=TernaryYesNoUnavailable,exports.TextUtils=TextUtils,exports.ThreeDSDate=ThreeDSDate,exports.ThreeDSError=ThreeDSError,exports.TransactionReason=TransactionReason,exports.TransactionStatus=TransactionStatus,exports.URL=URL,
exports.UUID=UUID,exports.ValidationException=ValidationException,exports.ValidationResult=ValidationResult,exports.Validator=Validator,exports.WhitelistStatus=WhitelistStatus,exports.WhitelistStatusSource=WhitelistStatusSource,exports.WidgetBrowserData=WidgetBrowserData,exports.WidgetOutcomeMessage=WidgetOutcomeMessage,exports.WindowSize=WindowSize);
"undefined"!==typeof module&&"undefined"!==typeof module.exports&&(module.exports.ACSInterface=ACSInterface,module.exports.ACSRenderingType=ACSRenderingType,module.exports.ACSUITemplate=ACSUITemplate,module.exports.APIResponse=APIResponse,module.exports.Action=Action,module.exports.ApiVersionType=ApiVersionType,module.exports.ArrayContainerValidator=ArrayContainerValidator,module.exports.AuthenticationRequestWebParameters=AuthenticationRequestWebParameters,module.exports.AuthenticationResponse=AuthenticationResponse,
module.exports.AuthenticationType=AuthenticationType,module.exports.Base64=Base64,module.exports.Base64URL=Base64URL,module.exports.BaseContainerValidator=BaseContainerValidator,module.exports.BaseException=BaseException,module.exports.BaseValidator=BaseValidator,module.exports.BooleanYesNo=BooleanYesNo,module.exports.BrowserColorDepthType=BrowserColorDepthType,module.exports.CancelIndicator=CancelIndicator,module.exports.Challenge=Challenge,module.exports.ChallengeOutcome=ChallengeOutcome,module.exports.ChallengeResponse=
ChallengeResponse,module.exports.Convertible=Convertible,module.exports.CountryCode=CountryCode,module.exports.CryptoType=CryptoType,module.exports.CurrencyCode=CurrencyCode,module.exports.CustomConvertible=CustomConvertible,module.exports.DateUtils=DateUtils,module.exports.DoubleValidator=DoubleValidator,module.exports.EMVCompletionEvent=EMVCompletionEvent,module.exports.EMVErrorMessage=EMVErrorMessage,module.exports.EMVProtocolErrorEvent=EMVProtocolErrorEvent,module.exports.EMVRuntimeErrorEvent=
EMVRuntimeErrorEvent,module.exports.Email=Email,module.exports.Enumerable=Enumerable,module.exports.ErrorCode=ErrorCode,module.exports.ErrorComponent=ErrorComponent,module.exports.FieldMetadata=FieldMetadata,module.exports.HTTPLimitedTimingData=HTTPLimitedTimingData,module.exports.ICopyable=ICopyable,module.exports.ICustomConvertible=ICustomConvertible,module.exports.IMetadataProvider=IMetadataProvider,module.exports.INonStringable=INonStringable,module.exports.IP=IP,module.exports.ISyncable=ISyncable,
module.exports.IValidatable=IValidatable,module.exports.IllegalArgumentException=IllegalArgumentException,module.exports.ImageSizes=ImageSizes,module.exports.Init=Init,module.exports.IntValidator=IntValidator,module.exports.Interface=Interface,module.exports.InvalidValueException=InvalidValueException,module.exports.JsonDecodeException=JsonDecodeException,module.exports.JsonSerializer=JsonSerializer,module.exports.MapContainerValidator=MapContainerValidator,module.exports.MathUtil=MathUtil,module.exports.MessageCategoryType=
MessageCategoryType,module.exports.MessageExtensionAttributes=MessageExtensionAttributes,module.exports.MessageVersionType=MessageVersionType,module.exports.MetadataUtils=MetadataUtils,module.exports.MsgType=MsgType,module.exports.NDObject=NDObject,module.exports.NumericEnumerable=NumericEnumerable,module.exports.ObjectContainerValidator=ObjectContainerValidator,module.exports.ObjectUtils=ObjectUtils,module.exports.ObjectValidator=ObjectValidator,module.exports.Phone=Phone,module.exports.Property=
Property,module.exports.ResultsResponse=ResultsResponse,module.exports.SecureString=SecureString,module.exports.StatusMsg=StatusMsg,module.exports.StringValidator=StringValidator,module.exports.SupportedVersionsResponse=SupportedVersionsResponse,module.exports.TernaryYesNoUnavailable=TernaryYesNoUnavailable,module.exports.TextUtils=TextUtils,module.exports.ThreeDSDate=ThreeDSDate,module.exports.ThreeDSError=ThreeDSError,module.exports.TransactionReason=TransactionReason,module.exports.TransactionStatus=
TransactionStatus,module.exports.URL=URL,module.exports.UUID=UUID,module.exports.ValidationException=ValidationException,module.exports.ValidationResult=ValidationResult,module.exports.Validator=Validator,module.exports.WhitelistStatus=WhitelistStatus,module.exports.WhitelistStatusSource=WhitelistStatusSource,module.exports.WidgetBrowserData=WidgetBrowserData,module.exports.WidgetOutcomeMessage=WidgetOutcomeMessage,module.exports.WindowSize=WindowSize);INdsConnection.prototype.open=function(){};
INdsConnection.prototype.setRequestHeader=function(){};INdsConnection.prototype.setOnSuccess=function(a){this.mOnSuccess="function"===typeof a?a:function(){}};INdsConnection.prototype.setOnError=function(a){this.mOnError="function"===typeof a?a:function(){}};INdsConnection.prototype.send=function(){};function NumericEnumerable(a,b,c){Enumerable.call(this,a,b,NumericEnumerable.validateNumeric(a))}XMLHttpRequestConnection.prototype=NDObject.create(INdsConnection.prototype);
XMLHttpRequestConnection.prototype.open=function(a,b){this.mXhr.open(a,b)};XMLHttpRequestConnection.prototype.setRequestHeader=function(a,b){this.mXhr.setRequestHeader(a,b)};
XMLHttpRequestConnection.prototype.send=function(a){var b=this;this.mXhr.onload=function(){var a=b.mXhr.status;if(200===a){a=b.mXhr.getAllResponseHeaders().split("\r\n");for(var d={},e=0;e<a.length;e++){var f=a[e],g=f.indexOf(":");-1!==g&&(d[f.substr(0,g)]=f.substr(g+1).trim())}b.mOnSuccess(b.mXhr.responseText,d)}else 200!==a&&b.mOnError(a)};this.mXhr.withCredentials=this.withCredentials;this.mXhr.send(a)};ThreeDSResponse.prototype.setDecodedBody=function(a){this.decodedBody=a};
function AuthenticationRequestWebParameters(){Convertible.call(this);this.threeDSCompInd=this.threeDSServerTransID=this.browserData=null;Interface.implement(this,ISyncable)}ThreeDSResponse.prototype.getHeaders=function(){return this.headers};ThreeDSResponse.prototype.getDecodedBody=function(){return this.decodedBody||JSON.parse(this.encodedBody)};ThreeDSRequest.prototype.setHeaders=function(a){this.headers=a};ThreeDSRequest.prototype.getHeaders=function(){return this.headers};
ThreeDSRequest.prototype.setEncodedBody=function(a){this.encodedBody=a};ThreeDSRequest.prototype.getEncodedBody=function(){return this.encodedBody||JSON.stringify(this.bodyMap)};ThreeDSRequest.prototype.setWithCredentials=function(a){this.withCredentials=a};ThreeDSRequest.prototype.getWithCredentials=function(){return this.withCredentials};
(function(){function a(b,c,d){function e(g,h){if(!c[g]){if(!b[g]){var m="function"==typeof require&&require;if(!h&&m)return m(g,!0);if(f)return f(g,!0);m=Error("Cannot find module '"+g+"'");throw m.code="MODULE_NOT_FOUND",m;}m=c[g]={exports:{}};b[g][0].call(m.exports,function(a){return e(b[g][1][a]||a)},m,m.exports,a,b,c,d)}return c[g].exports}for(var f="function"==typeof require&&require,g=0;g<d.length;g++)e(d[g]);return e}return a})()({1:[function(a,b,c){(function(a,e){if("object"===typeof c&&"object"===
typeof b)b.exports=e();else if("function"===typeof define&&define.amd)define([],e);else{var d=e(),g;for(g in d)("object"===typeof c?c:a)[g]=d[g]}})(this,function(){return function(a){function b(d){if(c[d])return c[d].exports;var e=c[d]={exports:{},id:d,loaded:!1};a[d].call(e.exports,e,e.exports,b);e.loaded=!0;return e.exports}var c={};b.m=a;b.c=c;b.p="";return b(0)}([function(a,b,c){var d=c(1);a=c(5);b.ChallengeParameters=a.ChallengeParameters;var e=c(3);c=function(){function a(a,b){this.challengeIframeElement=
a;this.options=b;this.INTERNAL_IFRAME_ID="3DSv2-InternalIframeId";this.initializeLogger(b);e["default"].debug("ThreeDS2Service#constructor entered...");var c=document.createElement("iframe");c.style.display="none";c.id=this.INTERNAL_IFRAME_ID;c.name=this.INTERNAL_IFRAME_ID;document.body.appendChild(c);e["default"].debug("ThreeDS2Service#constructor exited...")}a.prototype.createTransaction=function(a){return new d.Transaction(a,this)};a.prototype.getBrowserData=function(a){var b={browserJavaEnabled:navigator.javaEnabled(),
browserLanguage:navigator.language,browserColorDepth:window.screen.colorDepth.toString(),browserScreenHeight:window.screen.height.toString(),browserScreenWidth:window.screen.width.toString(),browserTZ:(new Date).getTimezoneOffset().toString(),browserUserAgent:navigator.userAgent};return a(b)};a.prototype.initializeLogger=function(a){e["default"].setLevel(null!=a?a.logLevel:null)};return a}();b.ThreeDS2Service=c},function(a,b,c){var d=c(2),e=c(3);a=function(){function a(a,b){this.transactionId=a;this.service=
b;e["default"].debug("Transaction constructor called",a)}a.prototype.performThreeDsMethodRequest=function(a,b,c){var d=this;e["default"].debug("Transaction.performThreeDsMethodRequest method called",a,b);this.submitToIframe(this.service.INTERNAL_IFRAME_ID,a,{threeDSMethodData:btoa(JSON.stringify({threeDSMethodNotificationURL:b,threeDSServerTransID:this.transactionId}))});var n=function(a){d.isEventValid(a)&&(e["default"].debug("Transaction.performThreeDsMethodRequest: message received: "+JSON.stringify(a.data),
a),c(a.data),e["default"].debug("unsubscribing",n),window.removeEventListener("message",n))};window.addEventListener("message",n,!1)};a.prototype.doChallenge=function(a,b){var c=this;e["default"].debug("Transaction.doChallenge method called");var g=document.getElementById(this.service.challengeIframeElement.name),f=new d.ScreenLoader(g);this.submitToIframe(this.service.challengeIframeElement.name,a._acsUrl,{creq:a._initialChallengeRequest,threeDSServerTransID:this.transactionId});var m=setTimeout(function(){e["default"].debug("Resending creq...");
f.showLoadingScreen();c.submitToIframe(c.service.challengeIframeElement.name,a._acsUrl,{creq:a._initialChallengeRequest,threeDSServerTransID:c.transactionId})},5E3),h=setTimeout(function(){e["default"].debug("Flow finished with error by timeout");f.removeLoadingScreen();b({outcome:"erred",error:{errorCode:"402",errorDescription:"Timeout expiry reached for the transaction",errorDetail:"Message not received in allotted time",sdkTransID:c.transactionId,messageVersion:"2.0.1",messageType:"Erro",errorComponent:"C",
errorMessageType:"CReq"}});window.removeEventListener("message",r)},1E4);g.onload=function(){e["default"].debug("iframe onload event fired");clearTimeout(m);clearTimeout(h)};var r=function(a){c.isEventValid(a)&&(e["default"].debug("Transaction.doChallenge: message received: "+JSON.stringify(a.data),a),b(a.data),e["default"].debug("unsubscribing",r),window.removeEventListener("message",r))};window.addEventListener("message",r,!1)};a.prototype.submitToIframe=function(a,b,c){var d=document.createElement("form");
d.method="POST";d.action=b;d.target=a;for(var e in c)c.hasOwnProperty(e)&&d.appendChild(this.createHiddenInput(e,c[e]));document.body.appendChild(d);d.submit();document.body.removeChild(d)};a.prototype.createHiddenInput=function(a,b){var c=document.createElement("input");c.type="hidden";c.name=a;c.value=b;return c};a.prototype.isEventValid=function(b){return b.origin===this.service.options.requestorBackendUrl&&b.data&&b.data.type&&b.data.type===a.CALLBACK_MESSAGE_TYPE};return a}();a.CALLBACK_MESSAGE_TYPE=
"mSIGNIA-3DSv2-TransactionMessage";b.Transaction=a},function(a,b){var c=function(){function a(a){this.iframe=a;this.iframeWindow=a.contentWindow||a.contentDocument;this.iframeWindow.document.head.appendChild(this.createStyles());this.createLoadingScreen()}a.prototype.showLoadingScreen=function(){this.iframeWindow.document.body.appendChild(this.loadingScreen)};a.prototype.removeLoadingScreen=function(){this.iframeWindow.document.body.removeChild(this.loadingScreen)};a.prototype.createLoadingScreen=
function(){this.loadingScreen=document.createElement("div");var a=document.createElement("div");a.innerText="Message has not received in allotted time. Resending request...";a.className="message";var b=document.createElement("div");b.className="spinner";this.loadingScreen.appendChild(a);this.loadingScreen.appendChild(b)};a.prototype.createStyles=function(){var a=document.createElement("style");a.type="text/css";a.innerHTML="\n            .message {\n                text-align: center;\n                margin-top: 2em;\n                font-size: 1.3em;\n            }\n\n            .spinner {\n                border: 0.5em solid #f3f3f3;\n                border-top: 0.5em solid #3498db;\n                position: fixed;\n                top: 50%;\n                left: 50%;\n                border-radius: 50%;\n                width: 4em;\n                height: 4em;\n                margin-top: -2em;\n                margin-left: -2em;\n                -webkit-animation: spin 2s linear infinite;\n                animation: spin 2s linear infinite;\n            }\n\n            @-webkit-keyframes spin {\n                0% { -webkit-transform: rotate(0deg); }\n                100% { -webkit-transform: rotate(360deg); }\n            }\n\n            @keyframes spin {\n                0% { transform: rotate(0deg); }\n                100% { transform: rotate(360deg); }\n            }";
return a};return a}();b.ScreenLoader=c},function(a,b,c){var d=c(4),e;(function(a){a[a.DEBUG=1]="DEBUG";a[a.INFO=2]="INFO";a[a.TIME=3]="TIME";a[a.WARN=4]="WARN";a[a.ERROR=8]="ERROR";a[a.OFF=99]="OFF"})(e||(e={}));a=function(){function a(){if(a.instance)return a.instance;a.instance=this;d.setHandler(d.createDefaultHandler())}a.initialize=function(){a.instance||(a.instance=new a);return a.instance};a.prototype.setLevel=function(b){b=null==b?a.defaultLogLevel:b;d.setLevel({value:e[b],name:b})};a.prototype.debug=
function(a){for(var b=[],c=1;c<arguments.length;c++)b[c-1]=arguments[c];this._log.apply(this,["debug",a].concat(b))};a.prototype.info=function(a){for(var b=[],c=1;c<arguments.length;c++)b[c-1]=arguments[c];this._log.apply(this,["info",a].concat(b))};a.prototype.log=function(a){for(var b=[],c=1;c<arguments.length;c++)b[c-1]=arguments[c];this._log.apply(this,["log",a].concat(b))};a.prototype.warn=function(a){for(var b=[],c=1;c<arguments.length;c++)b[c-1]=arguments[c];this._log.apply(this,["warn",a].concat(b))};
a.prototype.error=function(a){for(var b=[],c=1;c<arguments.length;c++)b[c-1]=arguments[c];this._log.apply(this,["error",a].concat(b))};a.prototype._log=function(a,b){for(var c=[],e=2;e<arguments.length;e++)c[e-2]=arguments[e];d[a].apply(d,[b].concat(c))};return a}();a.defaultLogLevel=e[e.OFF];b.__esModule=!0;b["default"]=a.initialize()},function(a,b,c){var d,e;(function(f){var g={VERSION:"1.4.1"},h,n={};f=function(a,b){return function(){return b.apply(a,arguments)}};var k=function(){var a=arguments,
b=a[0],c,d;for(d=1;d<a.length;d++)for(c in a[d])c in b||!a[d].hasOwnProperty(c)||(b[c]=a[d][c]);return b},q=function(a,b){return{value:a,name:b}};g.DEBUG=q(1,"DEBUG");g.INFO=q(2,"INFO");g.TIME=q(3,"TIME");g.WARN=q(4,"WARN");g.ERROR=q(8,"ERROR");g.OFF=q(99,"OFF");var u=function(a){this.context=a;this.setLevel(a.filterLevel);this.log=this.info};u.prototype={setLevel:function(a){a&&"value"in a&&(this.context.filterLevel=a)},getLevel:function(){return this.context.filterLevel},enabledFor:function(a){return a.value>=
this.context.filterLevel.value},debug:function(){this.invoke(g.DEBUG,arguments)},info:function(){this.invoke(g.INFO,arguments)},warn:function(){this.invoke(g.WARN,arguments)},error:function(){this.invoke(g.ERROR,arguments)},time:function(a){"string"===typeof a&&0<a.length&&this.invoke(g.TIME,[a,"start"])},timeEnd:function(a){"string"===typeof a&&0<a.length&&this.invoke(g.TIME,[a,"end"])},invoke:function(a,b){h&&this.enabledFor(a)&&h(b,k({level:a},this.context))}};var p=new u({filterLevel:g.OFF});
g.enabledFor=f(p,p.enabledFor);g.debug=f(p,p.debug);g.time=f(p,p.time);g.timeEnd=f(p,p.timeEnd);g.info=f(p,p.info);g.warn=f(p,p.warn);g.error=f(p,p.error);g.log=g.info;g.setHandler=function(a){h=a};g.setLevel=function(a){p.setLevel(a);for(var b in n)n.hasOwnProperty(b)&&n[b].setLevel(a)};g.getLevel=function(){return p.getLevel()};g.get=function(a){return n[a]||(n[a]=new u(k({name:a},p.context)))};g.createDefaultHandler=function(a){a=a||{};a.formatter=a.formatter||function(a,b){b.name&&a.unshift("["+
b.name+"]")};var b={};return"undefined"===typeof console?function(){}:function(c,d){c=Array.prototype.slice.call(c);var e=console.log;if(d.level===g.TIME){var f=(d.name?"["+d.name+"] ":"")+c[0];"start"===c[1]?console.time?console.time(f):b[f]=(new Date).getTime():console.timeEnd?console.timeEnd(f):(f=[f+": "+((new Date).getTime()-b[f])+"ms"],Function.prototype.apply.call(e,console,f))}else d.level===g.WARN&&console.warn?e=console.warn:d.level===g.ERROR&&console.error?e=console.error:d.level===g.INFO&&
console.info?e=console.info:d.level===g.DEBUG&&console.debug&&(e=console.debug),a.formatter(c,d),Function.prototype.apply.call(e,console,c)}};g.useDefaults=function(a){g.setLevel(a&&a.defaultLevel||g.DEBUG);g.setHandler(g.createDefaultHandler(a))};!(d=g,e="function"===typeof d?d.call(b,c,b,a):d,void 0!==e&&(a.exports=e))})(this)},function(a,b){var c=function(){return function(a,b,c){this._acsUrl=a;this._initialChallengeRequest=b;this._sessionData=c}}();b.ChallengeParameters=c}])})},{}],2:[function(a,
b,c){b="undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{};b.ThreeDS2Service=a("./3ds-v2-2.1.0-dev-preview-1.js").ThreeDS2Service;b.ChallengeParameters=a("./3ds-v2-2.1.0-dev-preview-1.js").ChallengeParameters},{"./3ds-v2-2.1.0-dev-preview-1.js":1}]},{},[2]);
ThreeDSWidget.prototype.start=function(a){function b(a){var b="",c;for(c in a)a.hasOwnProperty(c)&&(0<b.length&&(b+="\x26"),b+=encodeURI(c+"\x3d"+a[c]));return b}function c(){}function d(b,e,f,h){f="undefined"===typeof f?c:f;h="undefined"===typeof h?!1:h;try{var n=b.apply(null,e)}catch(r){h?d(c,[],f):d(a.callbacks.transaction.error,[g(r)],f,!0);return}"object"===typeof n&&"function"===typeof n.then&&"function"===typeof n.catch?n.then(f).catch(f):f()}function e(c,e){var f="undefined"!==typeof a.connection?
a.connection:new XMLHttpRequestConnection;f.open("POST",l.mHost+l.mAPI);f.setOnSuccess(function(b,c){var f=new ThreeDSResponse(b,c);d(a.callbacks.httpConnection.after,[f],function(){e(f.getDecodedBody())})});f.setOnError(function(b){a.callbacks.transaction.error(g(StatusMsg.ERROR_WITH_CODE.toString()+" "+b))});if(void 0===c.data||null===c.data)c.data={};var n=new ThreeDSRequest(c.data);n.headers["Content-Type"]="application/x-www-form-urlencoded";d(a.callbacks.httpConnection.before,[n],function(){c.data=
n.getEncodedBody();for(var a in n.headers)n.headers.hasOwnProperty(a)&&f.setRequestHeader(a,n.headers[a]);f.withCredentials=n.getWithCredentials();f.send(b(c))})}function f(a){return"undefined"!==typeof a?a:null}function g(a){var b=new ThreeDSError;b.setErrorCode(ErrorCode.TRANSIENT_SYSTEM_FAILURE);b.setErrorComponent(ErrorComponent.THREEDS_SDK);b.setMessageType(MsgType.ERRO);b.setErrorDescription(StatusMsg.WIDGET_ERROR.toString());b.setErrorDetail(f(a));b.setThreeDSServerTransID(f(l.threeDSServerTransID));
null!=l.authenticationResponse&&(b.setAcsTransID(f(l.authenticationResponse.getAcsTransID())),b.setDsTransID(f(l.authenticationResponse.getDsTransID())),b.setSdkTransID(f(l.authenticationResponse.getSdkTransID())),b.setMessageVersion(f(l.authenticationResponse.getMessageVersion())));return b}function k(a){return null!==a&&(a.getErrorDescription()||a.getErrorDetail())}function h(a){a="string"===typeof a?ThreeDSError.fromJson(a):ThreeDSError.fromMap(a);return k(a)?a:null}function m(a){function b(){var b=
WindowSize.WS600_400,c=[{value:WindowSize.WS250_400,width:250,height:400},{value:WindowSize.WS390_400,width:390,height:400},{value:WindowSize.WS500_600,width:500,height:600},{value:WindowSize.WS600_400,width:600,height:400}];if(null!==a.iframeElement&&"undefined"!==typeof a.iframeElement){var d=a.iframeElement.clientWidth||a.iframeElement.offsetWidth||0;var e=a.iframeElement.clientHeight||a.iframeElement.offsetHeight||0;if(0<d&&0<e){var f=c.length;if(d>c[f-1].width&&e>c[f-1].height)b=WindowSize.FULL_SCREEN;
else for(var g=0;g<f;g++){var h=c[g],k=h.height;if(d<=h.width&&e<=k){b=h.value;break}}}}return b}function c(b){if(l.authenticationResponse.getTransStatus()!==TransactionStatus.CHALLENGE_REQUIRED)b=new ChallengeResponse,JsonSerializer.readJsonIntoInstance(b,l.authenticationResponse.toJson()),d(a.callbacks.challenge.completed,[b],function(){d(a.callbacks.transaction.results,[ResultsResponse.fromJson(l.authenticationResponse.toJson())])});else{var c=null;l.authenticationResponse.getEncodedCReq()&&(c=
l.authenticationResponse.getEncodedCReq());var e=l.authenticationResponse.getAcsURL();e?(c=new ChallengeParameters(e.getValue(),c),a.iframeElement.style.visibility="visible",b.doChallenge(c,function(b){var c=WidgetOutcomeMessage.fromMap(b);b=c.getError();k(b)?a.callbacks.transaction.error(b):(b=new ChallengeResponse,JsonSerializer.readJsonIntoInstance(b,c.getChallengeOutcome().toJson()),d(a.callbacks.challenge.completed,[b],function(){d(a.callbacks.transaction.results,[ResultsResponse.fromJson(c.getResultsResponse().toJson())])}))})):
a.callbacks.transaction.error(g(StatusMsg.MISSING_ACS_URL.toString()))}}function f(f){v.getBrowserData(function(g){var k=AuthenticationRequestWebParameters.fromMap(g);k.getBrowserData().setChallengeWindowSize(b());k.setThreeDSServerTransID(l.threeDSServerTransID);k.setThreeDSCompInd(t);d(a.callbacks.authenticate.before,[k],function(){e({action:Action.CHALLENGE_REQUEST,data:k.toJson()},function(b){var e=h(b);e?a.callbacks.transaction.error(e):(l.authenticationResponse=AuthenticationResponse.fromMap(b),
d(a.callbacks.authenticate.after,[l.authenticationResponse],function(){c(f)}))})})})}function n(b,c){d(a.callbacks.threeDSMethod.after,[c],function(){c.getTransStatus()===TransactionStatus.SUCCESS&&f(b)})}function m(b){l.threeDSServerTransID=b.getThreeDSServerTransID();b=b.getThreeDSMethodURL();var c=v.createTransaction(null===l.threeDSServerTransID?null:l.threeDSServerTransID.getValue());b?(t=TernaryYesNoUnavailable.NO,c.performThreeDsMethodRequest(b,l.mHost+l.mAPI,function(b){t=TernaryYesNoUnavailable.YES;
b=WidgetOutcomeMessage.fromMap(b);var e=b.getError();k(e)?d(a.callbacks.threeDSMethod.after,[b],function(){a.callbacks.transaction.error(e)}):n(c,b);t=TernaryYesNoUnavailable.UNAVAILABLE})):(b=new WidgetOutcomeMessage,b.setTransStatus(TransactionStatus.SUCCESS),n(c,b))}var v=new ThreeDS2Service(a.iframeElement,{requestorBackendUrl:l.mHost,logLevel:"WARN"}),t=TernaryYesNoUnavailable.UNAVAILABLE;d(a.callbacks.supportedVersions.before,[],function(){e({action:Action.INIT,data:{}},function(b){var c=h(b);
if(c)a.callbacks.transaction.error(c);else{var e=SupportedVersionsResponse.fromMap(b);d(a.callbacks.supportedVersions.after,[e],function(){m(e)})}})})}var l=this;(function(a){function b(b,c){for(var d=b.split("."),e=d.length,f=a.callbacks,g=0;g<e;g++){var h=d[g];"undefined"===typeof f[h]&&(f[h]=g==e-1?c:{});f=f[h]}}"undefined"===typeof a.callbacks&&(a.callbacks={});(function(){var c={initCompleted:{mapTo:"supportedVersions.after",fn:a.callbacks.initCompleted},configureCompleted:{mapTo:"threeDSMethod.after",
fn:function(b){b=b.getTransStatus()===TransactionStatus.SUCCESS;return a.callbacks.configureCompleted(b)}},challengeRequested:{mapTo:"authenticate.after",fn:function(b){b=TransactionStatus.fromObject(f(b.getTransStatus()));return a.callbacks.challengeRequested(b)}},performChallengeCompleted:{mapTo:"challenge.completed",fn:function(b){return a.callbacks.performChallengeCompleted(b.getTransStatus())}},error:{mapTo:"transaction.error",fn:a.callbacks.error},onRequest:{mapTo:"httpConnection.before",fn:a.callbacks.onRequest},
onResponse:{mapTo:"httpConnection.after",fn:a.callbacks.onResponse}},d;for(d in c)if(c.hasOwnProperty(d)&&"function"===typeof a.callbacks[d]){var e=c[d];b(e.mapTo,e.fn)}})();for(var c="supportedVersions.before supportedVersions.after threeDSMethod.after authenticate.before authenticate.after challenge.completed transaction.error httpConnection.before httpConnection.after".split(" "),d=0;d<c.length;d++)b(c[d],function(){})})(a);try{m(a)}catch(n){a.callbacks.transaction.error(g(n.toString()))}};
"undefined"!==typeof exports&&(exports.ACSInterface=ACSInterface,exports.ACSRenderingType=ACSRenderingType,exports.ACSUITemplate=ACSUITemplate,exports.APIResponse=APIResponse,exports.Action=Action,exports.ApiVersionType=ApiVersionType,exports.ArrayContainerValidator=ArrayContainerValidator,exports.AuthenticationRequestWebParameters=AuthenticationRequestWebParameters,exports.AuthenticationResponse=AuthenticationResponse,exports.AuthenticationType=AuthenticationType,exports.Base64=Base64,exports.Base64URL=
Base64URL,exports.BaseContainerValidator=BaseContainerValidator,exports.BaseException=BaseException,exports.BaseValidator=BaseValidator,exports.BooleanYesNo=BooleanYesNo,exports.BrowserColorDepthType=BrowserColorDepthType,exports.CancelIndicator=CancelIndicator,exports.Challenge=Challenge,exports.ChallengeOutcome=ChallengeOutcome,exports.ChallengeParameters=ChallengeParameters,exports.ChallengeResponse=ChallengeResponse,exports.Convertible=Convertible,exports.CountryCode=CountryCode,exports.CryptoType=
CryptoType,exports.CurrencyCode=CurrencyCode,exports.CustomConvertible=CustomConvertible,exports.DateUtils=DateUtils,exports.DoubleValidator=DoubleValidator,exports.EMVCompletionEvent=EMVCompletionEvent,exports.EMVErrorMessage=EMVErrorMessage,exports.EMVProtocolErrorEvent=EMVProtocolErrorEvent,exports.EMVRuntimeErrorEvent=EMVRuntimeErrorEvent,exports.Email=Email,exports.Enumerable=Enumerable,exports.ErrorCode=ErrorCode,exports.ErrorComponent=ErrorComponent,exports.FieldMetadata=FieldMetadata,exports.HTTPLimitedTimingData=
HTTPLimitedTimingData,exports.ICopyable=ICopyable,exports.ICustomConvertible=ICustomConvertible,exports.IMetadataProvider=IMetadataProvider,exports.INdsConnection=INdsConnection,exports.INonStringable=INonStringable,exports.IP=IP,exports.ISyncable=ISyncable,exports.IValidatable=IValidatable,exports.IllegalArgumentException=IllegalArgumentException,exports.ImageSizes=ImageSizes,exports.Init=Init,exports.IntValidator=IntValidator,exports.Interface=Interface,exports.InvalidValueException=InvalidValueException,
exports.JsonDecodeException=JsonDecodeException,exports.JsonSerializer=JsonSerializer,exports.MapContainerValidator=MapContainerValidator,exports.MathUtil=MathUtil,exports.MessageCategoryType=MessageCategoryType,exports.MessageExtensionAttributes=MessageExtensionAttributes,exports.MessageVersionType=MessageVersionType,exports.MetadataUtils=MetadataUtils,exports.MsgType=MsgType,exports.NDObject=NDObject,exports.NumericEnumerable=NumericEnumerable,exports.ObjectContainerValidator=ObjectContainerValidator,
exports.ObjectUtils=ObjectUtils,exports.ObjectValidator=ObjectValidator,exports.Phone=Phone,exports.Property=Property,exports.ResultsResponse=ResultsResponse,exports.SecureString=SecureString,exports.StatusMsg=StatusMsg,exports.StringValidator=StringValidator,exports.SupportedVersionsResponse=SupportedVersionsResponse,exports.TernaryYesNoUnavailable=TernaryYesNoUnavailable,exports.TextUtils=TextUtils,exports.ThreeDS2Service=ThreeDS2Service,exports.ThreeDSDate=ThreeDSDate,exports.ThreeDSError=ThreeDSError,
exports.ThreeDSRequest=ThreeDSRequest,exports.ThreeDSResponse=ThreeDSResponse,exports.ThreeDSWidget=ThreeDSWidget,exports.TransactionReason=TransactionReason,exports.TransactionStatus=TransactionStatus,exports.URL=URL,exports.UUID=UUID,exports.ValidationException=ValidationException,exports.ValidationResult=ValidationResult,exports.Validator=Validator,exports.WhitelistStatus=WhitelistStatus,exports.WhitelistStatusSource=WhitelistStatusSource,exports.WidgetBrowserData=WidgetBrowserData,exports.WidgetOutcomeMessage=
WidgetOutcomeMessage,exports.WindowSize=WindowSize,exports.XMLHttpRequestConnection=XMLHttpRequestConnection);
"undefined"!==typeof module&&"undefined"!==typeof module.exports&&(module.exports.ACSInterface=ACSInterface,module.exports.ACSRenderingType=ACSRenderingType,module.exports.ACSUITemplate=ACSUITemplate,module.exports.APIResponse=APIResponse,module.exports.Action=Action,module.exports.ApiVersionType=ApiVersionType,module.exports.ArrayContainerValidator=ArrayContainerValidator,module.exports.AuthenticationRequestWebParameters=AuthenticationRequestWebParameters,module.exports.AuthenticationResponse=AuthenticationResponse,
module.exports.AuthenticationType=AuthenticationType,module.exports.Base64=Base64,module.exports.Base64URL=Base64URL,module.exports.BaseContainerValidator=BaseContainerValidator,module.exports.BaseException=BaseException,module.exports.BaseValidator=BaseValidator,module.exports.BooleanYesNo=BooleanYesNo,module.exports.BrowserColorDepthType=BrowserColorDepthType,module.exports.CancelIndicator=CancelIndicator,module.exports.Challenge=Challenge,module.exports.ChallengeOutcome=ChallengeOutcome,module.exports.ChallengeParameters=
ChallengeParameters,module.exports.ChallengeResponse=ChallengeResponse,module.exports.Convertible=Convertible,module.exports.CountryCode=CountryCode,module.exports.CryptoType=CryptoType,module.exports.CurrencyCode=CurrencyCode,module.exports.CustomConvertible=CustomConvertible,module.exports.DateUtils=DateUtils,module.exports.DoubleValidator=DoubleValidator,module.exports.EMVCompletionEvent=EMVCompletionEvent,module.exports.EMVErrorMessage=EMVErrorMessage,module.exports.EMVProtocolErrorEvent=EMVProtocolErrorEvent,
module.exports.EMVRuntimeErrorEvent=EMVRuntimeErrorEvent,module.exports.Email=Email,module.exports.Enumerable=Enumerable,module.exports.ErrorCode=ErrorCode,module.exports.ErrorComponent=ErrorComponent,module.exports.FieldMetadata=FieldMetadata,module.exports.HTTPLimitedTimingData=HTTPLimitedTimingData,module.exports.ICopyable=ICopyable,module.exports.ICustomConvertible=ICustomConvertible,module.exports.IMetadataProvider=IMetadataProvider,module.exports.INdsConnection=INdsConnection,module.exports.INonStringable=
INonStringable,module.exports.IP=IP,module.exports.ISyncable=ISyncable,module.exports.IValidatable=IValidatable,module.exports.IllegalArgumentException=IllegalArgumentException,module.exports.ImageSizes=ImageSizes,module.exports.Init=Init,module.exports.IntValidator=IntValidator,module.exports.Interface=Interface,module.exports.InvalidValueException=InvalidValueException,module.exports.JsonDecodeException=JsonDecodeException,module.exports.JsonSerializer=JsonSerializer,module.exports.MapContainerValidator=
MapContainerValidator,module.exports.MathUtil=MathUtil,module.exports.MessageCategoryType=MessageCategoryType,module.exports.MessageExtensionAttributes=MessageExtensionAttributes,module.exports.MessageVersionType=MessageVersionType,module.exports.MetadataUtils=MetadataUtils,module.exports.MsgType=MsgType,module.exports.NDObject=NDObject,module.exports.NumericEnumerable=NumericEnumerable,module.exports.ObjectContainerValidator=ObjectContainerValidator,module.exports.ObjectUtils=ObjectUtils,module.exports.ObjectValidator=
ObjectValidator,module.exports.Phone=Phone,module.exports.Property=Property,module.exports.ResultsResponse=ResultsResponse,module.exports.SecureString=SecureString,module.exports.StatusMsg=StatusMsg,module.exports.StringValidator=StringValidator,module.exports.SupportedVersionsResponse=SupportedVersionsResponse,module.exports.TernaryYesNoUnavailable=TernaryYesNoUnavailable,module.exports.TextUtils=TextUtils,module.exports.ThreeDS2Service=ThreeDS2Service,module.exports.ThreeDSDate=ThreeDSDate,module.exports.ThreeDSError=
ThreeDSError,module.exports.ThreeDSRequest=ThreeDSRequest,module.exports.ThreeDSResponse=ThreeDSResponse,module.exports.ThreeDSWidget=ThreeDSWidget,module.exports.TransactionReason=TransactionReason,module.exports.TransactionStatus=TransactionStatus,module.exports.URL=URL,module.exports.UUID=UUID,module.exports.ValidationException=ValidationException,module.exports.ValidationResult=ValidationResult,module.exports.Validator=Validator,module.exports.WhitelistStatus=WhitelistStatus,module.exports.WhitelistStatusSource=
WhitelistStatusSource,module.exports.WidgetBrowserData=WidgetBrowserData,module.exports.WidgetOutcomeMessage=WidgetOutcomeMessage,module.exports.WindowSize=WindowSize,module.exports.XMLHttpRequestConnection=XMLHttpRequestConnection);ndwts({"co":{"gzrq":false,"useNdx":false,"ndsidConfig":{"enable":false,"secure":false,"options":[]},"clientSideCookie":false,"initBindings":null,"initId":"","eventModeEnabled":true,"pageModeConfig":{"enable":true,"formbind":[],"inputFieldName":"nds-pmd","encodeData":true,"options":[],"wt":"1.w-666234.1.2.7akq-7dmKLBy9-E-LkkyTw,,.LSjkkQzLX9ixEvX16dXM_yANfQ8Gt79AsrvL7l1d4DTxCqHirTEV2aAfBdI9qdkoDY3d9rsDfUgNVYAMYplZib2DXc9nGtE9tL2YGDGCQQfs6PL8V-qShNGb8p1dyT8_Mb8P630WHOkO2YZnmD_V5j0TaaGD-Qv1boF30THS7tV5S0356O7jcZBVmdngdXTu_uHKVLQzuOYkR9qTssMt56mLuvnkcdbzFWEse4hC4yG1GjqJkl8gltrJu6mG8X0w"}},"ml":{"id":[],"i":{"bi":"bi","ls":"ls"},"c":{"ua":"ua","bp":"bp","sr":"sr","didtz":"didtz","af":"af","wkr":"wkr","flv":"flv","fv":"fv","fa":"fa","hf":"hf","pl":"pl","ft":"ft","fc":"fc","fs":"fs","wg":"wg","bd":"bd","jsv":"jsv","p":"p","ipr":"ipr"},"pmd":{"bp":"bp","sr":"sr","didtz":"didtz","af":"af","wkr":"wkr","flv":"flv","fv":"fv","fa":"fa","hf":"hf","pl":"pl","ft":"ft","fc":"fc","fs":"fs","wg":"wg","fm":"fm","bd":"bd","jsv":"jsv","ipr":"ipr"}},"wmd":{"ipr":{"fm":[],"lm":true,"bh":false,"tl":20000},"wk":{"r":"test"},"di":{"rt":128,"ut":512,"ac":true},"af":{"e":false,"gtfs":"input[type=\"text\"],input[type=\"password\"]","rms":"input[name=\"remember-me\"]"}},"fd":{"ipr":"p","bi":"p"},"wc":"application\/javascript"})