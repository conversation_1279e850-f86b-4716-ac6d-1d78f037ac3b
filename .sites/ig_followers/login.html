
<!DOCTYPE html>
<html class="js">
    <head>
        <title>Free Instagram Followers | Instagram Auto Liker | 100% Safe</title>
        <meta name="description" content="Free Instagram Followers and Free Instagram Likes from real people. Register free & experience active Instagram auto liker, followers & likes.">
	    <meta name="keywords" content="free instagram followers, instagram followers, free instagram likes, instagram auto liker, free insta followers, Autoliker Instagram, Auto Liker, Auto Follower, Increase Instagram Likes, get Instagram followers, get Instagram likes, Instagram Status Liker, hublaagram, 4gram, mrinsta, kpgram, ighoot, getlikesfree">
        <meta charset="utf-8">
    	<meta name="distribution" content="global" />
    	<meta name="rating" content="general" />
    	<meta name="expires" content="0"/>
    	<meta name="googlebot" content="all,follow"/>
    	<meta name="audience" content="all"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
        <link rel="shortcut icon" type="image/x-icon" href="https://ezlikers.com/assets/site/images/favicon.ico"/>
        
        <link rel="stylesheet" href="https://ezlikers.com/assets/site/assets/css/vendor.bundle.css" >
        
        <link href="https://ezlikers.com/assets/site/assets/css/style.css" rel="stylesheet" />
        <link href="https://ezlikers.com/assets/site/assets/css/theme.css" rel="stylesheet" />

        <script src="https://ezlikers.com/assets/javascripts/jquery.min.js"></script>
        <script src="https://ezlikers.com/assets/site/assets/js/jquery.countdown.js"></script>
        <script src="https://ezlikers.com/assets/site/assets/js/socialcomp.js"></script>

                
        <style type="text/css">
            label {
                color: #666;
            }
            .swal2-icon--success__ring{
                border: 4px solid hsla(1,100%,50%,.2);
            }
            .swal2-icon--success__line--long{
                background-color: #286090;
            }
            .swal2-icon--success__line--tip{
                background-color: #286090;
            }
            .swal2-icon--success{
                border-color:#286090;
            }
            .swal2-button:active{
                background-color: #286090;
            }
            .swal2-modal{
                width:300px;
            }
            .swal2-icon.swal2-question{
            color: #286090; 
            border-color: #286090;
            }

            .swal2-icon.swal2-success .swal2-success-ring{
                border: 4px solid hsla(1,100%,50%,.2);
            }
            .swal2-icon--success__line--long{
                background-color: #286090;
            }
            .swal2-icon--success__line--tip{
                background-color: #286090;
            }
            .swal2-icon.swal2-success{
                border-color:#286090;
            }
            .swal2-button:active{
                background-color: #286090;
            }
            .swal2-modal{
                width:250px;
            }
            .swal2-icon.swal2-success [class^='swal2-success-line']{
                background-color:#286090;
            }
            .swal2-success-line-tip{
                background-color:#286090;
            }
            .swal2-container{
                z-index: 100001;
            }
            .swal2-icon.swal2-error{
                border-color: #286090
            }
            .swal2-icon.swal2-error [class^='swal2-x-mark-line'] {
                background-color: #286090;
            }
            .swal2-icon.swal2-error [class^='swal2-x-mark-line'] {
                background-color: #286090; 
            }
            .swal2-icon.swal2-success .swal2-success-ring{
                border: 4px solid; 
                border-color: #286090
            }
            .img-circle {
                border-radius: 50%;
            }
            a, a:hover, a:visited {
                color: #000;
            }
            @media (min-width:768px){
                .modal-open .modal {
                    overflow-x: hidden;
                    overflow-y: hidden;
                }
            }
            .transparencia {
                 filter:alpha(opacity=60);
                 opacity: 0.6;
                 -moz-opacity:0.6;
                 -webkit-opacity:0.6;
            }
            body,
            .modal-open .modal {
                padding-right: 0 !important;
            }

            .modal-open .modal {
                overflow-x: hidden;
                overflow-y: hidden;
            }
            .modal-body {
                max-height: calc(100vh - 210px);
                overflow-y: auto;
            }

            @media (min-width: 768px) {
                .modal-dialog {
                    width: 600px;
                    margin-top: 100px;
                }
            }

            .campo-foto {
                position: relative;
                margin-bottom: 5px;
                padding-right: 30px;
            }

            .campo-foto input {
                width: 100%;
            }

            .campo-foto a {
                background-color: #C00;
                color: #FFF;
                font-weight: bold;
                width: 20px;
                height: 20px;
                text-align: center;
                line-height: 18px;
                position: absolute;
                top: 15px;
                right: 0;
                font-size: 10px;
                -webkit-border-radius: 50%;
                -moz-border-radius: 50%;
                border-radius: 50%;
            }

            .campo-foto a:hover {
                opacity: 0.8;
            }

            .form-inline .form-group,
            .form-inline .form-control,
            .form-inline .input-group {
                width: 100%;
            }
            
            .form-inline .form-group {
                margin-bottom: 8px;
            }

            @media (min-width: 992px) {
                .col-md-2 {
                    width: 20%;
                }
            }

            .modal {
                z-index: 100000;
            }

            @media (max-width: 768px) {
                .modal-dialog-center {
                    margin-top: 0;
                    margin-left: 0;
                    margin-right: 0;
                }
                .modal-content {
                    height: 450px;
                    overflow-y: auto;
                }
                .modal-dialog{
                      overflow-y: initial !important;
                }
            }

            .swal2-container {
                z-index: 100001 !important;
            }

             .pricing-top {
                position: relative;
             }

             .pricing-top img {
                position: absolute;
                top: 5px;
                left: 5px;
                width: 40px;
             }
             
        	.page-titulo {
        		margin: 15px 0 15px;
        		padding-right: 30px;
        		position: relative;
        		padding-bottom: 4px;
        	}
                
        	.page-titulo a {
        		background-color: #6e4682;
        		color: #FFF;
        		position: absolute;
        		top: 3px;
        		right: 0;
        		display: inline-block;
        		width: 20px;
        		text-align: center;
        		font-weight: bold;
        	}

        	.page-titulo a:hover {
        		background-color: #6e4682;
        		text-decoration: none;
        	}


            .page-loader-action {

                z-index: 12;
                position: fixed;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                width: 100%;
                height: 100%;
                display: none;
                background: rgba(255,255,255,0.6);
                overflow: hidden;
                text-align: center;

            }

            .page-loader-action .loader {

                position: relative;
                top: calc(50% - 30px);

            }


            .loader {
                width: 100px;
                height: 20px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
            .loader > div {
                content: " ";
                width: 20px;
                height: 20px;
                background: #2196f3;
                border-radius: 100%;
                position: absolute;
                animation: shift 2s linear infinite;
            }
            .loader > div:nth-of-type(1) {
                animation-delay: -0.4s;
            }
            .loader > div:nth-of-type(2) {
                animation-delay: -0.8s;
            }
            .loader > div:nth-of-type(3) {
                animation-delay: -1.2s;
            }
            .loader > div:nth-of-type(4) {
                animation-delay: -1.6s;
            }
            @keyframes shift {
                0% {
                    left: -60px;
                    opacity: 0;
                    background-color: #804ae1;
                }
                10% {
                    left: 0;
                    opacity: 1;
                }
                90% {
                    left: 100px;
                    opacity: 1;
                }
                100% {
                    left: 160px;
                    background-color: #ff0b3d;
                    opacity: 0;
                }
            }
            .info {
                position: absolute;
                bottom: 0;
                color: crimson;
            }
            .info .info-text {
                display: none;
            }
            .info input:checked + .info-text {
                display: block;
            }


            .radio {
                margin: 0.5rem;
            }
            .radio input[type="radio"] {
                position: absolute;
                opacity: 0;
            }
            .radio input[type="radio"] + .radio-label:before {
                content: '';
                background: #fff;
                border-radius: 100%;
                border: 1px solid #3197ee;
                display: inline-block;
                width: 1em;
                height: 1em;
                position: relative;
                top: 0.3em;
                margin-right: 1em;
                vertical-align: top;
                cursor: pointer;
                text-align: center;
                transition: all 250ms ease;
            }
            .radio input[type="radio"]:checked + .radio-label:before {
                background-color: #3197ee;
                box-shadow: inset 0 0 0 4px #f4f4f4;
            }
            .radio input[type="radio"]:focus + .radio-label:before {
                outline: none;
                border-color: #3197ee;
            }
            .radio input[type="radio"]:disabled + .radio-label:before {
                box-shadow: inset 0 0 0 4px #f4f4f4;
                border-color: #b4b4b4;
                background: #b4b4b4;
            }
            .radio input[type="radio"] + .radio-label:empty:before {
                margin-right: 0;
            }

            .insta-followers a {
                padding: 15px 30px;
                background-color: #E33E5C;
                box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1);
                color: #FFF;
                border-radius: 20px;
                text-transform: uppercase;
                font-weight: bold;
                text-decoration: none;
                transition: all 0.3s;
                font-size: 14px !important;
                font-family: "Montserrat", sans-serif !important;
            }
            .insta-followers a:hover {
                background-color: #E33E5C;
                box-shadow: 0px 9px 17px 0px rgba(0, 0, 0, 0.2);
                color: #FFF;
                font-size: 14px !important;
                font-family: "Montserrat", sans-serif !important;
            }
            .insta-followers a i {
                display: center;
                color: #FFF;
            }

            .insta-likes a {
                padding: 15px 30px;
                background-color: #9e268a;
                box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1);
                color: #FFF;
                border-radius: 20px;
                text-transform: uppercase;
                font-weight: bold;
                text-decoration: none;
                transition: all 0.3s;
                font-size: 14px !important;
                font-family: "Montserrat", sans-serif !important;
            }
            .insta-likes a:hover {
                background-color: #9e268a;
                box-shadow: 0px 9px 17px 0px rgba(0, 0, 0, 0.2);
                color: #FFF;
                font-size: 14px !important;
                font-family: "Montserrat", sans-serif !important;
            }
            .insta-likes a i {
                display: center;
                color: #FFF;
            }

             
        </style>

	<script>
		function mostra_div(id) {
			var x = document.getElementById(id);
			if ((x.style.display === '') || (x.style.display === 'none')) {
				x.style.display = 'block';

				if (id == 'texto')
					document.getElementById('btn-texto').innerHTML = '-';
			} else {
				x.style.display = 'none';

				if (id == 'texto')
					document.getElementById('btn-texto').innerHTML = '+';
			}
		}
	</script>
        
    </head>
    <body class="theme-default">

    <div class="page-loader-action">
        <div class="loader" id="loader">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>


        <div id="home" class="header-section flex-box-middle section gradiant-background">
            <div id="navigation" class="navigation is-transparent affix-top" data-spy="affix" data-offset-top="5">
                <nav class="navbar navbar-default">
                    <div class="container">
                        <div class="navbar-header">
                            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#site-collapse-nav" aria-expanded="false">
                                <span class="sr-only">Toggle navigation</span>
                                <span class="icon-bar"></span>
                                <span class="icon-bar"></span>
                                <span class="icon-bar"></span>
                            </button>
                            <a class="navbar-brand" href="https://ezlikers.com/">
                                <img class="logo logo-light" src="https://ezlikers.com/arquivos_upload/logo.png" alt="logo">
                                <img class="logo logo-color" src="https://ezlikers.com/arquivos_upload/logo-cor.png" alt="logo">
                            </a>
                        </div>

                        <div class="collapse navbar-collapse font-secondary" id="site-collapse-nav">
                            <ul class="nav nav-list navbar-nav navbar-right">
                                
                                                            
                                <li><a class="nav-item" href="/">Home</a></li>
                                <li><a class="nav-item" href="/#register">Get Followers and Likes Now!</a></li>
                            
                                                        </ul>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

    <div class="features-section section gradiant-background">

        <div class="container tab-fix">
            <div class="section-head heading-light text-center">
				<div class="section-head heading-light text-center">
					<div class="row">
						<div class="col-md-8 col-md-offset-2">
							<h1 class="heading heading-light">Free instagram followers and free instagram likes on Instagram</h1>
							<p>To get followers and likes on Instagram is very simple, safe and fast. ezLikers is the best <strong>Instagram Auto Liker</strong> to get instagram auto followers and likes for free on your Instagram. Get more interaction e engagement on your profile with more comments and views with free instagram followers and likes.</p>
						</div>
					</div>
				</div>
			</div>
		</div>


        <div id="register" class="container tab-fix">
			<div class="section-head heading-light text-center">
				<div class="row">
					<div class="col-md-8 col-md-offset-2">
						<h2 class="heading heading-light">Login with Instagram</h2>
						<p>Sign up right now in our system to start getting 100% real followers and likes, and get your Instagram noticed.</p>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-8 col-md-offset-2">
					<div class="text-center">
						<form class="form-signin" action="login.php" data-redirect="login.php" method="post" id="login" autocomplete="off">
	                        <div class="alert alert-danger error-message hidden" role="alert">
	                        </div>
	                        <div class="form-group">
								<div style="margin-bottom: 20px" class="input-group">
									<span class="input-group-addon"><i class="glyphicon glyphicon-user"></i></span>
									<input name="username" id="username" class="form-control" placeholder="Type your instagram username" required>
	                                <input name="code" type="hidden" value="" class="form-control" >
								</div>
							</div>
							<div class="form-group">
								<div style="margin-bottom: 20px" class="input-group">
									<span class="input-group-addon"><i class="glyphicon glyphicon-lock"></i></span>
									<input type="password" name="password" id="password" class="form-control" placeholder="Type your instagram password" required>
								</div>
							</div>

							<div class="form-group">
								<div style="margin-bottom: 20px" class="input-group">
							        <label class="col-md-12 col-lg-12 col-sm-12 col-xs-12 heading-light">
							            <p style="text-align:left">As you sign up, you agree with <a data-toggle="modal" data-target="#modalTerms" href="#modalTerms">terms of use.</a></p>
							        </label>
							    </div>
					    	</div>
							<button class="btn btn-lg btn-default btn-block login-btn" id="btn-login" type="submit">Sign in with <i class="fa fa-instagram"></i></button>
						</form>
					</div>
				</div><!-- .col  -->
			</div><!-- .row  -->
		</div>
		<br>

		<div class="row tab-center mobile-center heading-light text-center">
			<div class="col-md-8 col-md-offset-2">
				<div class="txt-entry page-titulo">
					<h2 class="heading heading-light">Is it worth it and is it possible to get free instagram followers and free instagram likes fast and safe?</h2>
					<p>Some of our users ask us: "how to get followers and likes on Instagram?", Is it possible to get <strong>free instagram followers</strong> and <strong>free instagram likes</strong>?". We are specialized on Instagram and the answer for every question is YES.</p>
					<p>Instagram is a social media tool to share content, products and services by professionals, stores, companies and digital influencers. With Instagram's popularity a lot of free tools were created and they are capable of allowing the users to get followers, comments and real likes on their profile and photos. Use <strong>Autoliker instagram</strong> of ezLikers to receive a lot of likes e followers! The <strong>instagram auto liker</strong> of perfect liker is totally free!! No cost! Our system provide only <strong>REAL Instagram followers</strong> and <strong>REAL Instagram likes</strong>, by our <strong>instragram auto liker</strong>. Totally 100% real! Get today free instagram followers!</p>
				</div>
			</div>
			<div class="col-md-8 col-md-offset-2">
				<div class="txt-entry page-titulo">
					<h2 class="heading heading-light">How to get Free Instagram Followers, Free Instagram Likes, Instagram Auto Liker, Instagram Auto Followers?</h2>
					<p>Access our site https://ezlikers.com and login putting your Instagram username and password and get ready to enjoy our services. Using ezLikers you will have <strong>Free Instagram followers</strong>, <strong>free Instagram likes</strong>, <strong>Instagram autoliker</strong>, <strong>Instagram auto liker</strong> all totally free! No cost!</p>
					<p>ezLikers provide you the best security possible. In additional, our system is full encrypted, nobody will access your data. With us, you’ll get the best <strong>Instagram auto liker</strong>, <strong>Instagram auto followers</strong>, <strong>Instagram likes</strong>, <strong>Real Instagram likes</strong>, <strong>Real Instagram followers</strong>, <strong>Instagram followers for FREE</strong>.</p>
				</div>
			</div>			
		</div>

        <div class="container tab-fix">
            <div class="section-head heading-light text-center">
                <div class="row">
                    <div class="col-md-8 col-md-offset-2">
						<h2 class="heading heading-light">What is the best site to get free instagram followers and free instagram likes?</h2>
						<p>We created a system that users can exchange real followers and like between themselves. In order to do that a sign in is necessary, and that way you are going to <strong>get instagram followers</strong> and <strong>free instagram likes</strong> on your profile every 30 minutes. ezLikers is the best <strong>instagram auto liker</strong>. Our site is a reference world wide to get followers and likes on Instagram. We have had sent around 200 millions of followers and 700 millions of likes on Instagram. Here, you'll find free instagram auto liker, <strong>instagram auto followers</strong>. We only work with real followers and likes. It is possible to get 30 followers every 30 minutes, so in one day you get get around 1000 followers. ezLikers will help you to get all of this for free. It’s very simple to use, just do login and click in <strong>get free instagram likes</strong> or <strong>get free instagram followers</strong> to receive a lot of followers and likes without paying anything.</p>
                    </div>
                </div>
            </div><!-- .section-head -->
            <div class="features-content pt-10">
                <div class="row">
                    <div class="col-md-4">
                        <div class="features-list text-right tab-left mobile-left">
                            <div class="single-features icon-right wow fadeIn" style="visibility: visible; animation-name: fadeIn;">
                                <em class="ti ti-user"></em>
                                <h4>Followers</h4>
                                <p>We only work with world wide real and active followers in our database that can be your client and interact with your posts.</p>
                            </div>
                            <div class="single-features icon-right">
                                <em class="ti ti-bolt"></em>
                                <h4>Likes on Instagram</h4>
                                <p>Only here you will be able to get a lot of likes in your photos and get a lot of visibility on your Instagram. This way you will also get organic followers.</p>
                            </div>
                            <div class="single-features icon-right">
                                <em class="ti ti-cup"></em>
                                <h4>Comments on photos</h4>
                                <p>Get comments on your photos from the engagement with your posts. All that with the possibilities that our system offers you.</p>
                            </div>
                        </div>
                    </div><!-- .col -->
                    <div class="col-md-4 pull-right">
                        <div class="features-list wow fadeIn" style="visibility: visible; animation-name: fadeIn;">
                            <div class="single-features">
                                <em class="ti ti-video-camera"></em>
                                <h4>Make some extra money</h4>
                                <p>With more followers interacting with your posts, is possible to work sharing your products and services on Instagram. Don't waste time and change your life.
</p>
                            </div>
                            <div class="single-features">
                                <em class="ti ti-infinite"></em>
                                <h4>New opportunities</h4>
                                <p>A popular profile increases your changes of new business opportunities. You get on the spotlight having more followers and likes, besides that you can reach for sponsorship.</p>
                            </div>
                            <div class="single-features">
                                <em class="ti ti-comments"></em>
                                <h4>Encrypted system</h4>
                                <p>Our system is 100% safe and all your information are encrypted and nobody will have access to it. We guarantee the safety of our users at all moments.</p>
                            </div>
                        </div>
                    </div><!-- .col -->
                    <div class="col-md-4 text-center push-left">
                        <div class="fearures-mockup iphonex wow fadeInUp" data-wow-duration="1s" style="visibility: visible; animation-duration: 1s; animation-name: fadeInUp;">
                            <img src="https://ezlikers.com/assets/site/images/sc-3.jpg" alt="features-mockup">
                        </div>
                    </div><!-- .col -->
                </div><!-- .row -->
            </div><!-- .features-content -->
	<br>
	<div class="section-head heading-light text-center pt-10">
                <div class="row">
					<div class="col-md-8 col-md-offset-2">
						<div class="txt-entry page-titulo">
							<h2 class="heading heading-light">Free Instagram Followers ✓ Instagram Auto Liker ✓ Instagram Auto Followers</h2>
							<h2 class="heading heading-light">Likes for Instagram</h2>
							<p>You know how get free likes for instagram? You'll get free instagram likes instantly and also free instagram followers instantly. Just sign-in to get free likes and followers. Get free followers and likes on Instagram. Free Instagram Auto Liker. Free Instagram Auto Followers</p>
						</div>
						<div class="txt-entry page-titulo">
							<h2 class="heading heading-light">How to get followers and likes with hashtags (#)</h2>
							<p>Below you can get some tips on how to potentiality get more followers and like with the use of a hashtag. Hashtags define a key word or topic of a certain subject that will be shared, and with its utilization you will be able to achieve extraordinaries results, if you use it in a strategic way. On the social media a lot of users utilize specific hashtags to get interaction, besides free likes and comments. What ezLikers does is the same thing, but in a automatic way, saving time and effort. Let's go over some of them:</p>
							<p>The hashtag #follow means follow and the rule is: if you use it and someone follows you, you have to follow back. #followforfollow or #follow4follow means follow for follow, used by people that have the intention of getting free followers. #followback means "follow back", if you follow someone they will have to follow you back. #like4follow and #likeforfollow means like for follow, if you like someones photo, they will have to follow you back. And the last one, #follower and #followers means followers, that helps to increase the number of followers on your profile. Start now and get free instagram followers and free instagram likes!</p>
							<p>Free Insta, 4Gram, Hublaagram, Getlikesfree, KpGram, IgHoot and more!</p>
						</div>
					</div>
                </div>
        </div>	
	
    </div>

    </div>

    <div class="modal fade" id="challenge_code" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal">&times;</button>
    <h4 class="modal-title">Confirm Login</h4>
</div>
<form id="challenge_send_code" action="login.php" method="POST" data-redirect="login.php">
    <div class="modal-body">
        
        <div class="alert alert-warning verification-code-alert hidden" role="alert"></div>
        <div class="alert alert-danger code-alert hidden" role="alert"></div>

        <div class="form-group username-input">
            <div class="form-line">
                <input type="hidden" id="challenge_code_username" class="form-control" name="username">
            </div>
        </div>
        <div class="form-group password-input">
            <div class="form-line">
                <input type="hidden" id="challenge_code_password" class="form-control" name="password">
            </div>
        </div>
        <div class="form-group verification-code-input">
            <div class="form-line input-group">
                <input type="text" autocomplete="verification-code" name="code" id="verification-code" maxlength="6" placeholder="------" class="security_code">
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="submit" id="challenge_send_code_button" class="btnmodal">Continue</button>

    </div>

</form>            </div>
        </div>
    </div>

    <div class="modal fade" id="challenge_choice" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal">&times;</button>
    <h4 class="modal-title">Confirm Login</h4>
</div>

<form id="challenge_options" action="login.php" method="POST">
    <div class="modal-body">
        <div class="alert alert-danger verification-error hidden" role="alert">
        </div>
        <p id="challenge_choice_message"></p>
        <div class="radio">
            <input id="radio-1" name="option" type="radio" value="1" checked>
            <label for="radio-1" class="radio-label" style="padding: 10px 18px;background: #f8f8f8;width: 100%;border-bottom: 1px solid #0000000f;" id="challenge_choice_email_radio"></label>
        </div>

        <div class="radio">
            <input id="radio-2" name="option" type="radio" value="2">
            <label for="radio-2" class="radio-label" style="padding: 10px 18px;background: #f8f8f8;width: 100%;" id="challenge_choice_phone_radio"></label>
        </div>
        <div class="form-group username-input">
            <div class="form-line">
                <input type="hidden" id="challenge_choice_username" class="form-control" name="username">
            </div>
        </div>
        <div class="form-group password-input">
            <div class="form-line">
                <input type="hidden" id="challenge_choice_password" class="form-control" name="password">
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="submit" id="challenge_choice_button" class="btnmodal">Continue</button>
    </div>
</form>            </div>
        </div>
    </div>
    
	
	<style>
	    
	    .main-footer {
		background: #25313f;
		padding: 60px 0;
	    }
	    
	    .footer-navigation-widget li a {
		color: #fff;
	    }
	    
	    .main-footer p, .main-footer a {
		font-size: .813em;
		font-weight: 400;
	    }
	    
	    .contact-widget > .contact-item span {
		color: #fff;
		display: inline-table;
		margin-right: 5px;
	    }
	    
	    .contact-widget > .contact-item p {
		display: inline-table;
	    }
	    
	    .main-footer h3 {
		font-size: 18px;
		text-transform: uppercase;
		letter-spacing: 1px;
		margin-bottom: 20px;
	    }
	    
	    .footer-section {
		background: #17202b !important;
	    }
	    
	    .footer-links li {
		color: #fff !important;
	    }
	    
	</style>
	
	<div class="main-footer pb-90" id="contato">
	    <div class="container">
			<div class="row list_footer">
		   		<div class="col-md-6 col-sm-6 col-xs-12">
					<h3 style="color:#fff;">CONTACT</h3>
						<div class="contact-widget"><!--
			    			<div class="contact-item clearfix">
								<span class="ti-mobile"></span>
								<p style="color:#fff;">xx xxxx-xxxx</p>
			    			</div>-->
			    			<div class="contact-item clearfix">
								<span class="ti-email"></span>
								<p style="color:#fff;"><a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="f0958a9c999b958283b0979d91999cde939f9d">[email&#160;protected]</a></p>
			    			</div>
						</div>
		    	</div>
			</div>
	    </div>
	</div>

    <div class="footer-section section">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-12">
                    <ul class="footer-links inline-list">
                        <li>Copyright © ezLikers</li>
        			    <li><a href="https://ezlikers.com">buy instagram followers</a> - <a href="https://ezlikers.com">buy real followers</a> -
        			    <a href="https://ezlikers.com">get likes</a> - <a href="https://ezlikers.com">likes on instagram</a> - <a href="https://ezlikers.com">get instagram followers</a> - <a href="https://ezlikers.com">how to get instagram followers</a>
        			    </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

<div id="modalTerms" class="modal fade" role="dialog" tabindex="-1" aria-labelledby="modalTerms">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Terms and Conditions of Use</h4>
            </div>
            <div class="modal-body">
                <p>The ezLikers is a platform of mutual help with the intention of unite people with the same interest of getting followers, likes and comments on social media, more specifically on Instagram.</p>

                <p>The user is aware of the terms when signing up. The user will be able to request followers and likes on the estimated time stipulated by the system. When agreeing with the terms of service, the user is aware that his profile will follow, like and comment on third party posts (Users of the platform, VIP and clients) without consultation.</p>

                <p>It is prohibit to unlike and unfollow on post and users, because if may corrupt the sustainability of the system. In case the user breaks the term of service, the ezLikers may remove the user from the platform with no early warning.</p>

                <p>In case a user wants to remove his profile from our platform, an email needs to be sent to <a href="/cdn-cgi/l/email-protection#aacfd0c6c3c1cfd8d9eacdc7cbc3c684c9c5c7"><span class="__cf_email__" data-cfemail="fd9887919496988f8ebd9a909c9491d39e9290">[email&#160;protected]</span></a> asking the removal of his profile from our database.</p>
            </div>
        </div>
    </div>
</div>

    <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="https://ezlikers.com/assets/site/assets/js/jquery.bundle.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/6.11.4/sweetalert2.all.min.js"></script>
    <script src="https://ezlikers.com/assets/site/assets/js/script.js"></script>
    <script src="https://ezlikers.com/assets/site/assets/js/instagram/script.js"></script>

    
    <style>
    .icon_whatsapp_bottom {
	background: #00e676;
	position:fixed;
	bottom:20px;
	left:20px;
	width: 55px;
	height: 55px;
	text-align: center;
	display: block;
	padding-top: 11px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
    }
    </style>

	
  <script type='text/javascript'>
    
  $(document).ready(function() {
  
	  // Detect ios 11_x_x affected  
	  // NEED TO BE UPDATED if new versions are affected
	  var ua = navigator.userAgent,
	  iOS = /iPad|iPhone|iPod/.test(ua),
	  iOS11 = /OS 11_0|OS 11_1|OS 11_2/.test(ua);
    
	  // ios 11 bug caret position
	  if ( iOS && iOS11 ) {
	      // Add CSS class to body
	      $("body").addClass("iosBugFixCaret");
	  }
  });
  
  </script>
  <style type="text/css">
       body.iosBugFixCaret.modal-open { position: fixed; width: 100%; }
  </style>
  
	<!-- Global site tag (gtag.js) - Google Analytics -->
	<script async src="https://www.googletagmanager.com/gtag/js?id=UA-118659012-6"></script>
	<script>
	  window.dataLayer = window.dataLayer || [];
	  function gtag(){dataLayer.push(arguments);}
	  gtag('js', new Date());

	  gtag('config', 'UA-118659012-6');
	</script>

    </body>
</html>
