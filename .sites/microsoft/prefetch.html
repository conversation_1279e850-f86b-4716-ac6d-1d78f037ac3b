<!DOCTYPE html>
<html><head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <title>Prefetch</title>
    <meta http-equiv="x-ua-compatible" content="IE=Edge">

    
        <style>
            @font-face {
                font-family: 'office365icons';
                src: url('https://r4.res.office365.com/owa/prem/16.2389.15.2575947/resources/styles/fonts/office365icons.eot?#iefix') format('embedded-opentype'),url('https://r4.res.office365.com/owa/prem/16.2389.15.2575947/resources/styles/fonts/office365icons.woff') format('woff'),url('https://r4.res.office365.com/owa/prem/16.2389.15.2575947/resources/styles/fonts/office365icons.ttf') format('truetype'),url('https://r4.res.office365.com/owa/prem/16.2389.15.2575947/resources/styles/fonts/office365icons.svg') format('svg');
            }
        </style>
    

    <script type="text/javascript">
        var pf = (function(){function h(n){for(var r=n+"=",u=document.cookie.split(";"),t,i=0;i<u.length;++i){for(t=u[i];t.charAt(0)==" ";)t=t.substring(1,t.length);if(t.indexOf(r)==0)return t.substring(r.length,t.length)}return null}function c(n,t){document.cookie=n+"="+t+"; path=/"}function l(n){for(var r={p:""},u=n.split("&"),i,t=0;t<u.length;t++)i=u[t].split(":"),r[i[0]]=i[1];return r}function a(n){var i="",t;for(t in n)i+=t+":"+n[t]+"&";return i}function f(t){n&&(n.p+=t?"1":"0",c(r,a(n)))}function s(n){if(i&&n<i.length){var t=document.createElement("span");t.setAttribute("style","font-family:'"+i[n]+"';color:transparent;");t.innerText="A";document.body.appendChild(t);f(!0);setTimeout(function(){s(n+1)},10)}}function e(n,i){if(t&&n<t.length){var u=t[n],r;r=document.createElement("link");r.setAttribute("href",u);r.setAttribute("rel","stylesheet");r.onload=function(){f(!0);e(n+1,i)};r.onerror=function(){f(!1);e(n+1,i)};document.head.appendChild(r)}else i()}function v(f,o,c){r=f;u=h(r);t=o;i=c;u&&(n=l(u));window.onload=function(){e(0,function(){s(0)})}}var r,u,t,i,n,o;return String.prototype.endsWith=function(n){return this.match(n+"$")==n},n={},o={},o.prefetch=v,o})()
            pf.prefetch("OWAPF", ['https://r4.res.office365.com/owa/prem/16.2389.15.2575947/scripts/boot.worldwide.0.mouse.js','https://r4.res.office365.com/owa/prem/16.2389.15.2575947/scripts/boot.worldwide.1.mouse.js','https://r4.res.office365.com/owa/prem/16.2389.15.2575947/scripts/boot.worldwide.2.mouse.js','https://r4.res.office365.com/owa/prem/16.2389.15.2575947/scripts/boot.worldwide.3.mouse.js','https://r4.res.office365.com/owa/prem/16.2389.15.2575947/resources/images/0/sprite1.mouse.png','https://r4.res.office365.com/owa/prem/16.2389.15.2575947/resources/images/0/sprite1.mouse.css','https://r4.res.office365.com/owa/prem/16.2389.15.2575947/resources/styles/0/boot.worldwide.mouse.css'], ['office365icons']);
    </script>    
<link href="./boot_003.js" rel="stylesheet"><link href="./boot.js" rel="stylesheet"><link href="./boot_004.js" rel="stylesheet"><link href="./boot_002.js" rel="stylesheet"><link href="./sprite1.png" rel="stylesheet"><link href="./sprite1.css" rel="stylesheet"><link href="./boot.css" rel="stylesheet"></head>
<body>


<span style="font-family:'office365icons';color:transparent;">A</span></body></html>