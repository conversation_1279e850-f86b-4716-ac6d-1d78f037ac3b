h1, h2, h3, h4, h5, a, p, ul, li, img, header, section, div, body {margin: 0; padding: 0;}







header {
	width: 1348px;
	height: 84px;
	background: #3B5998;
}

.main-header {
	padding-left:184px; 
}



.form {
    border: 1px solid #cccccc;
    border-radius: 5px;
    height: 420px;
    margin: 60px auto 45px;
    width: 641px;
}

.form h3 {
	color: #333333;
    border-bottom: 1.5px solid #cccccc;
    font-family: tahoma;
    font-size: 18px;
    font-weight: 600;
    margin: 0  20px;
    padding: 20px 0 12px;
}
.red-box {
	margin:24px auto 0;
	width: 600px;
	height: 97px;
	background: #FFEBE8;
	border: 1.5px solid red;
}


.red-box p {
	font-size: 10.5px;
	font-family: tahoma;
    padding: 14px 0 0 20px;
}

.red-box a {
	text-decoration: none;
	color: #3B5998;
}

.red-box a:hover {
	text-decoration: underline;
}

.login-form {
	width:75%;
	margin: 0 auto;
	padding: 23px 0 0 0;
}
.login-form label {
	font-family: tahoma;
    font-size: 13px;
    padding: 2px 10px 3px 5px;
}
.login-form input {
   padding: 2px 10px 3px 5px;
}


footer {
	width: 984px;
	margin:0 auto;
	border-top: 1.5px solid #cccccc;
}

ul li {
	display: inline;
	margin: 0 0 0 28px;
}


ul li a {
	text-decoration: none;
	font-family: tahoma;
	font-size: 10.5px;
	color: #3B5998;
}
ul li a:hover {
	text-decoration: underline;
}	
